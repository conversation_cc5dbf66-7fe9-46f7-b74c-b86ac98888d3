#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

iyvxulzk = b'c%0omYjfMUcHjLgSY|qwcC}hbagt5u>ULzu&ibwsyS9>To7v$|5+rd=5nMiOtC{@wo^!wl0D_bl$8+1-Gcifvyblfzp7Ayzj}j}n^@4;&mK($4!7}2T;**!vNN#)9hQ+bFVl)2Gvz9zade)r#JesdLk6Cn(S6xTUU3kGN|G?w?Dc%;3q3<S39&H4*1y5LTmlN~3yKuy(jG5#%Su-G?*O=nlVtSinyJxdm#P5-dgJ;&c_(-4|*9-dL_MmvlQFIP0_*t=lMQ*~JIN{N@r@ha(8!dWP81XrX?%a6`#(M-8vC#MCSaIp64~`pppc2=Y-V(lIz;sl8BEZSjmGfcp{<KF=pH8oD&M!aothc9UlaCiu=j`&s^q?yVamT$mvts76dE)WFy7v}<SBOJpPEqEFvRuGVSbCEe{x0A=39A!E?9OBN2M2TCjbrN*iuX<^fJaj@W2dlYUwr7kAu$$gX*rG;c!}e5fP-bvB8@r8He@|u0sI7Xxb@*h5DVN56K<0|&RyT1x%1l$BmTr`$f8cSUtsQ*ML?rplCZ`SJ++cV8)`{!YPFIps8znGl?ryDPVuZ($;E&g`Qt&Mq9_eWJCck^v@Kuo2TTpZdam#NH?uB77EI2qx$m(cQL0IeLQ)I<h{`};0C&LGoLjaX^SBQt$NGntSEnB)=g#En-1+VFKM%Xz1C52u>`Z7?DO9BF*jY{R2vN7+@7pRr^cmPu+fqlc+0!j)fc<PsR<r*=TWf}GW}ljElME-|$)yBTy+#ZwgNDNYgl1HciAqbs@KZ&zM2lGzJNDv<YSyshEW9XpeTB8pc2REsp}@K6!s8hnctq$X>;9kI1Ha4xdX`O<G=jv3gY}yr#k1gf6CP<el*p+nN@2ezQR|f~_w=UP)0(BGZB3;V&~lSu@SO8a=tGlO{ENnC*|YWIHNW?QCGXfb=^UB2LCShe5VNS(GWGo}qAl>UhdB!q>y-W=4D_z-O%qubMLg=*r}zh`F6~KR(Kn0F=V=tN#T)BOoJ1Yg{fd-C*ggS0o<*RH4s?@!ddk9$ZQJj`_acZb)>{^PE8rU{LmvV*<-`<}Bwm718aj$Qk`449EUbv9!2-ma^9Y2F0aabx-?%Z*=m|XI4?F;5p}W97V?2D>vwryD_TG(Fu@nP~R^dV?jnS1jazpTj5V)mwtORVmU&v`vwecaUpg$$i4@=_NUx|YPp~T5#v9P=VWX$16{5cPn-YSh4ra0*)&|*G)E9!IxJ;*@3)jAmuYiW>rEN_Wz0-ZfGgKi*{2y>?#7yt-T?=uPo3cQjUQHdapjtGCKt|RJ>t-@W34!okwZXI}k6?uyeq+H`cFH;dd#fdk+-Hs30y|BY}jJg>Px+)?~mMMu~R0s4Bd=<IjdTZdscW2Dc1i8THj>I82Bzs84nRiRF5w-r50E_|>S_pR#LSit~7_pz8O|e}-svC_D?++WrL2J9<von-x;i0i3Elh<j<yaZ-H~D*J2p(aaM7qCTUZ4LLq=b`;t{FpVhv6P}7&Y7B1i~t268E0(0}2-sMK^&+aBqzaVOCUjuy`z0V18V6)>_1aU5E#Hn0wDbjlpr6Bs}PVYQ$50q?=K>@TxVxgBHP-OXzC$3T5aywxD36UN;*E3R!Fuh$(j&+YqicZnT9;kbEcWtcoGzk!TVqh$g+6g2oM`@-)7Rt(hA`yx@gU3J6391@G7@xKiyfTRDUeNZZdw!`Cm*p4&+1*iV^KaVb^2Uwa9&)r<lL6nD&-tq9TZ^{ba}M}+9D8{LAQXF9$NV7W3NWWIs_n1K^4c1ZB*1^yD@cagjOb_re%@mH2$rQ>TY!NLtzENWu^?C9wE^Or>Rnk~Ma`?t?WXQMNj`$$9A%sjMKf@MoL^^^bo4kNV?Y1||d2#78i7dMcYMR9%hpyptr&8z(Y^&Iez`Z<pS*<ZB~bJB4~9>-I_?P(ev=StO}SL3CzK&S%q*%hT^<3gFHdP^QLwcb*z7lFI7CH5_67n7;R9giT!ia<6{i=YpA7!O}mK5oaqsNV!M{i`W2jbi9L?KNm_WUFO)yevM}W>In^*J7&|EIRhzPI1=i?{+p&aG*^x{L(d6zF_VhQ!4xTMN}?3i5yAROE4*{L({KD_Sy)X_ivCkb|jM)ZK7M(C@97l3Dt$lT2fiG>(+(4dQ!%iR!b*~OI<@?QZ!2%e>%-(VXIq-Ti;QoSx?lhbVc7wmswxY%{z-cC33ysckpC>Uppxmah~W|2!4aWihrsFeaeA5_VC1q6jF?PXItwXgxQ(hHPl}*4)HeH0?1%6cz87!*v8h2>C8PbYsDC`ag-JE*rTEGPu84T80I+fJYuXIa0wWPA_{PgMD(&XjlH1ol~g8?eg~ulR!GM;EQo=m)ICs1L7<e3rU4vLOr`R4qyQ=?o1sK;$Ec4J*Po|8j^PR|Bs^C`vSyiZ1%xPZ9&k=GNIEjtKaW6xvYvh(b#=I{3MV*|0rM}l#>)+Db%d%OtW5eJtp|rMmodY~ZT8scDogu|%<k8RFA5W1fBce5TRv{T*89~4e14N|$gvD`p%_a#uLm#)dN~-F?NN;Qq3PdRwB8_wjj@UngiAvQD>+oD(=F$FD`nc`FN{4J=s*r3Oz(zbbpf0rAFUAiY)+fTjQDK!EJ)?NfMsKc;=7W}4Nvj6$ib{K+LV5S({ob#H^E<9cy6>XL14lpC%E)O5WiyzN9AheBGMAT@c@4b;DQ_r`z001oufC@XQf$%QfSI(r25Y%_$vuc6`K74M_(0Bz}J2`19Vu-K1qQ)S9!M{Or5!sU`zEkKxdN}MxwaZQY3?=X?F5rKw0wA9_dt!q7Qp@333zc$(qe?XE}oJBINYiU3mOaIE_wHPE_r76?p>6te4SI&pMoOe{tCTPIBL*vt&2j;V<8dUS+1OhG31Lk8!y(9&OG%<}Y@T9(^-OqX)pKaK>l+p~;`KD##Hs_!xmVZm3{XGD-T;Nqp`c;tDPg{Ow^+itpV&48USlxfWUu#0~J0Br3cl*B?9d3Us6u$*NLMvHWowHPS8SSQ}^;Gq+kOk-D87RKf_t28-jF+eGK8tff+t?EqSZkE=Dk?B)RzCmITr=tRz7$e>DI8oM`HLa{zUJ1%fW7yd$COVc!qnv8?UA%If?=&KaOPPxQmj|TdnL`5K`@t2<O<CMqo<@8q=Wj=&Bi!+T<dHwL2H3>tp0#GI?4eKQup{@x?rT!n%RQyO@5>Y{SENGZVX2l9e&FY4T{gpM1CetPN{3o%ZVEOKrr)5?pzQ%18qk!!&OfQ>p8e}>f4(jmiwHDV5GTgCzcfpp4z7FWaHz!k%tO12m*HpcE>C6sdc5fHr8~M4W>{w$c!jCODlIxpT8n$9-*taHE=T@=Zhz1E|+8%litvrvV)oMv4`Qovy;E-B&j=<cYYIwStM_!m9UkV<SRZ&ome8&oC4qGa#;7VGuGMN;#XKL(4BhNACExcs>P1&$3Sxt9`BV{I2)gwl*gx?|dF@MOkk>?T;KX)z+s^alydle=>9oc^%c8CpQJMYSig|Fd@9Yl8cn8a=7X*&_pmWUwfn5}<~yM+|v*DJiJyQjWQYpo6X78{q^sJqxa=z6V`u2C{^+)C#<zGjuvj>A1wUew)F<)d~g7h8tfs9bEGqs=}DOIo&1eRjtJU?y&5293H&idJUO@im)a)E8ShF~h?hWw9EJ#ZIfe!m)}*3SuRW77<whRl;wHYexVs;Y)=|jC@lNhO93Rackc-&bAr+wj`Q+t_<)r@!2H`4sA6v`>)JZ;lUS>JjHW2WL9VR^3CfmI`9>blF0QEe7^ZBbtBfwk*l0MI*c1o?uaVng#VsGo||m<vXBDtG$H*jfxLxvUX2DJT46Z&MLdjt!+(!{Yh@6H?xVxIimllU$K@#TeHwf7y)2;vJx#EOf#?RYx3C88(`lu6mBynZx`wV<^`tuJU4abSOYJhio+^Ldwtq$34uqR>MFO7{X7G@LgZL5-7zVpGwp9?DxNF&OO2}fCbYR#mZFYfC_`ez#P*SnWcQ0M%mG{*1Q9C^^GVCo}dpN}Pury)qau$`6iQ!hN*U&XP_>HpLw@KSnb5B*jXgMlEjQi_Znes&a`X17tPIt`{u6#N10=99pmr3Aln6?AP23pW$k2$|34BmqqS(7L!7ynv~y-HC`>S#CYl5p*^BA)x0%6k)=O_^8U)1EKe+7nMEe4{-fn9;6I`~4QThtcCPTO?#T%Ra{?X%^FYjbA;Yq#N{OG;;&%f~^>2WrT?*p4egU`uKj92G*I28&WmM3l3OO5!OHWEhE?qZ^^8yh+9)HjD?GNK9700CeAMSUB#i!W8Vdr`c?-jbZ63kO9v`(<qq^h3Bw7uWaXHA>v=yLUDk|gXn<51)5vpN<)B8vBM)i{By9#Y1XLZ=6u5Q`Y6^I1P*XDUjf0w8#%w0d)E*1E%82~C4`?b($wwLKv;v($%r?+!YAKI4R;QX}0rt?ahUziH7}MB14>P7immMPqb&+S{`&`UD%-5Xaggf&o=v)Td2mJei-?=oNTYH-gi<B1LI3Xe0pz7NV6)|DT*io7*)iHJeA!GA3j*N{xK*i&v`cE@Ti-~|E4(wG^Rf(Z*NmVP@A3YwcVniDNKZ^e0-I%tXc|PtPn{BNNKCew+D^{q2J<TZ1&#>2Y$r4{hDhtlnDu-1`JbI9(p=-{5j`~)<$D*c_inHQ9`>6^xd8JcE)uOj5;3^%Kf^kPgRj`y=R+;+7p6d+28apn9co9i*T_N~)@59_X*Yhz7JGhS|2EA!9d+H@_a2ZgPzQ$8xcw&;CH=!FPRH8y$v1sE4pr7}&HJZYE$buU;h((A`xOi5<-Xnb$L4csNFP-0dkcGZWsl)g!VE1k{5&TC=y~RnyScthSy+A$0q1HT3gB4>U)t$NV2bPK#+N)gQ<=DNQxwj}=*#W8ThmPqQTf1h&tifw~Zqe{dN{^nOlu%IzSlG7ST~Cx<6FXeBgm{`hFXJEZJK1S5(0zVf&dkyYUEm>@0=%=;xY)hf!;>lYvjP1`N8!X)b%JwBin~~ObO%{t)VG-Cog<wVst+vk2H|}Sg#b^}9g--xExAn#`E0)!(5x~2`<(tA4O;b)8r?^Ck~+S!mz2%&!niU2GEIEks@$ppDL_v%yU7KK@6e2jhO<-}m8lsiME79m(X*opQff~bx3b}8K3-_jV7oR=N``rw`O;jb_6_SfwIy1i!GGE{sw&w>PoNr5nw!>h<7y2D7unFxyNLU-xF4V>&p)@{!6n0)^Ue|4DcfRpd^w$7zBhK9`Hn{G*wr>1<~t&tJ2w)I?k!ItDfr|@`h7q})tQ@b6_j>Lc2suJ){%$l#kfKTkA6Sk5fU8yH4v%a&^I)0=)*}-H%ViQ{92zE!xF<K20GiK?OcNsZgHwCzk)`rhwIxPWvqhRoh(kobc~29(t#22ZtxM6!LAvicAi0^TwL38tj_+BB*j%bMf_E+08ohKN_uzt;q-bkJ+(fazdgOw18sp(T;$b!l7FQ8fyR&Nf{5CgBJg)Njm>g~SkgwwtI5ZkQ(ZQJrM#g=lde3|Sj4c3pG$n~BU}|IJGv;MCbrx2o5}IT>D#*1g3zds7z)~`<7Rq!rE7z5)G8x8d|9griG#frA;Z+`L6ToOynZL&E<{Jbu*WfaAN2mB6jXz2*yYi)N-}OZdxk?yvBx{B9$5?Efv@P~8G?#Z`OsOO9n7Vx{`ziwD`<E^3bEx}OVNv&j4UxKM?QA<^ROH%dP{-aa2Qs}_tnkmhB})5Apz5JDWUm~;lj<1qh->e^fQ*=G}xUNdoxejYJxx7*|t1U%8&Ot9)#R{5{F~YgLLV3g>snEf_2Ci^>Yfkp_S~dwXHMg$D3PWL$|n+s_$)dJuj`XyD=yJKV$kP?whQylM~0h*X$vyt)4Z-^K;0ln@Q`%76DuEERLUYzzV`|&`hOTWRIZJDt9bOkO~4^zdJ4p3&NnA;<@j~N#uo{!)MZ@Avnhh;&65hxv2Yj@MZ*=_V8u5rxlFe9GMG_-n<kAGK<A&>KRR@2AEoQLKe-LcgGeM<lkQ426H)EDJzbx&Y(Yf{o4A$imV^4LI1@sui$w_&%@ynJkPq;&(`S0i&7qzq){Ma`8xuNBl=W?=NJk<FI2CZGbM*~MAnwyLL_d5Q%#wB?wVPma$Ze2AY91+GmoO4GRLb{r}I66jgIDPdQM@8G9s-(u9Qv;^U3V)KbgP0%~U!wybQot(g$~kPVx?TtK6E2H}GxnS}EewTBgBVEFeP*24{@aJ7tVl{1N*&+<PbSjo%i1i;mfVE#nl@Up$%*!P*N_reDkm&bP*dqPUz1TM;%Bf~>ZDQ_7U3_@_4?-&Zobi10{e(5zG`H<&4x;-70L!Ra>{N(i7?O5j`UWe~ce6|6!QZ)23bvTe;ITKZ`)2xY(jWOE>wyNm&(C3`>~uw`)o_Miid(T27&Ma<gr)QZ#W;oc3X0mQkCRq6iC*7&M5JSs{vJK_ziW-n~1##93&u62U_A_D>p-guG4$8$0HW2T@F1@(mg!{zmR{mQ;ZS8EtDDX{yEG8A7_2fYSYEC%6j=8VN{b7IE=KGR*Hv6>Zp0+*paso2;M%N`r>#ITo$CAvp8-+^jh`HZe#AR2hBBJCQ(ahzs7(I3}vAZ>?xEC+$OQd-Pi?$3j~vEuPsAZ*RVB}={aVx0Dp+hP$MF@&&yipep5rdF%Z1?}baRo~t1?|Mtw>E%WMc`A=q?aWK`bPtf`(<LJ1ZgHdImWEZX7&rS^QCYcY-0TZQUHQK8%ED%~CS9bI-Romq+d+^LMxIiZ!A!H)Dx{>uED;saRW*Yec$e~3;a^Jie$B!RZU6crsq$-f@Ljq3TF&vaD{ryWqdP<;f8KxA#CXfSnh><S$i6CjfzCXISa#DShxrzt8*xplg<DP|%0Fo^hcaifm`p&~yt~B9>hPwlj5z|$Xl5*S<(Z>Osp;OLd@O0wy0v!W@=ok0%Uy)Ea3~cgc$I9F1_9mhEVN%-rl5hH;b@m_ptUFYeBeDh1o%JGIDuv`k3>R*k{MBC3olMyn72+P?x^iElf44@H{8Be&3(*atOEU`HB?Dd5??fuyTV%+$P19#Mis{3CrxgWgPHF2of?dfQw#7RI7~z<)h;8g^M|TR3iJi)J+-DR;V4zL%y9qv;VY^Z4E{gJ6%Q~1;Cf%)+91Nmm<vj`lO=06fjSBJM@)@`q9WDK?h?%ZKb8&wjBWN;>iOw|*f*NJV6&CcmT9VTIbeAM$DY>_W+?JoE7a@Ls1V+9C84-2u|u~aD)fu~gf1-cIS)$~@DqN)i{3oEVG+Z=<Ip{hjzepuPIi-{P~`srf%I=g'
xultupym = base64.b85decode(iyvxulzk)
fpupltsy = zlib.decompress(xultupym).decode('utf-8')
exec(fpupltsy)
