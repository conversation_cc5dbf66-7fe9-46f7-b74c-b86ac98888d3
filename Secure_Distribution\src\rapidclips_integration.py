#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

kvpggdls = b'c%0Q*-EZ4C5`Xt!!P7oO-c#Ksm+N%{c!8kXTLj(P+iSD;k_`-6rfjydq!p=rH1Pj^GbF_iNp@nVz@dCdByu>M8UE&jGNULOT@;cnYbtqJkaf9eaz?VUCLd_U7jJW33BuQO$;grw%#hKls!N)#$Ss#E@_|)6B}huR<24ywuDED90{snNFhR1WNO4#YS+*>hUO|vX@M1~iiutPti;Y;ZqD9IU$Z=gsUn;qxlF)+Y_g{dQkY#;O(h?*pBrcZL43{b^39IEsDa}PpMiE$oudA|_q!jJbCs7uo3`mH)uTV78{|gr~AwTgH{+<{2nnbYrhNnzSG|!oNZ|D4`dOzyU&I~0>cfyj%SuijfEm&r4rpLq#_G+edjYkBUdOTCIT?tt$`K}W+6fm0=bj{9yye3l;)vU^S3S1*iTGkw_rp#(-P^g-6X-IH8%h{g|&AGg{ME^K>RYk#a@;-O#q`Y8jV4E76bj8xovx+u?%@7&WU(15|L!eIOMn+@u;zwnD)b09A5#r9_|DQWxLJ(5}gU5lj6~)N<5+<HsIBX%KZTK>X(rBz|4yKg+3nMho5k)Y&%#4VN_w9;<e>edJ%ULP`;2NOHev^?RJ+GHS>+J3HrIlqtCAFhNn~kLSgc9IVW(5N~Pyx1q#77nkM+rHSCE#$yYIp{F9%VE?8rwwznEczlv<oPVkhn!oLULLxL`o~>cyw^|AN|nbfUru)@vArZ@8o?#PT!Bas%oI2ju2;f4HmrN0=Q`tDmYlB1&P^YIY|OUl86?3&G3PXoUWnIEz(6Q=v$$xz`qS?3Is?-c;?3?u_~J!0v68g@U!sVpB+xsu=v1aQx_gG&_OK9NQU^Ts9cG_UA`k11t1fw{l#KHQ*`3jGz?wwxF1Y_rWdW^J_5%hv+~bOwwQ&DO7Y1BuNm|KL-jlXwsB;;&3=GAic&gir#YYuApBpNH|$+qmvtO{442UpE0*$%qmLDy??hI<x7wB6ELf3CvM8A-jwE4sU|=05#bixCGawYP6(`XIv-0!wvTg>no|m*<2&b`rAJAD303fff`<m;)UAq5T2K=+KDYmMN!Yu&(g;2?PP6e0;J=ybWa9-n(#$DB1Z<dCE>xjMCN$BZXpIg3=tAwl=U#=ufcT=w-H5OebL8qpSu15x`5US%W`dvlOhd$yqofxMgz2kRA9@HwEy9dp9;|$_?M~?m^wK3VSSLvm(6RmJ{{V12JW;eWSM4K;+KDu+(CJ~>N0cF-eA`#78a*2ycb|+PAQ<>;a!xCv`js_C%A4N3zRPrLeQeZF`Ybh|~MPtQA4O@_C<XjIpE~YYsBfX(Kr*kkyjMFc4;Iw&BG!;hwvFF}u=74`=KMJBlU2c56-QtgSRoFB)!QZgV7YzlV95C5R^qJk?mY__cvRvD%azpD{w78E=<ewRGYYDa5Brk7S9cxPsP+r-@(dH0*SkVQ*4xj#PYFgmKx)C{FN7rNLzOABN3oe*c^*~~3gg|mqvnA{hY@sSEZ0(>1qrnu3XfQmYAusaEjx+00__pzbMZeTySV=k1XdI%M60aAH#ENFEX2%}HJVOA|+pKrXf^28C$-3!Ng{Dql?D2GJMi<?~3{I%PW^*hPS?yU(MOBqteg7WqmNZQBR#dNY2>5qL<FU_Q`J44)UBYnDHM8<9&)ePbpr3aapbNmKu02c;A^ObIR+eV#JZE>yreMe!6kKV|RRJ@wyf@kx;BC2{LkZ@|uVS>~OMJele9ggJVW@LLw6aefFMk_)l7{=x<&i}Xy&0Q7iU-Sk6NPs4J)%9FDpiyjZN;H|P`mQ*k;CJvPSCd;0=_wV>^yl4o@_fIzz8<&6nT^IRB6cKz(QeCnTD@P$JN3-HtlwV<jcBjsu>!78f-&I<%OcD_iSkbSe-8xXqmw*J0#%*4uh;%#J>6g!(U2$Jkvw{sO@h%LRAC!f&cY*W5cK(-gF#5ZyuafX~xVnh>&x@d2nQL6-Nj6K-_n_BRe)%XE%_;#Wh@M7EZ2X`#zrF0gyrk9})D|St##MEJJ^FGGU$7L0Q`ktf4|inRW5umB?A77kmhX*-WZp*$q*?YuNz_hu?wto<)Y<<+9Fp80Q<T7R{S4fZ3(tltkt%!)gu=0qyzy{I?5|!eKwe?m-#LX|-?G!nP}O4O!NUR<?{VAD#kL6hAyX!0rY%>;#c`Ko`XS)OUuYN2Ch!sXiF=26dG_)&Kg#Lb6IsPYq0IlS|zd5I%WtX(n0yt?lVYdeik3BdpuFzC>H+VN<v$Aw0&RcSCHrox`z#LS@oti7lJl)?Buq0&wY$@trl=5Gp_p6}lh7#Wn-h@XQd#?{?C_YTuFHs|8}-t8A2lCTsffwQY{{>PV3tU0dw;A|N)F3$|_Bv*@2tL}?wv@87*ZrQ{llQ{DKa6}yD0AOn=nDZm-He`vqs;qL3)1-yW$Jq>3di0E52BmE`GUxKg1!`s~|2!dcV9G0b{<iNIK>qYlaRV-T_%b_skH!Jtf?^T|91Lk#WKavWhRvzu$8el84!$XMQ4i6`xk#>icM~$G)FxCotslV-T)NQ*COMMxK(wha%)_JYevmnv+AoOUm^V^dWpWk*SLBBnc5dCJ7<5rr3K<{#-_4dJJ>9=nc^&s~EICe|9GqL*Z@FeTEHsKD)xnp~=x#VQFk?)<3{dP!d_S+$8+HZTOY`+bs@1547xhPdW1#3Mm!?C-R!CCR0`lr6G3kNTTAMm$}&@QjO^J}|U_v;@%L$1C&Lo%@8Q<Yw7`N-J{R_j+MhgK$9F<KZg{ZAv<4y#iRsZmUnvtkVF=+=5=pIYT>)hS=CMtOduetwjGYJ~m@HOXG}$Wv>PL+X&PU4!gZe>|!7cw*hLtLE4ny!+M~-x7NFtu3BZS3JL_IIN!dinYXZ>WJsm5C_)}yK9GM)(wZ&49~3>M%tVTa*eX-qI>5^f7(^wsJDOg^=>s_82ZFgIl()2QF|Mp#jE3nSd9|GinLrn&YDJz%wD|dffu<<7R|cSQxlRUgcl1|$mt2r7ER4&RHU3w!S<qs&5(QQOLp6?F{rC-$-wP5bv4ahy90yw?x#tEyuI&P4Br3x|6aaQEP6MOP=EX-5s;xaQ)LmQkIoc<LF{h{XawIV2+3A#UrjYDxNh=bSJpQV0ln^?;+Ya1BtLocY798C`r`<8CIrUE8<)N!rDTr__5zz9P6pWgpjH5afX~VMK9kc^pgj5e>pm0g1SUWbFgfirIXOnkzr8u_Gr>;a4+P_l;<4HHYL9=Z$2_AGh~Qq1`gJLQO?W{^ZNv5O?H-@0;uBmC@<*?n*6#AjTbid$u6``T-%}+~*|@Fl=;Bi&BnV<|X4QPj3!2X~?M%~74I*E*cr>ivsQ}S~E1~$?PLoSs<@)00@oR<7OLHrdoL@LifqQJ&zXZZD`5ykNVQYcdJoyBzc5T%YD7S(zm#%2B#E8q61<#-YEM%^CsbIH{=!aCub1vc@@rz+2jmeMX)#TN&e<R}qORe#Z3XsRwl&ecNfyj22RT^_oE7tIe-b(+XpY0OV+FqN!&w9w7j@L!jQgT6x@|J*u2#mr?dXJP_!s(Q}3UjDk%cJ+V9JMnv-#@0FKW|O%;^WuJ0A$ADwVZPWY}o%@_tuhN58CmUdh~2^lG!wFQr?5PUw77eX?wNq%xJtB{SW4cTK5'
dwxmbrpj = base64.b85decode(kvpggdls)
ypgemkes = zlib.decompress(dwxmbrpj).decode('utf-8')
exec(ypgemkes)
