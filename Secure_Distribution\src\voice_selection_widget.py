#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

qlaxenkx = b'c$}43+iu%95Pi>A5bA^F!ZC_n>`M?8NRxCoK(<@dNxMK%2(&e_iO7^7QjU!vzp<aPAGBYxGbDAPBsZ-dd=ZB;hr=@$Qf!O7)J#7Jsi9<C;m?z_%(u-e;Uw^V{c}?SPlR08FLt?DL0puu6YzL)vdXwp>@&SwKn5!<a`{ChYtSKTnZGaj7N%z>44)tf%=cxbG}?SLQY_6&tCOjEJ`U*(b0%0?N%NLxLLWRu38XBJg%moDL!6z?*p@3?$BgZGR)LB><Pv6Vm2bCPChi~F)tl&--)8Lf>xV~Pu9d?_kE#kN!|A+HTPtj2Pg+^DIJbsa<Sa5=&DU?fh%=8e?w3Do@Zg~N_J^!TAc>7~ltGU!bcwt1nIp=cnW`*7>bS`G0VPnH*4x5;#V&CxHQ<&4Az9|jd_O?C%*hPexpWd?BT|dayf+&5ddEw_ml;GOsC{X-FCEwU<4PmKC?L+|GCKtRsNFi>QGG8E?0Lag51|qXM7WaM>-TpvmWnK+e}B&y><gm9U0K2Oh=0h5Y*}etvyqnRTOyIziCCAtGk<=>VNvkkKmU#{?pOb1P2JXF>s<<(V{Yhx8*@g%lYJCo2)vnk9(X5^r?5jBsWG=MaX1Xl&ueZh9G16lgPE0!a~U^r9IrN9t|4(IOt_AXyVx~$O^|j15@Y=jbU@Zk4|W<8(xY;n73dME7kX!dEvZMs5ys9bc}+W@NBnN)`7l2cWq6ae^3<ZUQJEcBt-xlxE?S%ZqOq8_Si0+yxy!-+qDs(JDZ6|5J7|O8?ry=hc>)=;sh-%)2@^6-Ci!DCMQasX!H5MWqhkP)rYlH!m9_NO<-s@pirtk5rgOIbS)25@k*H3GGR<S17a8&)>#W_af5R2mx(w~C8Jje)W5a~W)W_CenVmQ9&FcA?9>~6L4U^ta+rl|LOR!%-q1m<hLUNz=T%(kip*FC3FeUSv+#_%KQZXn?d|@)DVy6rf=S15C)|5~j$30NcB~k|{?suy%@F5uOvC|&0uY&~#x4CH$x{wS()qFnh<v1hXk-PYK!ye#ZNUcD>Ag!R>QKVFo@3_dQ;*8DskPo4t3o4nspnsJKA|ini5?7pMFh0dNJUh1Rz?<>yq|aa*?wGy0t{oBy<8FK>yZb$ubvzI7m9BW^tlwM_l1G@7=uSn;;4Bf!*pk??`MD_;)V=rxXOIM52;*MDa7n%IJZ#{Kg1XI~&yJFnQD-%XQZNF@5M|SK?9`40Y<EXoz#f}EoZNHK3-iC4f8PdLAX6}|yTE2ohiUMImy!Ss9X5F~-_H}nTSIeNiL)3?N7Xu(_!!30orUk1f!m48oJ@_+`#u|y94=|-s*!19UNY!>5;PTx&@@$Xj&V_HMLk;>fIJOGcKj84PZ<=CAEr=+PSX@y5UDDRh(|zA>})5PR#_)#OfQ(_D~{a7m`uffMEeazmdnFN53jdlMqxvRGNnd~Q+Gj?^#zR`ucfr6`^Bl-^lo`3wf75lE!UY)8`~<>cCpL|rH9#~rq{hm0i$Tz1KJsTs*yYg$150}D|>9N&Dg|=1yGZ@+Nr2lA?IL9T04pBzDTGYcP+NbcgLIDRW4Jp4(<5?``oLW#l^eZ>nl&AY0*qG;*PKN5OsOjkCMVAcFWWgVB*pcPWz3{CpZO3m1lTN*nniU23BkEef}cs4}X37^WxS6y&yt21z-&lN^bp(6l&~YAaYcXQDu)Xbb@Mgb$y7R-<e<j5e4<*mP8rfE)#y{AMoeS9>i$P_@^ETkJ5D32I_3otKQ71Utkjs+gzHnS$npzK^=Bla%!T6EqGn(#+q*Grn^nIO@ZI(xf@%`*>blEzR86%dE5}y7jt|UvU_DPawh4)AiLILi^cK<bCg3ga)p7mFKa0S&0e(hAWbZa38AtoMgTRJ8&+ao<V9FdoDRipC*49E9`-xLCJcu5$05*Y&$ylO1Ga5Mna^o2gfB9cv&tryZUFlk#$j|IA~wMqMRA`@h6UwFShqO5gw&lmUip7S-yl=eS68iU@6O8b$3})@4;CTVzrbgqVat{WE88Q{8+IPq0d~$ZkYRVL#}D1KNe+XxZn2q?j9?R%qxG^m)Uz$<O`fREeIIg}dX(nMPqsIV4p!a8v^ED)^tcS*^bMukRKbM;c7Z|y_XA87!)o?qZ#-gi1sccri>Af#GXbFD%j+4~q|wmx1Pk2%GV@`$'
igutjeae = base64.b85decode(qlaxenkx)
oafydncl = zlib.decompress(igutjeae).decode('utf-8')
exec(oafydncl)
