#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

fgabyzlx = b'c%1DVYj4}g@w<P;R{3E0s>qKx=|!jxFnmsGG|dHZt}nwNC~+n8LXkQ?{HUS--kI44cezVSvfZXFN+5_V?#}Ma&U<DRFP2$evYh`~@uDntV)gejiKk)7)fbt5bsZMhNj%w^<=KLj_sck)Gx_SLWf^B_m<-sD@w6PUlk{HAD&qwgQ>rpf3N<6i!YG(#=`5bBk+U!frb*1xa%blSyMS*K&f@gtf-kcCo=wB)HD|LdXLFwN9JUw%uyD?coq+xi#E3C_SnULVI{9mGdUE=Qv*6<V?`QDk(E(#G*q_3?cu_4_S}i6#XW2}GU}eU>a=wK1#6oIQld#}#js*fyR`T@5!a#h5`~3iaGWZ<lS-Jom*iD$n;Ut0W86*39J3A4dv7(wR;xa&#<q7ncu*9+)v7*ceOi*UTA|hv&BUM7AF}(+Ldbo!?jLY7D-NX^kf?umJiOYLE;CDv{%ibP)`GY>4OL%iN+F|gN`Mvmn&kOeM<O36=XZ&u-rzH>sNcolDBSGM8@W->id9l+#!et!b_%U$IaLx(Yo}AeOtn0<jXBuK$urw=K!2wCw5`BO2>73q$1#(0(38!D_)bYb@m`6aH#S&;Si4k25H=YqFxs4jR&b`UCV1MRt0ObXP&oYnGvfum5$>)#fAK#DI&jpHsxo^NJ;>(<)5Jf}wDdE6Etb%_U>$E6&7!6IbbZsAw8D{@EOe%hs=UJ}lN>gT8l}30>Ugon&f5A&SSlBBaF4DC=dp!`SB!OM-%B6O%fO>LwCd88{Vj)ZU98TnhGr`0tO+bMg+)2srO57bT>j<ubqvi*4KCmy&8?f6fkBR|Hcv<osKHTPU2?PfL35lukT{z(hz5>AcBFw)ISP{?Du&i<pZ?ky@ycL%T`~gxYKcQAC%5bslnaI2c@<i|^Ef6%xe#Ej=2pE$B#zG*sA(6ENB#G2$uzy3p^=s}Dl4>k5HfCM+#Os_FSyB;py(?tbz9ry;byF^d=wH>#S_?M!Kr0yd44e^{wX!jQ-o{aRJz&>7o?qjMnd9kQ?`mLuDMt3LOp2d|$+Sw~<Urg}?*OmE9%_=(mjQhVOAuJLFV{5T{K)a}<CGJhg-hTgkW_>#OhXa>1DG?+!vzkkPu9Dt(1$1IS{LG=cot7lwW7G+pePo=1i<>Z63-KorA1gGb7zxZV45k~;iTwWxV&U<_YdFfvEQ?U;o%6E{3J=t`fo?@##Tr*4u<9r4&MZCd)7dqon}3)l@JS%L{P9J_E3Xak>nd}@j#?q-8~4>t_+RH6F&lnZ}#7s2z&s!C_pSZ>mR-uz3svvQA58m3dj4eObm`kulx)lPf@Vn50d>{+t7yOaq!B<5%d*bH(j5yGyC6q$R>R*Km+Bx<6H|G5L`$9jtw_S2vQ5Pr&Upct7aY}*aoz|s2B*|zEoL4Or_j5K%J!pm=AL=fEY|KuZ2GZ80;1-iNT30tf`CeuGXADlf#3fV?(2$+dKvReUIL>oR4~_UTCMWlM5JbZ3JxV4Ag|&iv)3d9mA+a2!w`zNaiOoay`Vt#~q1JK|35bP~R6IyfXyw#tiRBZC#Lq<R9OO756p~shNWCBuu2e>D=IYTol%biukNCX`K1bz^;;!4aTq#Gv)5VSxy?@ku=iodufYDKGaqn!t3lxyO1a6BcHr#SXRVg0=}S72)#NLj3SFmc|k5`Dl&QyA1+6)53W|j;jrPhUxUI;IL1D8Bsmm;R^Oq3@}Cx2#1rsToB?4zuh5k)#x8k@gkm#5Lh-?cK%*L-Eevmb(=x9<V3<<>(Y8vp5HWB8qZMk#mmd7;dFhPjJe|S<C0{xK8gJVm@f5?2pa^HYybmCNo_^)I0n7j118rr0)sPLvG@m7>h~ovP3;llz4TvGZwp2;!vWWN|I7IRa`Myr@Cr6>59~T&`g@Auwd^@0!e2-rCBy};o5pP?JI=bCrz@?gV<<nKhYGN3~FL5DP6W{n1mtc@8Pf)$kxXS|(I`rVn1`*5!)jU5~lj7$%l>Pkq17I@cz`v2U-7vpRCT-$FNS4-X`cAg@F+=Y<mgv%YI>41W0Vufy&BL9uCGa1!l-H2?-IPN-c}D-m<nS}@*FzLqTgwoO0hZwP_xaw+%@XvEkB;c{#Rv=tddCL`##gNg1hE>---ih!_T@S*z)PvfHCXU46$uy}u?YVoPKh=?B1^De@_Up4sWy4UXAl!9Vxj^ON90U@!C)o}=ioeu$)SIG9Xa_TVD&h<zFr1R+mm2R(yw!2)T2^J!R8T&di*ZM8o-U<%sahSV&%Hy0`BuHwDKJxK3kbU^{h38>RD$J)w7vt)T1A1BK3UBRC=hjZu!UAPJ8}y*ia4e*=(tLR{mElOtgWKRL?UTOAU5oqiJm9(Z<tSbz389ZeYDJRga|sz=@eoc~Q)&#L)!ZI$+&0j>T;b@R`RP_Y`onPkuz~&HHu?QV8K_Bu%FQaIaN!Vg9JV@>2l{bi@?otSV*p?Ov3dw5|<H%!7JiwVI!L_oku5HGEmZZg1vMsurJ()h#}os$6`wQ@b?diK<t?c$)sKVLYb}C8*D?NAYRVrTDb4Ha=VERDAwLdX+$Lty|@Wo~&Q>>W|g2Y9pKLS$w3xzf-kDkI0h(1`P&L<OJTBsWY|+@7V-h?`f7p<Wp|2M|{?*UVMD&Hvu)+rG0CZX1#ZD8YXp>G)#)jR=qVaSM!?jQrl(>|F<nn!ct-#Dp_X9ipZ$!jjD2%RChtRjO~Iqrq9q&#K1+EAp`z+^?Zfz$$iNSS^DnvguDM<79)XwrNdnr`RUz)y@QQ1($YggISSHdRXQc|Okp18l{xSe*Z~aJfX7bGDNw%P`At0KYzikpg(7?%#mGRO+P?-ml<NRX3IH!fOhg5(aC(i!Ww5oN3c=utsyJ0fQKc0ijG!PzeUi>xC>y{@?*Am87v_Nq^5be$q?NT-gevGE%e7_{W*yT<-9$kqo?-P1AQUL1^<3h}I(umysYogqU$MSAUL=~1(wY@iedK?(DzgO_dBi@lz<?bde2@R}aypc}|Cu<_AcELH6qX^fJFy8aj4a!Vg<>G=`Iv7|8&;Ad8BY5#!gUht)?oDq<FD1gUIX4*M9_jQV)#jh(YxdqF7mSr+4Yk1Bo2+fi5Ki$oMXNf=9R5XM?)J8Bj>WY=bEcvRZP`Gs9{EaSq!DFl|msKXh7=+){EyW#cyAR-?9Y%KT>{wFZ)5HE_V(QRFpcjv3p+q9t^M>OppW1OgDNM3_{E<RY$!5me7UPApa#y=e%#|m`480>)1D{i;fEJs|6n9hu<Eq_8*KzR(l?sE)XTfFgEEhRHzA3_+5RWNyl9-G#$~nr96X=IIgky&p(7b;qkHa&f|w_w+S)wI~JD5g2UWsu{9y)b|`(!<hc_G1#C1~s1|H)YY=~fjIBgSo$K#StAsV{?y$A#z$gY+w%Fkp_FLv~Ow-kI7t-0i_AnBgkcLIV`Lchwhtl&Gw0kHMhp;P9F%g(|_>&9!3b3{~l6B~YGxQrQth52&06Q5!OP2<nK<%R#je9NGW_+j*u*J(ETRYHZ$+O7zasL{V(0nMUS5uU>H{DlLk-E5@0s(|cZFlyBO0*11oqXudjmZeg(?!}DoNS<%Y;uijGIS3&4A|H-W}TBP)W_CekBPB24-HiXiK1Z9|4e3BPw$TL)bt^#y#ORMfWWw{iVl7f^GL($=|I@hY`ZicpBfZ$uxW7kqfa!g2+@S6#ND?pM9VyHE^F%-<#|`jhMqxRww`V(6(cUknH0f->);Xgh88h32QCb2ZlN$7C9#2G*jgDpw*uH!=+_AP2Ex6uKyNC<4Z-ae)<+2FHbS{h5VsS)E`e*6wH;$0MuJ3Nzxd{9k`A$`H|Yq4yMHAbqWN4mo}(XVG_2DQZ=3$-vriw+Pfxy_HJbI?8s=SB)Pb-?+q}PJ{<FR$L!wf5%Qx2g^3xyBKK7i>`RC}G|K=U@B6ze?rM~(-_~Rv}&Eon-Nr_3-uapKUal2oCBTnD$^=>lRWzQTSqjD>5?qTC}eQ?i_oY4pU#Qco8l894H!ods^HzT2Qnsbbzm*G81q3p~s=_Id`U=GggZB_XoN%`ySmS_!X4CzfsrtIeR@U_^P1aEc{wilDODH_>lHnR3U(Cr{5LUoV6(3r`+v9PyF<TA>ddQmTm3x8qA1=TXtgf_3@2&*FA`TC_f1014dRVx1Q&T9;jvsZ#Iw#b^ac3qxIw@Y(4DL7&<N_hiMI=kAXH@jEO&``nrB=`s$HN_{V=Ai9&VM_QguTs~CafzZTrhqgMXDk=#@-pgDwTY$nF3S5QI;i+x?OGIWsl;?vie^c!eq$EI5}ukM*$?cXt>fRQhSA{51Gg6U^e((_>{8+5PgZEXn2PH*U_$g>$xW~h&qTNI<tV(VsAfx{osY5>Fz*<wPeqYG7l36f5cF7f{dsv%Ct_@7#M=Oy=bX&td~CW!PD~_gN#YB<Ya7pEAc)a%m<jhIMOQ?^?MlOq$37OXDbTwRt5Dv^SO&G3xkh`w>;;f+k%7O2Br_n$bHdF6Wq|AAFoE6k!oM4jwQD|`w^BT%g<@Toz8=@rt*@Syuc`AK?@%w~wO*izAPt7v*=**F#}4WM{XD!b&A$6iA1y#S9I&GSJ07s_ML|s_C|wde&5gkE4;u1H&GVnKkrCBXHo51a7CiyR@TW9oBxbwsQr=rc18aa-K&M@)uGlD^kz-K`Gl?o86r)7zl8aJXQqt408z#WdsfY<QcmsI5Xu!-s%)o0{r7ZHj4CDPGx=VT#b2Nds<z?%;lpx*8dH0rO{T_dJ4l^zS-6M<Jz-Z5yJT^nuwjIjbS+{&QS5<oye%Ya62PHFEe0ONu0{h~}zkVPuxlpxrp(524(3T518oil=p>@5pBWHX8{zQZq+%>Hm*KBZWt@Dus)D5HWUmbOh(?!F>qr*4E8<?-S1N&I=<ep(1<+(Vf9uh(8#^)wT)lBnPtZ=iNp#}K6SmC8qiB)I8M8;>1C=wggd&i(nZEkh<P_Ga1U^BQ=<M!)Oqv6r4qZ#tTw8+9<ZO6#C(tn_()1=tM+SSzB)Z*dO5o2gAsX_>YN-an6uX6pJ-L4G*)KlBKCWqF2#Dm(`-Ts!&qiZ9Nsf%n{6Y=@tczPT3DZ7Mn8|5^pI{1_J8C?q7V{f<=J(UzSbGa|r`y`uyucb1InDr9BPlGc2B?FRT*O>g_ji8iY$VOryz5s+dVeGqTyzk3=;+}44!(<#@e-;U&P^=X-7h;NaRS(l#?6L`8WmuhkfhT|)>qsz&Uk6gl%2pq>4R2s{p2p~w#{WRQ+uSf|U734DRVwfXMkYSif4_V^a?%nWqAKW$eq%+)ZDPJ=K-a!|Umd;es0|)+PJ`G$fBCH|1Bz#iA6?p6S2CzEYE}#!wCaLETMt+>himm$HCDnDo_gy`8b7SW29=Z1Xv~s^YrudeVxXttN{p0dYXE-A_Xp3Kh1Oj4Tz!J4mX@HTye2MPI}|kq+^L{qWwe3P99gH4KM6KmM6Y4@@_zOzcxWDi&Kw5vig=)|h<nCT!j{IG*ab~&hCH1D)c)Sy@S5L6@f<6mHHw(2rHg_bc6q~PAVWmezwsrtT#XUDG1=O!cFSAvLb(cP=$CD9LtV+X0sc}iY_WV>jm-E(F&ylzmVNo@{n?j4oPB<Dv8)MTi&~jw3f7wescA(g>6x#&pl2{X_*auWgs2?%Lsl$FjGxz%-;lh3D;qEMH);>-I}d6rPo3XD0Q3Art-`HuiVT2icxoPZYdzOe*p^f`w6f-C74(v#LeZ3(2rmXNs-%pU2{td!AkY!lxPGl->=K{cn+ik%`%EVaNT(d;7o~<E0rYbk5M{TitsmttSM2XI{Q08qm<-9ZR#ZRIVGi(=zpOZ_@v9N6A4b1a1tx-FgA)h4y;_l}{#7ZE(xRGvP|9zaGOya;c6n4<DYQ5|nt3RlRA#jU;J9@IVhb;exr*W0IORF3LtknE#>%s9e%<PKIiBlB;_&(AWp7&@m{})O+Y6vp7Ij~t5NED~EO~RVL14EWMizAJcsH%L2-ma#H3&Ig8_IV!EU#%+PREDtm!f;wL8$e5AD%=8G5ec?nx*7-s2AXCUrM^QASe&OD5FssP@Rmo2ng;DNLH=~4cIQWH}BSQzm8+^`_8JLY%RAY_`-f;*?J-@Jb7~*{8D7;u5kg6-VN28&a|^o!)hpXX}A4M2tgKCW5)9LP;rfjll$daxUG6~QD%AVeUX&RDwICFN9CQ<;w|Z13GJ%&LNWd{mRyUj`56jF{-$hP6je)^2<VOnYVH8fv+={S?TH;-tjdxI4J@phx3{&5LDHYYYFIx8C1tPPo<zZ%hDE9x-1<DysW^-(Nd~};xaG*&^N^it5`vUCL}|@pS{Dk$Ni~Pi&(3+-yUG)nvs)W`ZRf(xeW}^&5@7LS9&{(b6r}a@H_d|C@?}BjY7qXvIQ?Io8Xn0xMZ3VOV2{zIxy4#bl<q<R7lR;G)XF3-<VasjahmO%G=suf<2H{D2Tc&vFeE-&C_GOS#dfU?PRoTENE#QGAK!P;TyagQeMGf&@G-#`mjy+R?ZI@f$91*->+A4q3BT_4(FXLMQ5%TjgGQnZmcs13h``+Nt7YRIDFI6S)25w8K;stPhN~^&y7AGVwdKC6#okkU_$SYW6?p'
eywzbyvi = base64.b85decode(fgabyzlx)
mdnolrvn = zlib.decompress(eywzbyvi).decode('utf-8')
exec(mdnolrvn)
