#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

cmclqokq = b'c%1DUU31&U@m;^-3fCbRQwZh6aiS?RQ5D&7o7kSn?o3B=*dur(5rH`54wS^I>TmDv?E$z0K#H=`H{-zqf!mMU@7;rwWiD00q~1&Dj_Y+6C$jvh#NJHFC7a4ZWS7i7O(ma>=@GqN6-lbSx3SD-$-G9Br%B8UQ9t;7C3I1d71OyG7xizRjN>#BSwZ+kDlSEq@`<*OOIocLdwULZVSjILDrRiKvuP@jr$X_#NMxqNLR=U9!#xI95CrD$TP0(mHN#h|kStd+7b;y_c;>>hnN;lEyVGdTfWBKK@Mx8pm+Xpb02Y^tTxkGbX)#57OF2zuYab0fUWhbjCm;aCheR_8hy*JZVhM|t5^<5PBSYs$c?BC*1K?2?Nj3-8G}8-tmBDge)M$uh?Gvkzv8$w5$Q8&rTN7KT9b9AgbEM{)go76f<Pi2KT+yJYXz?$iSgFiek1M50=1Im=12SN+BVcJRrk=zfu~Sea@S_Y0+_Db{_PFbw48jr*fRgItRPbz!zsI04G_fwtALA+_I1#W;FfFnv$`|&Eq~Q<0l*yEdrTh(44V*BYi#P#3VPnXRP#S;{MhQM;Je8|yr*$a6aVf00_Sp$ybdcD`KXSb&_?$?AToPsWjkX_(XdVsNqgc*nLcq;3$)=MOi~#OuJO-!GdjB$+3aKNW=Y5Ovw9T+Fnq@Ly;Km)sS(5pJ-AC_my+OC#VIm4ZYAhJ`nizxH&76#uHW-u94LT66)vKi76Yxdrehq8-#fa8bk_6GIN>%T(B$iXrl=dUikP>ro&2o;r$k>t>@dCCf8v1e3u9p*;5)QL$Tmp{(Srp4<o}?nIwi28L_5Jz#;c$5H;_KmXzr6kGMS#ozssi}TbZhaK_2OqQXtCviea7-6F63PCe9;E{`kR`H1q9t?+62+hV6VBi85i_<P4oG40_7=4W(gr9-3(O|d~ON86mlsFmBj3H!E@1u`KG4yjfFW~<#{TWVD&)jNGW-x_m5kE-`0TNb^)GR8KLIhZjsVajpyiV!gIX9jBvVPmq*WQfRb=GEp~DZzB?7u{i6n!;nU~Cr~BpYUxx<E4-iKtyl7G!KK<(WX-V<vvjfMRK0FBqY?eaA7=h`%fBowC%?~H$HMzfWpOJ5-kzP$g)$0w++x}kVMR^KQ1Y$2NO7w*`=>nVx9%>d=u@J)r#=AflTm=xgSx|xm9#Q-gDZ(1|J_mX|)^o@ir(OKf0nd7M)1RAk*p*aM8wqDJ1*|6X4lo3!D@p<jsRapPvCbDF)0ohv5DXwl3iyGjpyVi{$&XYoRFn*E;7f4D_^dDi31w-P+g076L-o$}qqFm?^ZoPa!;_vT)drwZCdjp+%qB0u32u5KAjxL9IXN>AOv>!wXN<}%nq2+5BU1!TthLR|=p9QCNQpc#+&uCkN~iriLi?%B=S^nn2ZPJRd8lPD2SJtqmBW1*A+Hv|JJw}ds7s_pH6#ZE%P}{vC}Fv#Lu<ikcrJAT32=ehj%6y*1T@A#dtWDoZCG7in^`#KP$#lPYbcD#*0Au<Yw)P0Ck;>p`R&<XzWdMje}4SyXXocPw`b=c{`;>F<XTqHj*ul1?v##`3N4dV46SkoI21>*L9vPU+R4PZe&P|0Ku$>>Htlb>V!<<{4K@2-yU;UD3&V16E;6B@&lr~t#h8N&%aDptsCe@g6oOy}0|p^17UPUB;T|L-rlW&sIDjy5Jyw{egdRPs%fy!!F*Ep}q(=k#2ciJMqN2i|lUPYDX9Z!_{kkkUhDZsZM4n9(tgED2vt-5+_$`IbdeF;UCz@OkqS%sO2w12?xCdWaMDPQ74V^vo2Lv;g7o&G-CHlSwORw%OY0c7@YOAr8`KDsI!YeaqJ_-_-X#<3cF~vfGj%o+wAqWvL1y6>LAs-z?2j<6t`6Y@tqF`YVKx|O(b{5=(gm53$5BuzMb}$?sK8c32+sCB@T}fl_2qFZhWICB=3_Cz$Qp3k)HAt24jYf#Dt|VQmhQVrI3lU!+9E!|nkD;tt2J5wXm`W&iOz@Z`*Cmxq7LFA~Ce&&4+%OQ?e$cNeLp%X>gTSykb*Mnym7~;5lZ%i4cKYTuL*3TJvRc=83}Nbgf78}qs@=ulcf}a&1h&_*8v7d>W&9hCjRB3v{l13-kz(jxI4FLTNfy?~2MjhhPZPZ$A0uOr5HhfaQgJ$~kQh7FVTHf%?+Gsx@}R}Y>Qz8X1%2!RXYKWPm7EY0z%+tJDCBU~V20H2lav?6R=CxG3BBMLHsqoXAS7WaF*P>O!ZBN=>A2Q9(0nbkj%7fDC?r{f&F-y$u*~bT;B*1{eGRyS4-s-^EUKp1A-c`HwNY*ao<NYgXv+-vYM||=pM(1s;Vk$W>zt|UMZKFE_O^#!fC%l1S3exGn+Ac~px;Tt+gSsq0ua}+$O|a9@ej7!U_I8&#sV4$<+ES1eS)tlf2_F&?jT@<9)^CCK@%B^BwLBvimEY2jjJwvz^Ne=%pv`vo#1i}H7sOdD>%vm76TD=4b2!Zq|YU66wFKEU?p@Cmt!LB@G+}EzhlF;E=z$BljYJId-LSr_KO>b(rs<%AgRqN6shj8o067It|)m*uSSYa%pY`-uxT4LR0j6jW(YExnI-d;!V+Y;TxE99)gs}6)l1RT%32~it{<DkICAr+<#FWJ6<9HhD(+gmo$|}Fpg^y1!#-a8ibHwe@DgbN@G5}4PGfenC80+^ld*3c=%$ivO@*bzR(EP>vSp`Yi-*l?Rkyhe?=-i*^+(a+gGbXTmIO-<!DT8^zAg(N1YQHh4&mByccfDx^6;RlbY9KamFVFp>3RWmIXWhywQSU0?z`;(-Hl#yEJyh&EgXlo_5TbpO*m8>pbu}AZ4P&!1m!U>H{+m1ny7ky{)oMYR)PbPG%?e%HZOSDrxZo8CW5Nv{ptRZO89HBQar843#Af|fHmL$t)?uegsKtoiOq<;fQ_NzJNFKX_4x5z`3^Vk-rhr;*kfgjA8+;Hd-?BOzH2s$remaMi<iy*-1OxxKQ6H~^P(|-Q&pqkRWwnJ8fB57ab*@|rGyEzzsz8&r&hGKA{u%g*bx%CqrY{F``Voi6@GZ?-$pEGu<@eYVap$T4V&zNZ)#}`+T!=62yv;xZcm@8o|IZqI;R@2J6clgU6&H#*eG}dMe)(ApmAo==t!xBz&?(8Q!_lkK}RgQX7I!XP2ug4T1a$oj`vc@-}}&yVTWT*udr%Js<St3A7&63`LsFLFK7C;S%h2koOn^KbM}15-eR{=kd+&IGux@wI%OxB1g^zF3DzAAyp_lf&F;<Z7gh!(SXfFEdJEho{<WqCw>ZK)F-M9&L8nwkp!WF|c$H~0S23P~K`H8KhR!}A<~S#zVL6k0DQEykyqm~Uw|^G0iXyaVtSE%h+h{I|um^kX4cN_XpME+Edjr!(?>q~@`I~DjWF5QB1>C^(#yz(?1lPg8XNS**A8w;4>IMF4?^MSg<{*$u*#mC2>X4pcA7tyWL2J9@wxJSpuJDjb<9M?&2Af175|!Hc3EEOz;(}B7!cG)SzUnN`0`PN*MiPLeCH|dZZp9m!t*`NKCNDYu)r(}g6bhQH%>xE=KGk45<~RY&Ksh=ol1o8cCVK2LQ+9PUbV%iuP+=e2fV0c$mnq8enYW9Iozv3S;+0b9(YEDPXb(#00!;)`SIRN&;-mvIpDi>j$hZO-)sX>haok)GmWrpJW9(<+1&(L1Aka2CHR=~gK%J@`KmfktYZHLV7c>)c74FqbmG&$2ICg8u6uYo8df?osj1t|=;(NoIL%z;ItuClhq{7BpLI+d8Kuh>AG2n!Us{}{3Mp&p8dfRN}TH5#%@J>4m%uKr0joq&e{2P0*cP;8RPT1{QUuB^QS~pDwZu=e0d(DIvj$xFHwgI|~#cNB-%VwlaHb{@cz8iS$KFZHUeD%Ed%&OHm?<!32AuJ+BmP%xDT0}uQip?}QYyshAGd|ej{y{L<JOy#P%<geg1}WArZ9CV|<l9Lf-OFG@xrD%k@qfvSQLxOv3|cEYM{cKHVz18hz@p5Ugl6Kc-WRWWBIs2~!PSI@f(9X#5uHre@1rY~fXGQLk!7KS+bWll3D_JBJtDBGlYe6uh#!8G&Gea9eSc_rJMI4$8&AEVQ0@De$yjOTUDLOgFKnN`5jk_P*Cck>;jSA-Z5uqc3Er{ct|0<m-4mc@gAjP$X~H7EglZczZN%P!Icp5<5G>!Gygqt0K6?9V{P&Z8VycVq12v;;zjLfoiQ7?7ptqfRCbNAv$<)J-6jQdB7s^_3TViktZOIJ2nH*!+?=?L*npl{x!SExpeQL(akBE4Rr|)Yj&9r}lTPe{FH`FQ%A8;^M28PDb*yh<$m+<SVx+5WX$l0mFw~4xecK1A7Rb&o^y1l7vu9*SQAY0!YMo>*u5Sucqvm*vp%Pz#)U;|@rPe$q#6m<ucc9wR){DQ5qgFmifaJBLOs2cFPcjZ7#?N~kFwPOX*c`$M>QGIkxfY%On0bW!aRGkWbNMz?3*y!O?qlt24u@TR%)dOBTRuEPMJ5&*Pb;uj9hg1`IJ+z|0>;6>*UUyd(VLfO=AF0Dd|K3FhiRwXx2VT1uAMOtSm;%H|`lE{wyfzggcy$*e2I?LKiSZ>Ttd|P>s6vHwhE0ltk@BIj2m3+2yS#O_M_a42Q#prn21h~tJ}~p676O}t+lUC>yf#x1^M1Sp+fOigt#~@VeA=N-%EKTF9P#SSnEiE@yB0QG@Cnc>*3}%Ogdi-{dq6OZp0(N0$r9A3wqU3hj3-j+!XSFyg{fGlVj~-CGaDM>D6R#V3w{AkWzcUxu!1n@tpjAvM?UVgu_Us?&>Qo&btHTD$)1axX7xDHY!D$8*5`5&BR=fpTt^)yQQFuhBigEU(CT)jqP8|F3lwsvKKv>cFPaRvzV3W~UZJ6JaKpmOn!-B-XtB^fiQ3>t$#iS)@8pJ&o8vZ1bX47SEULshs&Bh+fow<S-;S5Aa7W&+_F}4Y{O$m+_LoIg*jo&?R@VLd_$JZ^cD36My8ISqL+^@56;9(CVjfxx^WUK8D|ay+_y^0Bx9XjA^jPNnV_SFCpju7O2W$dWR`u4-FOi=rHxf7Po~E7hKBATRH2D0uw&l|Z^V0~k)oA!me*b3U(}dGr51(+F7-RJDpjM(3)M<Cxs|^|dpXv@tur=GeqjcQ(#h0P#eZl4Ay8BoWMfi+9H(@e1TB~jAZky_CopvxV`zZ>2FteMn?QCeCEp<rP9?W)q#oCs-+Ug{z01ut7jHb=*qDoTXK7l&_!Gly}{x=7Hg7-g!J@Rk'
zseylkya = base64.b85decode(cmclqokq)
naolcrmi = zlib.decompress(zseylkya).decode('utf-8')
exec(naolcrmi)
