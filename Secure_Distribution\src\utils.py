#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

rfxtcwqs = b'c%0Q5Yj4}O_Pc)tp&+n~*<RXS>=wp+fh>LHuHAwr-B7r`0xi)t8=2Hd%86n4zu$96N}~Kow{Kg4RuXyMZ_?$fLMlU~?r`^}SoQW*OVODtxgr@g%<vT>ey95G?0;KmvnjY(RIu;4HhuDnr^fBxT)(fju2*!yI-Sn#-LD@%CwHH3Uf+^2nRE#JiEdM>Hc{VweAsBin$MqDhU}`(YXO@~8|3;9*vSU+lbTUM-m?W0nXaix!`IYsDQI4;y(tBGLsL@&y_Z1kjZ`c0UwO}(Wj<%*MoDtVi+b^9$#bb?vD`G+>sq|a>$^*G3sUih41R7x9d3U8t$KY$H-rmfmW&{`YWYr;j9jSz=ZfWQrnuR(CBKqF7hKVFL++NW#rA^~Y(sA88ch{y@P-?~v?lLpdf%v}H|bL5a<Rt%)X&P)kdJK5*@JEbq-&Oww<XUSj<2pile-d?r+FZuyx*7L6}A4m%+@?@tintS=vwD=#nY%W4WlM`cX|8n?(!{0&o+#n5sA+9u{gqw)$y3Zt=YHZ3&EC8#To*XxRO5+d{&Nn;V>3)e_o3DQ-O&vU*YX|gyL=vD}@3!M<W&?isJ!nkxxZ3Zc=V2Ia4glxY4H~+<M_yyj#w4eJbv8JEI#eU?o4E2nb@}Gkq#85GbwJ&gS3B6$@XlpH3a;JXl4;0$nbsXhOH0QPz=u<?KPKds&*3X=d%xES{}6#1Av4(htFveJ5vy+dGky&Rllo?174msl3$Yc&b|KbKq|L($VB~wsH$+b1AcBSU}o2?fhw1K;GPjyk7F+Ala?{%z&lfUuRq@_$nwMRUJS`?%v7Sl0p<N$=SV-4>`*gEb8n!os7*%su*;r1aerGDrHH=RjeUq3WKkGVxu#GzQy{)aFes%$nrS@8?PY#NzQ;a$PG3lU_hSZ#~-CMRtd7A>R#JTUC@+ii<|<INGv(1OZGX`2AIY@|LjKHUZ>4S7c2!^lXOWT_@L}KvWgWs5ccHB1L|3#|8y$VQGN=Tm9toNC;$8M@I?=%-9G8U-vFxjB--GMo_>c&$v&2s_{tg;dp{*$MV6uw37-=V1A$~(bPZvTP$Dfnf}yd2ht$J@n&t4T<RY#<U`x;y3*6G{lL)aQCno@|=sklC9aqHqjzJ>t$5<5hMGNJcOJysIbv2GC%)&OFKPFtmr-3_^rcL9NOqUsi4pb>^=&B$_5+-!17`T!D0rjjcDWmC<tRZKm#cuG%^{h3lUm)CFNX_vxj?O=yug<gN{N4G7^V{Tn?hWV|)t;V5+rX>4WLq=YMc=d|*WQpe649<+4fJ7P*dsRGw^k_DDchbjt74{9qG9IKo#uHW3nnN}Xu;z&=S;v53i@f7I5oV{N2mq{%l-I;PxdH^?DGn+txA{I<nrd4#J>V1cn8JbD)~px>5SML0`-}vPLxZv(2kJHDr7lA8t(OgObuE|HL{~Jk@IQ=te)CA0mW><8_`GP1AXMHaz#YBnlXjAPA+N;TNOq-(LP!$1=2yQBhcXt$^|&6EL4#K$}vOveu1WjiHwHutjHXSUdv2=h6fXS1tDIp-P#hSC5&*omOLXVS8173<uQD*l48ym5F5FqS>nE8?^FyiuK>;iii!n`1GRgNBA{#Y;bQg^>QE|-t2t*GR#F2DRz=Qit%h;RT9rI3IV9N<%4-h#%_KP0y9g^T`lv;@Hqf}ydALVBZefJZ9oMmU2qH6<kDUo7T^F|9>7;AdyVE`@b%z=Ez`-9g1`$vcpcoh!9S0vu7SNy}Q7y^=1GFSl6k>oKVH50jr!Cyxp8ht~Dl>rU5}LZ6Tug^vx1kTI8jU@s41D<7KG5V;6V~<@2rf)Kk1hpaN`d2LnwAO%$`RSJUDOr<L(<2^k6|8=0eLn$P`e`i5i<h%^>8>O@ukP#R)oGI&v%#u+koD#x5t>}nw?@0K@QegqOfqYY)C!{I?HVmEMTgkWKDCPHOBMwy-hLSI;}|sMcX8{tj#1<S!~N=zi9+x#PNv$W@#D(#0hQ%De@P4$KZT5IM2xW_2~S===_$P7g4*|ZfnvJMBZ2E3ker<3A5O?WunN+49iaT_Fk!(!s!jN?JW}+e=Ljo`}kgROT)j{AiD-SY7B+L<HUurpZyA+-X~4diM`Rh^RAXsYV2^$Trl#^!7{|d&X2{2L8Z0Q#=)gUpF9g{QWbYwz8jKlgTAZm2!#hi1H0H0h0LWF_za10QIsI;YdnYAq;Kl|25X%I26Off66P8Caa<9Dx15Pk^4?)odoKq-6ORsAHvRKyaV=cTJLDm`fnftIZM)Ngaxn~Y%omE^XkN0{SO$(2iJGFdMoo}eqfl=Wq}sTh;{_8qf0NgKw;#H`X0qW^EG2k}0dY9vr$HD-DHm)-_ZIOi!dg%g&L4vr9D~Buf<IC<pvr`c8JrXb6he8)_NWRb#UR~)4n-+igy8sxhaaw6l;%8N4TR(>T*Nvg2QM#lxVWa!cOU3_PiAAlICy*rmzNNz>ZmE^a9|t2A(Agdxcnj;q@bget<{2R!}37}lvX;WMT^Ir3Pbf~u;w%9?JcE*ge1>lrW$~H;MbNKOMTpOo98WaEo3&ZE&>Lk#dxvgvz)u}WqPa%2x23~1<64jfVFlBaIy4s_5;M<Ch?uTr*q!z|0B~KMvpa9CNVOpTLsG!ILq4ZqvLc5!Jt{tQxo64M)q<V9GpTUO0rVfGyMqd4_<h7(IbQZ*wK7~aMMxyL>4l^s^Uy$XBc6uEMecpe_%v2G@NxUV~niRGETZdi|bAsdxg9$9e}Ksy3?U9a&F@87tw93k1`~X<+a=uYOOo@-i1p=$je&GTj7DoHB8%uU>a3x9~z?_P$D}912fqXgK4N$BGcmo@dN2jdS-|tv&iD@LHKqXQM;o+?xB%Ti4xB>?j6qU-Aa7Gt6u*EmSK+uIeF2lE1?$PZcnGZaH_bq#<HV&yW{|;K@&?3Y&jxxIDQ!$;2?&N?Cf~JR`&N#C-4%A5EMWKO_Jkv6r%Dq97AH04LxL^y!heb!ofo;D-D)Rzt_OmB>Los8aRhGR09G<XtPRIE8l+)$$4=R+5pO~m1uPFJll=7o}2%wl*X_AHY7f}c#+|y2ii(QpFB}Qj;@Y^($I&4r-5RXK=Wc4Ov;+JnaAzT0Uo{L0I*M@ho}!uO(otjj-%4d2QQ-@o|fjLR_0+F3X-qsL`OwyHzrGC_@Fp|+xGND?45Fg-NT6SL?>asOnW;r00o}{uag9TpW@hH*aVpZum!hH2`>rU!&+|Kbst0grLtQMc=ZKhaPw5%E>_=krtujkmQIrR_UUQk)?d1(3}ou<IXWQOCtcOucdL(F)L@8e#`9k5Bsj~d@PllO^pSZV=x1g*{`d2XK3OupSeo&(=PxfX5w*-qtl{Gbb_<9r{7pl+d+Kq77ch!)6Ybx5RTq7hou7j0yFGg;Z7)+Y@TAaqZ;$sxGib;eks@Gg?@j`i!(*3Yl^!E<gO-LvGG?7+WjWV?R?QLl9Us+2m6I(76VWn<<_&5#LFJ4wBJb?;6bjm^7kHo&enfu5E7wq+7_A72bny}&&wuWdfBoE`28vd=!4iopbEC1qu$di&do9bKk?VQL5YF$=^YQ98>k~L7hbFhbpSysn4*r4gPmGkuFWW*8-WEc5oJnLap_67$N7H>De*=8dd`%C>$3VUDpQSPhm`@KV+MuzGrJ_8E>{@gP>*8e(*ewRoeqN1#W*a-M_Q@NF;9sP9BTJFlrQQ>FTfb8!ps<g~l`Qj&2x+V%PI&g%#aBh?lg8hjd>jY(fMYe>J#L-w^^qeeAE2fE4;Mqh9^&rDw?B3JB(`W(5wVplmulPvwDjK3i~)%u=4oAqdYw=<S3<I|f`C{R?2g3yBKBHG^)((F5?fm9AKz%6%MEl!I8*e=952+MoQch2A8fd$efNXglCPi%S`15L?`&^rgoi++);^5_-EaXpw9lk#8h(3@PaGqiAbaOWyVma7PTr@^j~3!@bAEL4o<2Xi{eRl|@&6r4P4W'
fasgulfr = base64.b85decode(rfxtcwqs)
sjlyztsv = zlib.decompress(fasgulfr).decode('utf-8')
exec(sjlyztsv)
