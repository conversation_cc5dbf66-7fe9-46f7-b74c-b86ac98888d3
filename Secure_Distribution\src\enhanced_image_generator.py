#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

lkyhemnn = b'c%0o_TX*9&l78=Bfl(eLO-qi`nX^0NvpqSz>CSB?my_<z**%V<1Cfx3F-UL-(r%{P|9z_p7lIdAQqN`|;up&#P$(3>s`?6mEb~%VHPhyaw4W;BKh-i9-P3<I!qic#QptqZ;>oVkIjh@J7JKGieqYv77hJ8_OPSOw_M&K4?C;XR$IrP^e5+iyrk2Y1UP&cD-e#+Kal4T@--{<tQn6#=ZC&xC-rS0IBb3NRQJY07Oj1c2=ZcxSTC(+D@%Pme20ydejDEkeT^V#`chWRmF)x9U0-Tvd6oN(KSgqMkCyilsCK&0#_Ryx{`1axn34T%SjU5Vqddj?kUb{xAv1HrzM^QIb5%qq-5?!RCFd}7lqB5uhDS#G*&lAe`CizjA=5UHi-U?<!U4x_*{I$>Qr$j4VnHA$j3ZKFHMv{Ig&B_RdX3yF6%@a4y-?hF4)haJDo}AUmz1Wnzt_92zyN8KZ^Mx$l^ZLCmmTbY_^Wr_27(QPAvbwl=x}2|~rcTn;^jlHf$x0Wf;(-=QC)_T`w0K$^lj(ai-C)a;+qBXp)4O2<XAd-ln<taSXzJYp=e$lbfwri`#ZDF}_`t$7Tg5h9VS}%~{O<c7Uw!`K^{Zu&Aa`C!C)KM!FHnKW7OuAhFqxVciC9px6|0+4iRHNQ1O_g6S&AaHJ+IfF+$<3cUy7PML~&EIeWja{-DeV10W{YfhNHL6Dx+b9GUEKfmm?&4P>-SL-yFhX=qV2%K*^eI+e7c5GoX3D{A{{`e;Cww<n5!d8cpN(hpXMpPx6Yr3m|)#E&p}Yj76i^A&4j*juAwU55dRA(9b5q!(fSB!`@3fV96DDhnVi4LApICyw=6!+i3{(=ka^QkG1h{fS$hKY3zjjIfbC`@-U9N{*!CHt9TB~VDcR=_UZtBAhp%gZ%W`8rATUdccAN?6!)hhYdpW-C%(cwe-n_6q(?~mddKbsSh`VZF|R!vX1IiMCzHVTR;)3C$fAKi1x(p-Tuj3;3w5R`bUjq5)B7X#y2Sh^3t+>YJS2=Y2U3YLcd~-a|7`s!{Kad=E5ULNPr_JtBFO7R5Eu*!UsUt!i$8pJGw)$xIoaphTmZS3<zi|1rzKqeJ<LXlViDRb*<aZwmkzRaIzLoWS772_Mbz(J)|n^G_qs|==->=P4{`W8F-6dHo=Q-jxAwCrp}wJyJE&cnO3-IdfbcU8fxn4w;s+`y<~PHU`jb5{NnW%IREb_(D19#~XAu81)_WNl06Gr`>VtGl`-knXub$nYCEwus1K9&$JoCm2je^Xv8;nPo7VTgaqYmr=p#vGYcZzQ`Ocp`t+yMe)uQm!5)h4d&p0nAC%`W}{ofiXjVs}HiT}c)T0IFDPjb%esTYtm00Ia=)a@TtnVsTP|CHoKd?6NaUMWx`lM0C$5Ww@HS$S|mFN^tP9-XO|0rrFlAR$^QnuUL5ZSg~wnMrE&+jwDzXiLRhZ=fs#Ru-Y9jFgyVGa;mdztO?_sRws{3<ly|B!qm|JNocQq*8>%`o5^79dQ<QMsagR5^eo|`V>c)k8W>>w16;^YkFVoLpjxgW%|fsSXoC>G6^dNBgjfN?vW2+VUjQh-(HYp}E1{I0;p)unM;i?jC3w-TuZyA079!{`*S}odJiYqa{1Jo8JX-01=+85e+~SyZWJo`6gfv%`;3+Qhp<FRgAcP*-o3wv?ieX-)>9jr&RseQ)GIfvM*0D?MX73_l4-tp}le`w_pI2=DZlLhPd}jT|ZR7~MzqGJ>7PYS0t>#sFMf+9P<S{Rb_Qq15B;yofkO$%>)^)#;Yh~(I3F~dvYB<2gjR*95*A&TB*#5f4{x_?zQ+B;=LHV1TC*+!gr&oR<fmYU4li0f3Iw!qye)>F`%Jn?5(>(0p9NiF`0>GhQI=L}tbqV0}L4{InhhR)*cU;=7KnJpUcs-{C7v@5=Bc58<@TSAsK`kE*m6N?zhemVLaw5}BFg)OLQqlu^&)yL<9%eE01EwZw2LCFS6Ys-O)e-t53-16LtWiMGu)*NmuQz%FT+gx9{KnC-<@F^X5)}M`GBCb-yq|51Tb-k}XpcvRd6=8oU?>I@*a%}{)_6!#*WC|(PVb%`kEp>GTw$rgk;yJkP-=Ear2~?B1T{oyAbF5|iuD%6-wI?lxQtlrqi>RAX?(1Xf`IcP<cPysDUSoT?Gm1VZw;r0KXI=I{FzZo9Q7ZSWuz=L>i`&<45}`Kw{x9|1!h<t!DH|<D2k#MqRtAL$hw7Qy%!K`Q&A0{65H-S+VMmJ^$V1J@OrD$c2l?Is9(lqwIOF74Uzy)HNXQ&+HBfwdj9OIKYZq*3g+3#ww>9Af#)eOT8J<+_SF8i)OK3Ra==L-6R{3sF%24rw?H?3e)sdci$8w$^TW>%j&BSaFMAAjDhV#eJ$jiL0J%x^eW5f@ZJIX2JdHj7A(DDJUsJnHDyva8{=dx8#00QG$j)?p#b8{}3^X8bu=>+Z1Qf<v7+b<aU+SgJMGnLe`j|yfdu1DJ^zHLERU<q)NY|aT{O|fEdT8y~KdSix1{XZZ96NI1qBG$t7#t#iqx<&6iQ3uC1;9nFQ=yzpO2ZtWBZ;O#C43tal8kdE4kwBxC;5x<x<O72&oHZY04HrX&~*!`t<PuTSMy%z#FL0P=lc@D&T7qSSd3KecA`Rp3DZ)kv3nLdZVpH5;oNW@+jT%j#xbm;8nL=P5nZ`26fqr|OU%$dY>=^>;Yk1b7>`Lm#Mt_>si=nRks-WrV}ObdPyf0)x}%4I_Mc#aj-5TS`2Y^#b`TJ`+=5>w{Q|L5GS=@AJ$AF#<X!J#Y<i++hy5VJjX!6R?%w`cJo<6=O<s+u-B*DPZhCMtsnmzEbb1%h;|~WHvA=HrkEEXF7lJJDtmhb@OI~L-uSOAiH?vFLU-^r&*w1<k;F3arJB6c#`^N>&cx?nX%o$zQiW%JXtjsj%BDjLUB@^7#N@O8$BPN**EEvZjg6vli^ArR`Q3C=5B!a#$79_Dh|IPMZv78&A6T3#mJN_${R@}Dit`?XK{7m12_!1xoq?N+$ie=DZ{ZD{D5H~bL3T3QaluX~F%N?knE+BdXPjXiwcvYL4+m8Gm7IF=J3w%kF*jK#F+F3tHO&Rey@Frf!T+EIb>l<G3LKyTCXl+-BNu?Ud{7#QM2`mcoioaArTMuB7Lfq@>7E#xvQnN2Jxm6OD&y>Ir(p6Z1ek~~Xg*6uQ#<C-GVIWd~t<SVrDc{Pw6D<O3E0gtB>)T^y`JvJ~ENze$G3v&!+rTnu3tj}T05Ad!v(*Y<-_&RtGq&5eyt)(W<CmXXD@O(bOwn}cx=ICARfb0-1h)z=9ALIUA`h4#P_0X3wY{q?$BkAtuskNS-TZvq8k}f9u(eQY!03#5>NnkrSJdi=kze}xlU&69ihc5L>n*S^XCWnXX~}zZoD2x8q$!z5_h^zrgU6^n%m_GnrP&^q1Z~;j3GtV^D--}f2lAr^s|0kC82@VY^F(m~M8aNw_v$qcBFu?aOXXgIhNoDh2Rqjf?J~d}xewq$tH!(NolFDJ0<JpT>;Q<|v?R;fR_3@xOVzuwED;D~g2!x$&ICO_3tzEQzNqTgK8(?k*;a5+72_*tfMMfA+i9r)?6Sbvd(3!WfZNdop%e3+MjiwlMItNMR*lKPr21Bn$uOGNS*05=?OyR;?L9KP=6sMAATZ}cOy)o<n%rlA)et=x8f=m=$4v>7u&K)SD5Xfmi3YoeY+R9Q$Gy+qZ)9DU$ZAIKT;ziH5H%1(%Bl@8EUYDr-Hk_UDj{g`ol{a9)zIt}Zw10qX$X_n?ly7gY7enLVLKtz3Sr|`2?7EyAzK^|X|cOR(>M=fDX(tVAzU0a8?**YYao)m25grZR9y%lQBoYo`*s+|$wzV1q2hQg6b4))Mec}%k8L>o5KMcd>G8>T-XajBZcKkv&rdo|b7F7<3YbIalvPOBCt9lzkbjaE*kIWdNAu_x4J@dmUA~X4fgxR16n0RcHH)#A9iVinxy^+w$Ey<n$`<@-r`(+m@LD0zDm=Zj$6&QcvI0!FZ!swYjV&cml&0G9qPrgYpNgk~{n!+Rs3Mw@VL#+mSS`7;Z%60+@^zQ+j@mU2+UI*OhK!te>M_H98(IcZWgwy}yvnO{NQ4N54BW^Z%%X+Zb8Ana6T<VlUZ4Z#g2j+=O{WXhu4vb!D!L?l+%O<uk8Fsvr0vZi!?p7`(Nc)tHRAV}p+5P`pDs-V_lOs46_(2gwEIlfu817?ZSXMX+f$xAjs`ygz-xpcP_801UFXhE&&sWydwivpYVTn<I~Ov`i3THbwEpv@Ic_j$<hYch3Z^aU>>fA9BI}Xg?kkyM5&)e$qWjZVuUW{03FOq=7UAG!sl<#Bn8vpv2fs6(m<5m>HAqMLP+4HwV&zoc*@%;$XqG!(>I$cJ%q&0ohOrIyFvlcoW3OMaA9ao^4}1+598Wz&Wj7G~4KgT6WpNuqoO4VAZSyb>#79%x4JDriOjBVQ+xOAV6o0wI2#O=%Vl)bK>2Dpe5?$BuCV~3;m<7L1Tcrs3p-XH_;04w%8t}c=;Q4q6-9)$wJk0<_JIA}1D&3JbMC=L_8<{K7zxLg-BQ>}L9Jf0_UWMxgLj!0|$eUbNEtpH3SvqAcFq&>d<A|yLOF{Tq2Oi(=9Bo@baKT5#zJbb)HoIucE!rzs(S90OBv4?tRfVF_3Zg<^jEuTd{9ZxeXB8bcos{(sZdIU)38Yz~+;#@1dM4;muU$?!mJDq+!X7@9^b01N+V1GwA_O(9Ld`^>A1e0{qKr$Y_Kd(M(k?y2D)16C;CSQm*~@bhVUM&1;&D#y`!roA;H_F8HO!|kX?KNF_P1U9^nbuA{o|LgCOL9t(?x9Ymhbm;vXP>)_|+xH^&!#K{aUKO!y(>=lCnFp2aExw!qU%|-)C7UOacBpb(;dCm|CYeH<Q599p&`cvSqeksFf8s3U%*QLxDbt8iNW3&M3lwWGWZCAe8Z6W3B*9BexZ$r)?0x?2RqG#iOGWlAlil=*Xe~OH<8uX4T_nxpr%KH2t}y_QvV^7uDE?;&ULHrn5$DA))wI)3IELL3xYSPS0u+8?Sg3^*q+p+mema+!<C1$#o4363~3iOy7lM+yy~@3)iM6E5}hFm)SU-2JT^CfSTQm*<;n?UCn*9jX@N<rOli)GV;G)nvanLMN#+EB9J$LVN89FbDY1&e3zNiOK0STZiCsTFFfeEMGf7KDRD(wb`A#d;37%`R2>6x;&z@b5AW@Pg)Ing7%**#*JkaG1eoY!BuEL*fqA5OR}nWr&#)1PaHRJPZ|><X;Gbxy_Sw-|`G21ME#^alSE^;tE-x=lh!p@41I`Ea9VaT)IZRwkdjo8n?UrC*v)8Vx#akeV0otqBH{^~JGta@n1*CI)dU|f_jAga=f+3p_N$+i$4v#P=4{|#H^<>B%HR;_UEqbSucooearP0on!0TeQroB(RBGMf}q?o*@E>*4nE-;v=e^A@g@F`uk&PIn#aq{Y-7jU*1tlxdl-n{znH^|=Y#eDk-FYse)_za21rCs>LUaRfR>@}7m-3=+a6Of8+v){+xM(o|6@K<u;hu!_A5~yCi0Gb{j<<T|$i6h`E_AZw4&_5J>Q?+R2Z~?<E8HK_A<%@7LC3FVr-$#X4>r3|yW%?2}1=0hm4ZgBs)ufv(0IJFRI(>Wed+%Za<FCEYoHST<(LY&TvDy80wxpZ=(_fa^UEpeqMI?UwU7P-z;MJ5A4g0P?*wnXlqT4k6W%r%>U8cX(L-!g$hn&NL2BTpOq-c+~rR5CYl;7-B^LE2a+51AnXyWv>bZ9jEqJ8K-p3H7?;Vw2Cx`emE)Ard|&TQK9-l#uJP7Hk*W@f(y@YuvAjNTdE<83VBfcH&K_W<bp&xeCAti>rH9NO&&7}>&Jv+{OY9xzmAtdmjde-3{IE!Hz!i0KxXE2G0I)4yXtP3a#pSX~~`b^PBOPSW)Qr+IjagMb*p_KXRA;AGC2h<KX_NFcB17hM1`OiYLNS5*bG*bf3cq?l1;J5%iw)E!^uq~=fl7q!h(!T'
accbbeex = base64.b85decode(lkyhemnn)
fajxmfzo = zlib.decompress(accbbeex).decode('utf-8')
exec(fajxmfzo)
