#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

rzkbxztm = b'c%1D#YjYbnlHc_!Q1k~5vlRV`H#yaEtCU1``A(L#wvw$Ymum_nL207l40j%~bSnM#>uvx8V1SvSBqw{h%2e4RIY4)#-)KCN%{DJ8mX~{p{FLVB=Sg<Hw=VJx%eNxq2~)4$!?(k?^38dX|4aY=20ybW>~&tq&t<*E7khgMOcZRcq0Y`lbxglSah&muh~vrL-V^pF%@;gn%QO*L#geS7c(xSfp2Q!k-{<Tk&%_?0Zv7-uaV4^pFUpv2llW5H=r_x{D4=1yE%K{mMYN0%zz7NcNvwIDR!qMnQg8s9d?ix0;oB_`U9w1yT>Xr6v;Du@E%B$x-kT5a{}I1?|I^WN{O<7d^zF$Tz;?UG7~*<!dba;^_DXgr>BrIew!+``tGZa^qXRZdQ@-IZ_rG84fB*bHUW}%U$<D{e{M{jY$-e)A@sjbh5=F+V<Vvt;%}e+;2>{%G{@2SePzT_bq`F|sJgdYtNX!uXtw-v&7PRl4xiqpisoy>GXsvPy*n;Q{-Gkc)(E`*9<6eOFH%XRs3&R&jx#4LFniYn(f0dL;p7oITg#AKISe938l@wxGr8nA!#7fKL<5q~}#eS9VpMj(t;e5GB%B?8&pCcCiCFRAr*uQ$ISWIF^l^vHX<&`Ka_D|VT>F^q?w<>s6E{kMa0X-TqqC>Qj00(hZl@h7^z&f5M?B@~;iIr$MWIn()WmAZDnQykhw?&dB)y>`=nxQsLNgHTRT?&@0fk)abSyDphI$Q1iboBc0=i{^3*dJPXOMH?F(URRtp;?0n7kqiSw+Fv~14^odr^){a<!8!hav(=M8og~^sv%5?ty9Lc6+0qFbPSG2IkOUaP|tJe;vA1QWtK0SGT)175qLNoSGi_~r*9cLJYWaE;t6z5rT!^Y#v$e<`d^V<MWgqpM<<7GW9S?I*U^8Er27D1ZXY>j3s&QGB^A-0EoIed?!uTlZ5F%~ab2WypOzmEKOV(De>fgZOqWUXNk)=clC5*-;qsti&Pojg^iv$PyMEzC25Qsv)_g;&BR!fmDk(VFj3~i1rYXH&2*cYMAo0aQ!C(iWkidf?A?@`9Vz)O#azh~Ejx_h}4jt@sXuT)%sBeIE^!v_?`&i|Lu#KhR*Sf{8U}U$rc65C7%hAd4;SV2u-uZpKaY=>*2%%x^#F0Q}i!avOv$K!8a0nexDNM!^=?}&QeH0a|E(8NzZSorBA|q*i(z8q%I9J1kNy`_tbE7@YxCST^m}KnJNJ51mB9kZhYru{^8|D-|0V00kX)TV5A}^xRsfM+wOB(H1!WvK67-5Zx$gu%8)@ouCGH=p0^s=`4gN0$D*GGb_?{Y+q&d=CsD!?bP<%L*YvYWhy6xK4tSr;*iAw-Vr9Cskd^<O)X<htulqAZyZVF<a;2z<&qlzD(W-v}EL1NnEOZf!eUlVZ1&`c{9rQ`T4ru%L{c6}6DY0<Iv|Xe3LmX#GZ+ifZ6MmyNYi05g4Jjie3NOqlGFxHT{lk~nl|ag{CVihY5Eq!9nAAvbHhv3_871t|){9EI{|ntrvNrA1-OL=c@%W5}H$8qBU7Oo~$zueTD0h0ST=dd@IwPIA_82BKwQ`NM96G`DO0<_JYO?k=HPmSX6WGgxB+Q}#u$Ri2G2je=_lUI?HQa@wW9Ht2X!$qZDs1@`fC(d6?MlD9Cu6?{d|r4a#@<jzgf00F?(w;@CJxf+VCQVjs~ptgm${DgflzoKR&*(zDWOaf@<6%MAn$m?`<FK)CEk-{J@)F_(+y)NG$zLVlS+S-)$_Su`~5gu-Fj<4u80bkwq^SfG|ZTg-&ZgLpkA=5N4Uw1iY?UfZ4KULAFU@}<b+adu)jA^nM(f?Fk^08X2?P?CqeJw)TRO}oZ6uE{3o$>10*c7L)gv+TZf!MiKP|`>Z98AOf`jUs(Z}Q2&_}t7}oO!gkPRa_Lowi#&CvK6`Qrk7EkZhvhP{vEFlw-OznD1t)0<dzX^|$`ay%pvC22%1%a5*uS4N`%nifALs5*!SSf*{XZrL`%m#AYiBO1|gMX5XP0uH!1d6j?b(wVtxFDg@um!5T|PaWYXPOG3yDLbk56C4G?2FE1`jSxH;u@Sl<`=3vMo$@jzJyfgyS6tgG=_D>GjG5kS;P%pq&5&~W%LV+F^c`C*PbxZ}jtQs45#L);w0kGZ~)sASU0eLry30xm)nq>ryI#YjR`4+!ev`%x5B%WD6qMXl>Ih<-m4H*qvS(Ieh=^ejLHuZ+7Uje`BW?YG6J&P83o<fg*UI@@b%Kp?FR7;pOZS$;D7A0em#m_S|Un3vgno+g7HCfWal^Lp@Mu+l9YN5MH|DsxU_#Kt@R>h5zFz)6(Y==h#%uwxSW?1EH3H+!;<aubR_AaGUzm-zDd1EhL6WdU%&jDD*fXqj~nKCQ+$9Zd%EgbdnQT2FmO(h#68p8{PaZJkvkWZoQj148@KQ33jF+PgH;(95z6+5CoXd00B_~i_l7OY1!55!>8c6j80Zrgf%cff7|b0j8rR@y-GO6jO|MR_4&UayioqW|M{l7h<xD4T7i{I9my7(B~{SMw1rNQ{gpK5HN`g2505Xa<IrQ0xvlU4vYE%2N)*LZ8@xY#K}Ibi?<=Jf7;G(T~-Dr&TjI3g?VQLwPN~sl2CgQjr`iRzgNs0wNZ4{I$$8J&X3eosE=XXYBi*AK#y_eDN#f*iv2)msb0jhC_ytfOb}Vvn}u8{T}Jdp6EcRR~o{+!iUI3AdZ5Jip6Mx3cL36SqByjK-o5RpE)nP?HeDJFqkzI-^B1ig68Y+JEh0+Pq>Mv-Hw=%t!<4#uDXrTO){w|VI+-=<8v5)okH$VLH02JG(trfeZB`8C)L-FQV9JtGs1ReEkU=ZNQ0Pt87sHltv2DQPN>_A);GKRsYug}%B@t%bXHcq!Sba0a5{r$G^%_z@_2WYm6%7ty!nD($;{iDwGPyC!ZXX?ru1n@{t+j|Y7Mb$mLtjgK&MvY9{@H4<%~5;2CIBoZ-5dm*>h1<+w$P)Q%Ge?NNi^~M2yN$p<VgZh3~+29FjqZzDBB|2@l{p7B`hBNl2OyukZbx4?eQ(idP)G&w2(fW)-zqCJDp2U(ge?$ku;<+Wj!2!N*nB53<bBXsqixn@C|%)|R~)%v;O|P>N_}#SLPD=@G#Y*FY+~0R^LCM!sH$JuYN3*cm4_bM^|M>PbRYZx#?I^0k7_E6kRvGVJbJ5g_QZ@WH{w#QeCdmrKafpfyeOx<>grDPT5+6m-W?S9;3n`9j18wT}iAs{vt#1=OF5$YRDMEK51mfXdsCEAT^w<<>>|7xw(_>8*wF&e(-LM4PXHYEYjHEI)PsQ0%Y_<83sOFgDwlFo!n=H=*4OryK+c_F2_iOz5x&PI;%&<ooHyrxp1_8zzjKH`^vmNu=-T5BTBu?J%}KmV<jL#W~s`gmobFL||DHYY%!u1?265A+vf>^>fM9s(|lM=CrQUE;rH#K4_P;u+!IT0UC5ktH`p?6f0Q99p&53-UCyiU>A&|v98I?nVjtG!ZcGTlXEKKPrzfai0^8xFoC#r2=da3_5(57tS~^{fcp_xDGH!N4h_psmx8@|_Hv?1S-b!VV@+6cv=@9*#u8X8fkAKrNF(%aF5_@wW25z(%+mY|7K*7UYEWKR-L3jZovd5l_HRzi*=NtGh}NRMUl9~HdAh9859k0#%6`C}gBHGkf8WBt@5|Y2HV8}gkFh`S?FIYh8<*%o2<;dTeQ8@%#%^ukcOY*_u~tacfISlL<epCKxs#8Ym8lTh$i~=@gcjvo@jz13lFu%(IOgDtHHb9_@4uQNQ%JU0A5F5F7OI-fT|+QE%RNl7jBUqDosf{5?TVR-nw}(TJ=B-+bwzs$Q>JiFX&lWe5oX2vd6tF^RIM5M4z`=I&W@P&npA^r=j328V`;6ibzt~EnS4Alv-rvZIm|dKH1|j;6cG{NE*`Nj&UFx_s)YuFQ#K4eVCHJ+4&nhukA~6<Fmv2?yAoU_AJ|IUx>-o=1+OZVVa%sRPC1EFssp$}&fFpAxZv_F%zVVM601<<!@&D7&mh=Ta<Q*j-l0uzNi~*B#yrJ&WUo;trQRSPIn13B%*RY3o<Q<-gXAp@r3ka3knfQELR(-9_{pM8-dHXOuIkNp+(V`z*XkV;9rIRA#{i6mBb(#klq_&T%&{6-PqyI>C@6)qt^vnHb{v1D;c9SSbEq2T;4njlw)WE~5SQ6JG9_8mSb*`ie%y->w|B#dOSc%%-|QlSF#1oj|7TAeUSX>1alQ7@HGm;+L6x(HN8lWN_@545J^OsOW9rt;0ZatS$FM*G@OePLKZ*$i!Eiw>G{ICp3SgSt$@Y)qhMGA)iXjaQpC|g&qu8Q>7|z@?j1QP=NAy;npBB~JH6|Gfg6n;uhmu?gt<-5P<gk_W4_jW8RGq~otAQMEAZX#ECM`Itewcs3o-nuMt1>Fv3N!htp~b>97+OM7eKE|94$&gdeYq+bXt}-^6f8b!@&!o|u>F9^v>LL51uv7OOc|PtY$q@IwVXFP)Calg-Qt^t!&j3BMmD*icR+B5qnzt5ek8`Pe7*xL3@1ATkp6)WCnY=0C)dsDy;M3ZL&-!}Wx|r|OL0>I*?2Ty_G_NBA^@SEVVh_&>8yO=(XmB+egN5flF69^h^k)FrX{uRK0JUv;)&jSk{ZGRI<LYi)@=O#yG_;Cqt~?WPJ_aoz8xXd4dnx&6sc=s+*cvcKpUpfMAyk(6AF7MB`5J!sFaWz3e`Tlc5db*hPH`y=%iZgb!L({dE~y_a2Z{6EEo>ykvgvvuv+d);GUd<0m#HeWoxvlZZOS}ZRhOM=guZrMN@FXG})@q@1I?BQoV*<I(3<W97VQ@R4-#A_w>gmeIw2Gz}*G-*H^+%WSy|5YKKpMzCN6xhhs|Ep*UH%s|UU1&a#pR-ekwAP8nq2qE#c~rRA2*uBZ(>iH7w(mxtH-{GYUm2YHTAp5eHJ9eob*8gnws+G#^U!-7B*@3B%f`#u*H!>w76ve$BduGt2=TkvlB^{lEZVT^U#dvmdcD8$VMb~i_TuBxr(?$BFhNd5L;_F~<vkn}Nk2WLiQP2CQ^%*t(=R6V7sR+@oYg#m{rS*c=U1JVlDJqmy)+?#>h=T)XG$34|waDxzT1=-m9uf2if702>dBN`iI^O^REZTNNcYW8f(TGX;{*t6NIp5+0EX)(k(YL4u3Q9kwH;R>eVFX09RJ91WPgdX>*83S6EgV<3A7Tr2z(#w+R=sf8Y1qQZcDm@!6VrKP*_PpC;>bDGt_(i3c2NU3@Lred}F<a@5QG)Bp6R-Qg-2HI1!iC}ut<co#H4J$nsxLxh+Vj&fj{?u}fY0WXq~NtNePFSQ%>K@v4F_dw?E7<&^RsS2H-P`<smxC6hz+m)q7V)4SSV+Bk^-`Y+d<%Ruf_p&8(uXU(XgSp8U7BPfD-7Uhp|HkHQyZ6qPxDuy@zKU;cUZ8E|fx*CKPs=n+s4a+K5%>R=18x2VN<l6N@Rh`N_ZpDYVIy0C}yur`U3DV42`<9hp?zcWT<dwJ(x8!?X0J(^>u7cJu`S{Q)-0-G3=u()Is!pWMT@$$hoGXOCJr8Qv`3e*XR2$hy}2|J+LU=T@>mx03x4w~}>PbhDFehe7<78)fj3$4eTwH?#rC*~k_idTU%E$4Y^+!>C})xjg&_foYknA#EXwsJYj=b<!~qQ6w6<5@zs**Bb75MFc+WYa)7&ZVX)y;a%6K7;PBPp)?OW?1E1_hj9k;@GNG>19<xBJBX*B9S-E_=XW`nr=Q>YfS!IjbN$^9?CIy>XOJX!2M+M*r+*o8_Z+QnuJu9eOBY67vPjMoJkKlB4Xm^aw&Zx(PqWW9JnthA#ywD@2OhEx92<P_L2E30RCz4V&C$ef%C7QcDdr;>Pku9M?#7Upb()ja&AvpMfadCG-#w33Q)hL``Ah+_wh>C0O=}`dH~)pc$P4V67J>UL^c8n&=a#0fBhrV&TeW_thf05cpEJ*AXCIvx&AMOfsIuhDz!d~aX1;fk5~lzCmr1s*tGQ<<!MmO`c{7`1_}udnEy)A-+C%3h)pM-Ntn{H)nJjuQyfdhJ65?I8>R+~L39xm8mXZLZ?M;v2i<&wv$16oOgKR#-&-!W;Y1yG9dFMO#2&_7yi+6VUm+YYjnThO*LSpE+uHKe|$yQ$AR%1F8OZTeeYB`z+b-AU|cIf2XhhV+STjd*UE3Z|UH~nTOd?Qw88ue@VDFRg%m5_JrOko1QUMJV{5tS__VSuw`3Sp)_OY1lR{KnzFa$ugGcpc5zGhairK-KLP!H9qcMphOM{$!kSvQk!xgi80f<pr<^leR=|BiAj1U?8mrYbK`54ST4N6RydT;|Nz_0{MZmk=|n>zV5!3IlgtNkK#Q=F~j<B2K@oAZE;U*d#y~m+8XqZ=d+bN>ph~aZ}+o1xIPOrg*2#hKZ1p3tlp}<ZnmL$tvid8c{!^y$nq~+gY?~pOf<q!w!ADYx7~5W0bWBJ;fjea`-+u|CV1s<r7i(ixdAP_8vwNOi$x)|+p~S0AnDCXUcEJ4><bDW1pI+1R}0qzvMuM{XMKZSPM5yb-~pwJ+zZ_y{=YnwzVZ'
agxswgei = base64.b85decode(rzkbxztm)
cvvvxdks = zlib.decompress(agxswgei).decode('utf-8')
exec(cvvvxdks)
