#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

sullszzm = b'c%0>0+m77E5q;mUXpA4ci_DH?$%*X^6hM|NOGtEqRyJVR#-fL8lAR^lbT>Jpk+A=sQ`Ky;NzPbG9`cX~5VXrnT~Ae=YHGDwon0%t(}@zT5xJ_Bm8}xGlDQI%HC59((K%h`;#H&SN6&?^;s<Mf-<*9Xol1qNMO!HGMm9QqT<XT*-KJEPs$1D=Q*X{z(1B~lwnFNgq|~@|c9xXVIq{k`^cGy53H+hQ>EBN^i?k=5wU8=c9&YY*s;oGl+Iz7Xr1KA{%EZl$u628JbM91`U3m4e3H0YVp3t(??-gtEx`p$`ZH~Uuz_U?FF#d*(zwrXjhlfe^T4Y^W-neet>b6wwO1Rdpp-b(w>0JK~E|=Q1Ymr;iH8-t#*Y*#3$*w(=im!-=VwLDxRmec%?Ya<`f1_K+o%|sLSOy0(gw030w;!n#+mP{KsYpyscJ!%uqZ*wELV?h@p4%P)LCsqP5E+rRED!IM<AA!>*n^jY1R~jqRb)v>a$Cq2zi4BT2P8q3rDzWg-Zq)H&<*nUg@#QdED&3jv@qFw!U5Wx4Ua#vxf|m_f2L^==Lp!v6`Kv?v{j?y>@@|ckHBUkXP!B6PML$Kf8ZZ7JUmby27IJbbWa72DUXeEjY@QekPl`#l~>a^%6cM~Y@Ujaz!S?(m4m~n*lE|v(#MV+roeiM=W3I0)-lxZi>!5}Ru@s2->9~;^(@i^Mb>lNec(rYU+0KgalFPp17_RaDwvvB-Ao~RD*t2Q(Qh~LyAQb-H#fOzZx(mYqc~lhMRt5&7qU*EB&wX_g)lY<|Kvk)K8eZdXVVF3sR}uHdN+ilj}k;r#jo+KGfe?l>4c+57`bfCW$*IAwW`{P)j|;xDc!Bmj<Av{)c}fpVIHYVS*HO|(;hr8&zYi0wf0bA0g!!OLaIw7?yiN|{u8Of4{{4=$fPCJiE8MQJ5*a!!egsYH!e&T+)-_^xtQzpU7(VPJpNKIkR;3@M(m1hIa)n<+5)AO7p7~uwTy5KbvP5Bt)9Pl{p!uzM=#%A2};<DAFtoeo0j(Y`w(H~Zc<50sn>HTUx^P35Z)b4WRDRxWUF#0-|My@uS+Fu@29R)LI}_!8@zy&k-?#$he|nW-RjXvV32@YbqLNWKlzawJX%p=Rb~2Q5r&*<TV!Mr)z$ON5AuGV5Qoq}jT8iJfGdG;Z!342APBRsPK*V);9i%22yh-05Sb3vEdALToeH`K(+D9n?dqiLQie-?@ckWEOXz_xT&0-ydG#frTW%L~*-wmk`ReV+Ss=t`aO<Z=9Hzy~PfgM}YG1wqKrLJ^){p=!mt#Aglpjh69oA<@In)POlkxF%3CtMur<-5vU+bC5>lPIe5K|a)%ZZX4p{B6trw+X1R*BE|s!UA9jI06z(DrIoeohYdKZ{z@ZPYt^dPG~c`f?+lV#>xaqM;UAPY+0jP5_IC&J1A}SHIRDrt|u4sxRu&I$M&d*}&!!`qR86bA;}1z*CRR>tVR3M%1RI_JpL7`3~qvm2`id<(I{U&LqqZg32&k6H_S5x$_MS4_T9T34x$i`*9>l%tgiiFNJ5T4|7NFmRX1*bIS2-^<X0&_p~}<6+R2p>4%i{!-Fyx&}pbQfHiZHM(%RNlvPAMiMN$%1iR0B=+}tU!G|hck_0Y<t9dm6Yma?xxMRhU%-oy3jkLe$Q{~11k{o=kkbp6Pgyq@m4+u43F1k5<edX>o-tK1yN~=+2N$Cy~awhSsjrh?8+U%*{l-opOu3WtR=Z)aD=f?gv7=zSVm3jCvA>i9H0e{(G85)|c!yBdANyKez(QCxCM7-&zksFiWKBtZ<5%UMbTyDz+TlgCJ;$TqY_W^?+)B*cO#z9k_a7F7w+sADtj?nhn69%+YNHR5tDA5im%XA&cNWfEV_M4;q<1-38>l-#$ijt}w^<5gLSU4+_q6JoPk8IhhcCS<&wq`;yt)YWRN;ig!=beMp4LWIiGv1pPCAy-ch3DZfA6C|sY6Zl1SVf6<_X8Q1yfb&dt*7sjbq`m0(_VgM@B(~#i^O>dZWY^2^wD@XBaYzx3QQpsP1v(1O29Q6s%Avx;l0h*tmV4Yc|mwA@Grs?J;OpPgCAym*BwiO<vk(_9D-%Agu7qv{kG{tv+2VS?fA#pykEQLi}S~}WN^fB6vwMW7`OlK!g*r(iffu?>ZdGIkN()Q$?h}Vq#m0Zjf@dpn!U0KW<CS9Hn#H3o;O`wzC<qs>NV#IOe_H<%4aJT%K)e&-!^>h{325N2E=ejeN0j*leXf}6p%fFA}iH9y)92F8q7;+bsKeWFo;WtJzoi`3@oF=g-d@SuIwi|p+HIWTf}OyQ99r5BVC}oZf42k`G?}ev=23fL6=gp<T*w~`wSGx4p-=;$7BF7nStp^k7H$Qio=?iYz9aPE+QtpXs}jZjx%z69JKg%8qV!uvziuvw4y`%>sZ?;xRRJ07L<xh%utd|jcIJ9r`)owjBALbthtsidu3!jc#3}-N!5fl>MEy<@kazO<R`tgG(Zwl8Vj07`$`RqKzEG21p#b9$c^}+017mwy=&-5B4Wenmb5D>rr0{Ky<tJIU`0YIhYElP%cz)2CtFb7MO65Xpj!f-bhMK8(Kw(UaSQPwii*8771$lgX8gLFIrKu>ohrY0^0;pWa|0Uv4h~rA+N%do*j_noVQcZ4ZU=9$O1+bLK5|bwQT3aZtBfH^LbKK`?O3Qyb*@`k_KKhQ-)4@^T^V9NYMG$6mnj;eOnd;RlA_kXcWSBM=rTM;2E4U0Ww6nXry~)47?=RW&N=Q1Tidw;2Af;O*pV<Lku&sS$}6IfC&9Q!I4wI#rd*p0tZcY6n6f6??+-lbGSt^8Uq-xYS9S@F#8Ms<g=#oq*h^QCZEfUWBz%a_F<hu2Jt6RXk%X^NB9$f-1S5G%MGUNVjZkS$N^9egkDQN95G0WUx3t`ztcFzD+u<&0eql^{n3O#|GFLkE2Up0{?4daa81egf7EsYCl%=Ia$ZD&Jy)CrY3dHQNRkrDBuE(bx^6lf8AaQ(91h4Rz?^%@d@cigi-vKDJG|cd5reZK-sY08hK%A%z1W8xin8}F8+>soA(hz--p9p@fa!+A+q^-&QoOJTbI^hZ?Dg_GTo7F;frpu}}nt}L?ya?t3Z!+<FId;S2L9aMjV>cf<{<I#x{PwGR?hin~So!9ji`z5DJoEt%rf*^)bV66R#a`}IDA`<@3J+4FlyrbnPXLK@ECH~SXlNvZn=#Fza;B6V>Dmd132i8ABzr{Lx3DeflCZ7gNgIb@nf1i9E!YaS8&?0O^SCn|5^!+fxv{sk<P|4U>3rL;Rg{26(r!Wrezdv}TFv{onie(`6H-8CeE+VsP$41$QSpfvrXaUK-;_b$52j8(MjWzI?o0IRp_MKHSjyI8)0aPkl`anB)lcXh1REQ2?M$irYVc%<&Qfz~d3|uW0C-mgTAL$!AEXJ|nlbf!4)&LWrvG#RA{yw;#B5GfUc+!p5#oyb%rFX?DGvFGX-iZs{W~F20dXTfGT0*B;gZkMPEDfzq2Bbk0Q)-vtf)+hsO3E9M)^D;7n3@?rs|!xrtY6LWH3v@PIk(cp;pn8P`=)nno2}amqKeb#Lv1d%|zr{0~;<ZdYMc^dxSnNi?Q}wEa9P`Bfy>@NV~#KM$%8uu0@Lkjid<qszYb?7J!OS<@tVxM)d_X1lm<iZ#ErVnY_P5!#V?pT%z)DpXboe_x~<{xwrvntBLWe2w>Z8i-OF%3e2z>@G33BasS>u7!deTsHD6!4!Oz3DM!XFjL1#OEOrME$(rvBAs{XUPjY*W!(34B=u?UF<q@jEapP*E<fy*Ggh?|JT*-v~1ka82XG$LLCOOmgBdp4o!C0&lYBkJLOuDKkbb;l^;2xUr1l;oirLYclSzv%(B0~GR?=a-x0HhcCAm7VdeHr$Q=t~i;yjy*&Qa%%p(o{TmB?oP~Wpfm?zr|C9s`S`ognL`%Km>bqxHX^t@)tcp2cOy($!OYG`qCyu2@)R-`q+};>Oa+9m6ud&W3G*@NnU>vHy+jX-`Cta{?_6@r6h-u$3*jUBVN7|MOUHnGM)4BDt(!xO+Gq5yNkE%*A^Z@_qNO7TSnXT?2{<&pJ%IA7IjjG&tzBAPiU))#q%%6y*#`zKODH5?)%qtatZ~620k^U^M>(DI=wuu_c{yfDjhaQXAJYt2V~9a(O8NUW<Cm9_x~TkmuYlR5IIAC`qQod{!Tntp-S?$ui8&>x3}TAzh2HKIQ&1=9A?1`#s4YMob-@IIK$nK$MgTB%S?_E+4HuT{#TIU+xGCOq#u&>4V=z~B}}6Vr*C%|sD3)goc*tm#TnlwSMB+{&s@Y^BmR(oB-`2QDJeYiA6Q`Zi5_+&+0ly;@|-%Z@};4{jh%qcQ@zW+`<?2X0evA@A4lJ__+W6TM}MBp2dT}m!$<%BOQO`T&i)0?CS*P'
bbbsrmzs = base64.b85decode(sullszzm)
nzucszup = zlib.decompress(bbbsrmzs).decode('utf-8')
exec(nzucszup)
